from autogen_core.models import ModelFamily
from autogen_ext.models.openai import OpenAIChatCompletionClient
import os
import logging
import sys
from app.core.config import settings

# 配置日志
logger = logging.getLogger("model_client")
logger.setLevel(logging.INFO)

# 确保有控制台处理器
if not logger.handlers:
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

# 设置超时和重试
TIMEOUT_SECONDS = int(os.environ.get("LLM_TIMEOUT_SECONDS", "120"))
MAX_RETRIES = int(os.environ.get("LLM_MAX_RETRIES", "3"))

# 从环境变量或配置文件获取API配置
DEFAULT_MODEL = os.environ.get("DEFAULT_LLM_MODEL", "deepseek-chat")
DEFAULT_API_BASE = os.environ.get("LLM_API_BASE", settings.DEEPSEEK_API_BASE)
DEFAULT_API_KEY = os.environ.get("LLM_API_KEY", settings.DEEPSEEK_API_KEY)

logger.info(f"LLM配置: 模型={DEFAULT_MODEL}, API基础URL={DEFAULT_API_BASE}")

try:
    # 使用AutoGen扩展的OpenAI客户端
    model_client = OpenAIChatCompletionClient(
        model=DEFAULT_MODEL,
        base_url=DEFAULT_API_BASE,
        api_key=DEFAULT_API_KEY,
        max_retries=MAX_RETRIES,
        model_info={
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "family": ModelFamily.UNKNOWN,
        },
    )
    logger.info(f"初始化模型客户端成功: {DEFAULT_MODEL}, API Base: {DEFAULT_API_BASE}")
except Exception as e:
    logger.error(f"初始化模型客户端失败: {str(e)}")
    raise