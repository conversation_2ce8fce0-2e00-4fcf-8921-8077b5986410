import json
import logging
import time
from dataclasses import dataclass
from typing import Callable, Optional, Awaitable, Any

# 导入基础模块
from pydantic import BaseModel, Field
import asyncio
from dataclasses import dataclass

# 自定义类型和函数
class RoutedAgent:
    """路由智能体基类"""
    def __init__(self, name):
        self.id = AgentId(name)
        self.id.key = name
        self.id.type = name

    async def publish_message(self, message, topic_id):
        """发布消息"""
        print(f"[{self.id.key}] 发布消息: {message.source} - {message.content[:50]}...")

class AgentId:
    """智能体ID"""
    def __init__(self, key):
        self.key = key
        self.type = key

class TopicId:
    """主题ID"""
    def __init__(self, type, source=None):
        self.type = type
        self.source = source

class DefaultTopicId:
    """默认主题ID"""
    def __init__(self, type):
        self.type = type

class MessageContext:
    """消息上下文"""
    pass

class ClosureContext:
    """闭包上下文"""
    pass

class CancellationToken:
    """取消令牌"""
    pass

class SingleThreadedAgentRuntime:
    """单线程智能体运行时"""
    def __init__(self):
        self.agents = {}

    def start(self):
        """启动运行时"""
        pass

    async def publish_message(self, message, topic_id):
        """发布消息"""
        pass

    async def stop_when_idle(self):
        """等待所有任务完成"""
        pass

    async def close(self):
        """关闭运行时"""
        pass

class TypeSubscription:
    """类型订阅"""
    def __init__(self, topic_type, agent_type):
        self.topic_type = topic_type
        self.agent_type = agent_type

class ClosureAgent:
    """闭包智能体"""
    @staticmethod
    async def register_closure(runtime, name, callback, subscriptions):
        """注册闭包"""
        pass

class ListMemory:
    """列表内存"""
    async def add(self, content):
        """添加内容"""
        pass

class MemoryContent:
    """内存内容"""
    def __init__(self, content, mime_type):
        self.content = content
        self.mime_type = mime_type

class MemoryMimeType:
    """内存MIME类型"""
    TEXT = "text/plain"
    JSON = "application/json"

# 装饰器
def type_subscription(topic_type):
    """类型订阅装饰器"""
    def decorator(cls):
        return cls
    return decorator

def message_handler(func):
    """消息处理器装饰器"""
    return func

# 模拟AssistantAgent
class AssistantAgent:
    """助手智能体"""
    def __init__(self, name, model_client, system_message, model_client_stream=False):
        self.name = name
        self.model_client = model_client
        self.system_message = system_message
        self.model_client_stream = model_client_stream

    async def run(self, task):
        """运行任务"""
        # 构建完整的提示词，包含系统消息和任务
        full_prompt = f"{self.system_message}\n\n{task}"
        return await self.model_client.run(task=full_prompt)

# 导入项目相关模块
from app.schemas.requirement import RequirementCreate
from app.agents.llms import model_client
from app.agents.utils import extract_text_from_llm

# 配置日志
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 定义主题类型（用于智能体之间的消息传递）
requirement_acquisition_topic_type = "requirement_acquisition"  # 需求获取主题
requirement_analysis_topic_type = "requirement_analysis"        # 需求分析主题
requirement_validation_topic_type = "requirement_validation"    # 需求验证主题
requirement_output_topic_type = "requirement_output"            # 需求输出主题
requirement_database_topic_type = "requirement_database"        # 需求数据库主题
markdown_acquisition_topic_type = "markdown_acquisition"        # Markdown获取主题

task_result_topic_type = "collect_result"                       # 结果收集主题


class RequirementList(BaseModel):
    """需求列表模型，用于存储多个需求项"""
    requirements: list[RequirementCreate] = Field(..., description="业务需求列表")


class RequirementFilesMessage(BaseModel):
    """需求文件消息模型，用于传递需求文件信息"""
    user_id: str = Field(..., description="用户ID")
    files: list[str] = Field(..., description="需求文件路径列表")
    content: str = Field(..., description="用户输入内容")
    task: str = Field(default="Analyze requirement document", description="任务描述")


class ResponseMessage(BaseModel):
    """响应消息模型，用于向前端发送消息"""
    source: str  # 消息来源
    content: str  # 消息内容
    is_final: bool = False  # 是否为最终消息


@dataclass
class RequirementMessage:
    """需求消息数据类，用于智能体之间传递需求信息"""
    source: str  # 消息来源
    content: Any  # 消息内容


@type_subscription(topic_type=markdown_acquisition_topic_type)
class MarkdownParserAgent(RoutedAgent):
    """
    Markdown解析智能体

    负责解析Markdown格式的需求文档，提取结构化信息。
    这个智能体是需求分析流程的可选步骤，可以处理Markdown格式的需求文档。
    """
    def __init__(self):
        super().__init__("markdown parser agent")
        logger.info("Markdown解析智能体初始化完成")

    @message_handler
    async def handle_message(self, message: RequirementFilesMessage, _ctx: MessageContext) -> None:
        """
        处理需求文件消息

        Args:
            message: 需求文件消息，包含文件路径和用户输入
            _ctx: 消息上下文
        """
        logger.info(f"Markdown解析智能体收到消息: 用户ID={message.user_id}, 文件数量={len(message.files)}")

        # 发送状态消息到前端
        await self.publish_message(
            ResponseMessage(source="Markdown Parser Agent", content="开始解析Markdown文档..."),
            topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

        try:
            # 处理Markdown文件
            documents = []
            for file_path in message.files:
                if file_path.endswith('.md'):
                    logger.info(f"处理Markdown文件: {file_path}")
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            documents.append(content)
                            logger.info(f"成功读取Markdown文件: {file_path}, 长度: {len(content)} 字符")
                    except Exception as e:
                        logger.error(f"读取Markdown文件失败: {str(e)}", exc_info=True)
                        await self.publish_message(
                            ResponseMessage(source="Markdown Parser Agent", content=f"读取Markdown文件失败: {str(e)}"),
                            topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

            # 如果没有找到Markdown文件，发送消息并返回
            if not documents:
                logger.warning("没有找到Markdown文件")
                await self.publish_message(
                    ResponseMessage(source="Markdown Parser Agent", content="没有找到Markdown文件，跳过Markdown解析步骤"),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                return

            # 将文档内容发送到需求获取智能体
            combined_content = "\n\n---\n\n".join(documents)
            logger.info(f"发送Markdown内容到需求获取智能体，总长度: {len(combined_content)} 字符")

            await self.publish_message(
                ResponseMessage(source="Markdown Parser Agent", content="Markdown解析完成，开始需求获取"),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

            await self.publish_message(
                RequirementMessage(source=self.id.type, content=combined_content),
                topic_id=TopicId(requirement_acquisition_topic_type, source=self.id.key))

        except Exception as e:
            error_msg = f"Markdown解析错误: {str(e)}"
            logger.error(error_msg, exc_info=True)
            await self.publish_message(
                ResponseMessage(source="Markdown Parser Agent", content=error_msg),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))


@type_subscription(topic_type=requirement_acquisition_topic_type)
class RequirementAcquisitionAgent(RoutedAgent):
    """
    需求获取智能体

    负责从文档中提取需求信息，是整个需求分析流程的第一步。
    该智能体会读取用户上传的文件，使用LLM提取文本内容，然后分析文档提取关键需求信息。
    """
    def __init__(self, input_func=None):
        super().__init__("requirement acquisition agent")
        self.input_func = input_func
        logger.info("需求获取智能体初始化完成")

    @message_handler
    async def handle_message(self, message: RequirementFilesMessage, _ctx: MessageContext) -> None:
        """
        处理需求文件消息

        Args:
            message: 需求文件消息，包含文件路径和用户输入
            _ctx: 消息上下文
        """
        logger.info(f"需求获取智能体收到消息: 用户ID={message.user_id}, 文件数量={len(message.files)}")

        # 记录用户输入的内容，用于调试
        if message.content:
            logger.info(f"用户输入的需求内容长度: {len(message.content)} 字符")
            logger.info(f"用户输入的需求内容前1024个字符: {message.content[:1024]}")
        else:
            logger.warning("用户没有输入需求内容")

        # 发送到前端，通知用户已收到指令
        await self.publish_message(ResponseMessage(source="user", content=f"收到用户指令，准备开始需求分析"),
                                   topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

        try:
            # 从文件中读取文档内容
            logger.info(f"开始从文件中提取文本内容，文件列表: {message.files}")
            doc_content = await self.get_document_from_llm_files(message.files)
            logger.info(f"文件内容提取完成，内容长度: {len(doc_content)} 字符")

            # 发送处理状态到前端
            await self.publish_message(
                ResponseMessage(source="Document Parser Agent", content="文件解析完成，开始深入文档分析"),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

            # 创建需求获取智能体
            logger.info("创建需求获取LLM智能体")
            acquisition_agent = AssistantAgent(
                name="requirement_acquisition_agent",
                model_client=model_client,
                system_message="""
                你是一位专业的软件需求文档分析师，专长于从原始需求文档中提取关键信息以支持后续的软件测试活动。
                请仔细阅读并理解提供的需求文档内容（可能包含文本、图表、流程图信息），然后进行整理和摘要。

                重点提取和归纳以下信息:
                1.  **主要功能需求**: 清晰描述系统应具备的核心功能。
                2.  **非功能性需求**: 如性能指标、安全性要求、可用性、兼容性等。
                3.  **业务背景与目标**: 解释该需求的业务价值和要解决的问题。
                4.  **用户角色与关键使用场景**: 识别不同的用户类型及其典型交互流程。
                5.  **核心术语与概念定义**: 列出并解释文档中的关键名词或特殊概念。
                6.  **数据需求**: 涉及的关键数据结构、输入/输出数据格式等。
                7.  **依赖关系与约束**: 识别与其他系统/模块的依赖或技术/环境限制。
                8.  **潜在歧义与待确认点**: 标记出文档中描述不清、可能存在多种解释或需要进一步澄清的部分。

                请以结构化、层次清晰的 Markdown 格式输出你的分析摘要。确保信息准确、简洁，为后续生成详细的测试需求奠定基础。

                """,
                model_client_stream=True,
            )

            # 创建用户代理智能体 - 注释掉，因为当前版本的AutoGen不需要
            # user_proxy = UserProxyAgent(
            #     name="user_proxy",
            #     # 移除所有不兼容的参数
            #     # human_input_mode="NEVER",
            #     # max_consecutive_auto_reply=0,
            #     # code_execution_config={"use_docker": False},
            #     # system_message="你是一个用户代理智能体。你将收到一个文档并帮助分析它。",
            # )

            # 设置获取记忆
            acquisition_memory = ListMemory()

            # 设置获取智能体的任务
            task = ""

            # 优先使用用户输入的内容
            if message.content and message.content.strip():
                logger.info(f"用户提供了需求内容: {message.content[:1024]}...")
                # 在终端打印完整的需求内容，以便确认是否正确获取了需求
                print("\n" + "="*80)
                print("【需求获取智能体获取到的需求内容】")
                print(message.content)
                print("="*80 + "\n")

                task = f"请分析以下用户输入的需求内容（这是新的输入内容，请不要使用缓存或之前的分析结果）：\n\n{message.content}\n\n"

                # 如果有文档内容，作为补充
                if doc_content.strip():
                    task += f"补充文档内容：\n{doc_content}"
            else:
                # 如果用户没有输入内容，使用文档内容
                logger.info("用户没有提供需求内容，使用文档内容")
                print("\n" + "="*80)
                print("【警告】用户没有提供需求内容，使用文档内容")
                print("="*80 + "\n")
                task = f"请分析以下文档并提取关键需求（注意：这是新的输入内容，请不要使用缓存或之前的分析结果）：\n\n{doc_content}"

            # 添加强调，确保使用当前输入
            task += "\n\n【重要提示】：这是全新的需求内容，请基于此内容进行分析，不要使用之前的分析结果或缓存数据。"

            # 执行任务
            # update_count = 0  # 不再需要
            acquisition_content = ""

            # 流式输出模型结果
            logger.info("开始执行需求获取任务")
            # 移除stream参数，因为当前版本的AutoGen不支持
            # stream = acquisition_agent.run(task=task, stream=True)
            result = await acquisition_agent.run(task=task)

            # 处理结果
            logger.info(f"需求获取智能体返回结果类型: {type(result)}")

            # 处理TaskResult对象
            if hasattr(result, 'response') and hasattr(result.response, 'content'):
                # 从TaskResult.response中提取内容
                acquisition_content = result.response.content
                logger.info(f"从TaskResult.response提取内容成功，长度: {len(acquisition_content)}")

                # 发送结果到前端
                await self.publish_message(
                    ResponseMessage(source="Document Parser Agent", content="正在处理需求获取结果..."),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

                # 保存需求获取记录
                await acquisition_memory.add(
                    MemoryContent(content=acquisition_content, mime_type=MemoryMimeType.TEXT))
            elif hasattr(result, 'messages') and result.messages:
                # 从TaskResult中提取最后一条消息的内容
                last_message = result.messages[-1]
                if hasattr(last_message, 'content'):
                    acquisition_content = last_message.content
                    logger.info(f"从TaskResult.messages提取内容成功，长度: {len(acquisition_content)}")

                    # 发送结果到前端
                    await self.publish_message(
                        ResponseMessage(source="Document Parser Agent", content="正在处理需求获取结果..."),
                        topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

                    # 保存需求获取记录
                    await acquisition_memory.add(
                        MemoryContent(content=acquisition_content, mime_type=MemoryMimeType.TEXT))
                else:
                    logger.error("TaskResult中的消息没有content属性")
                    await self.publish_message(
                        ResponseMessage(source="Document Parser Agent", content="错误：TaskResult中的消息没有content属性"),
                        topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                    return
            elif isinstance(result, str):
                # 直接处理字符串结果
                acquisition_content = result
                logger.info(f"收到需求获取智能体的响应，长度: {len(acquisition_content)}")

                # 发送结果到前端
                await self.publish_message(
                    ResponseMessage(source="Document Parser Agent", content="正在处理需求获取结果..."),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

                # 保存需求获取记录
                await acquisition_memory.add(
                    MemoryContent(content=acquisition_content, mime_type=MemoryMimeType.TEXT))
            else:
                # 尝试转换为字符串
                try:
                    # 打印对象的所有属性，帮助调试
                    logger.info(f"TaskResult对象属性: {dir(result)}")

                    # 尝试获取content属性
                    if hasattr(result, 'content'):
                        acquisition_content = result.content
                    else:
                        # 转换为字符串
                        acquisition_content = str(result)

                    logger.info(f"将结果转换为字符串，长度: {len(acquisition_content)}")

                    # 发送结果到前端
                    await self.publish_message(
                        ResponseMessage(source="Document Parser Agent", content="正在处理需求获取结果..."),
                        topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

                    # 保存需求获取记录
                    await acquisition_memory.add(
                        MemoryContent(content=acquisition_content, mime_type=MemoryMimeType.TEXT))
                except Exception as e:
                    logger.error(f"无法处理需求获取智能体返回的结果类型: {type(result)}, 错误: {str(e)}")
                    await self.publish_message(
                        ResponseMessage(source="Document Parser Agent", content=f"错误：无法处理需求获取智能体返回的结果类型: {type(result)}"),
                        topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                    return

            # 检查获取的内容是否为空
            if not acquisition_content.strip():
                logger.error("需求获取智能体返回的内容为空")
                await self.publish_message(
                    ResponseMessage(source="Document Parser Agent", content="错误：需求获取智能体未返回任何内容，请检查文档内容或重试"),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                return

            # 发送到下一个智能体
            logger.info("文档分析完成，发送到需求分析智能体")
            await self.publish_message(
                ResponseMessage(source="Document Parser Agent", content="文档分析完成，开始需求分析"),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

            await self.publish_message(
                RequirementMessage(source=self.id.type, content=acquisition_content),
                topic_id=TopicId(requirement_analysis_topic_type, source=self.id.key))

        except Exception as e:
            error_msg = f"文档分析错误: {str(e)}"
            logger.error(error_msg, exc_info=True)
            await self.publish_message(
                ResponseMessage(source="Document Parser Agent", content=error_msg),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

    async def get_document_from_files(self, files: list[str]) -> str:
        """
        获取文件内容

        Args:
            files: 文件路径列表

        Returns:
            文件内容字符串

        Raises:
            Exception: 文件读取失败时抛出异常
        """
        try:
            content = ""
            for file in files:
                with open(file, 'r', encoding='utf-8') as f:
                    content += f.read() + "\n\n--------------\n\n"
            return content
        except Exception as e:
            logger.error(f"文件读取失败: {str(e)}", exc_info=True)
            raise Exception(f"文件读取失败: {str(e)}")

    async def get_document_from_llm_files(self, files: list[str]) -> str:
        """
        获取文件内容，支持图片、流程图、表格等

        Args:
            files: 文件路径列表

        Returns:
            提取的文本内容
        """
        extract_contents = ""
        for file in files:
            logger.info(f"从文件提取内容: {file}")
            try:
                contents = extract_text_from_llm(file)
                extract_contents += contents + "\n\n--------------\n\n"
                logger.info(f"文件 {file} 内容提取成功，长度: {len(contents)} 字符")
            except Exception as e:
                logger.error(f"文件 {file} 内容提取失败: {str(e)}", exc_info=True)
                extract_contents += f"[文件 {file} 内容提取失败: {str(e)}]\n\n--------------\n\n"
        return extract_contents


@type_subscription(topic_type=requirement_analysis_topic_type)
class RequirementAnalysisAgent(RoutedAgent):
    """
    需求分析智能体

    负责从需求文档中提取测试相关信息，包括需求描述、测试方法、充分性要求和通过准则。
    该智能体是需求分析流程的第二步，接收来自需求获取智能体的输出。
    """
    def __init__(self):
        super().__init__("requirement analysis agent")
        logger.info("需求分析智能体初始化完成")
        self._prompt = """
        ## 角色定位
        你是一位资深需求分析师，擅长将模糊需求转化为详细可执行的需求规格。请严格遵循以下分析流程：

        ## 分析要求
        1. **需求拆解**：
           - 将需求分解为最小可交付的功能单元
           - 每个功能点必须包含：
             - 功能描述
             - 输入/输出定义
             - 业务规则（至少3条）
             - 成功/失败条件

        2. **场景分析**：
           - 识别至少3个典型用户场景
           - 包含正常流程和异常分支流程
           - 使用Given-When-Then格式描述场景

        3. **验收标准**：
           - 每个功能点需包含：
             - 功能正确性验证标准
             - 性能指标（响应时间<500ms）
             - 安全要求（如权限控制）

        4. **非功能需求**：
           - 系统可用性（99.9% SLA）
           - 兼容性要求（浏览器/设备）
           - 数据持久化要求

        ## 输出格式示例
        ```markdown
        # 需求细化文档

        ## 功能需求
        ### [功能名称]
        **描述**:
        - 输入:
        - 输出:
        - 业务规则:
          1. 规则1
          2. 规则2

        **验收标准**:
        - [ ] 标准1（可测量）
        - [ ] 标准2

        ## 用户场景
        ### 场景1: [场景名称]
        - **Given**: 前提条件
        - **When**: 操作步骤
        - **Then**: 预期结果

        ## 非功能需求
        - 安全性: HTTPS传输，RBAC权限控制
        - 性能: API响应时间≤300ms
        ```
        """

    @message_handler
    async def handle_message(self, message: RequirementMessage, _ctx: MessageContext) -> None:
        """
        处理需求消息

        Args:
            message: 需求消息，包含从需求获取智能体传来的内容
            _ctx: 消息上下文
        """
        logger.info(f"需求分析智能体收到消息，内容长度: {len(message.content)} 字符")

        # 创建需求分析智能体
        logger.info("创建需求分析LLM智能体")
        # 创建分析智能体
        logger.info("创建需求分析智能体")
        try:
            analysis_agent = AssistantAgent(
                name="requirement_analysis_agent",
                model_client=model_client,
                system_message=self._prompt,
                model_client_stream=True
            )
            logger.info("需求分析智能体创建成功")
        except Exception as e:
            logger.error(f"创建需求分析智能体失败: {str(e)}", exc_info=True)
            raise

        # 注意：model_config参数不被支持，需要直接配置model_client
        # 可以在model_client初始化时设置这些参数

        # 发送状态消息到前端
        await self.publish_message(
            ResponseMessage(source="需求分析智能体", content="开始提取需求信息...\n\n"),
            topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

        # 构建任务描述
        task = f"请分析以下需求文档，提取需求描述（注意：这是新的输入内容，请不要使用缓存或之前的分析结果）：\n\n{message.content}\n\n【重要提示】：这是全新的需求内容，请基于此内容进行分析，不要使用之前的分析结果或缓存数据。"
        logger.info("构建需求分析任务完成")

        # 在终端打印完整的需求内容，以便确认是否正确获取了需求
        print("\n" + "="*80)
        print("【需求分析智能体获取到的需求内容】")
        print(message.content[:1024] + "..." if len(message.content) > 1024 else message.content)
        print("="*80 + "\n")

        # 检查内容是否为空
        if not message.content or not message.content.strip():
            logger.warning("需求内容为空，无法进行分析")
            print("\n" + "="*80)
            print("【警告】需求内容为空，无法进行分析")
            print("="*80 + "\n")
            await self.publish_message(
                ResponseMessage(source="Requirement Analysis Agent", content="错误：需求内容为空，无法进行分析"),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
            return

        try:
            # 执行任务
            analysis_content = ""
            logger.info("开始执行需求分析任务")
            logger.info(f"任务内容长度: {len(task)} 字符")
            logger.info(f"任务内容前100个字符: {task[:100]}...")

            # 记录开始时间
            start_time = time.time()
            logger.info(f"需求分析开始时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}")

            # 移除stream参数，因为当前版本的AutoGen不支持
            # stream = analysis_agent.run(task=task, stream=True)
            logger.info("调用analysis_agent.run()...")
            try:
                result = await analysis_agent.run(task=task)
                logger.info(f"analysis_agent.run()调用成功，结果类型: {type(result)}")
            except Exception as e:
                logger.error(f"analysis_agent.run()调用失败: {str(e)}", exc_info=True)
                # 返回错误信息
                return f"需求分析失败: {str(e)}"

            # 记录结束时间
            end_time = time.time()
            logger.info(f"需求分析结束时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(end_time))}")
            logger.info(f"需求分析耗时: {end_time - start_time:.2f} 秒")

            # 处理结果
            logger.info(f"需求分析智能体返回结果类型: {type(result)}")

            # 处理TaskResult对象
            if hasattr(result, 'response') and hasattr(result.response, 'content'):
                # 从TaskResult.response中提取内容
                analysis_content = result.response.content
                logger.info(f"从TaskResult.response提取内容成功，长度: {len(analysis_content)}")

                # 发送结果到前端
                await self.publish_message(
                    ResponseMessage(source="Requirement Analysis Agent", content="正在处理需求分析结果..."),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
            elif hasattr(result, 'messages') and result.messages:
                # 从TaskResult中提取最后一条消息的内容
                last_message = result.messages[-1]
                if hasattr(last_message, 'content'):
                    analysis_content = last_message.content
                    logger.info(f"从TaskResult.messages提取内容成功，长度: {len(analysis_content)}")
            elif isinstance(result, str):
                # 直接处理字符串结果
                analysis_content = result
                logger.info(f"收到需求分析智能体的响应，长度: {len(analysis_content)}")
            else:
                # 尝试转换为字符串
                try:
                    # 打印对象的所有属性，帮助调试
                    logger.info(f"TaskResult对象属性: {dir(result)}")

                    # 尝试获取content属性
                    if hasattr(result, 'content'):
                        analysis_content = result.content
                    else:
                        # 转换为字符串
                        analysis_content = str(result)

                    logger.info(f"将结果转换为字符串，长度: {len(analysis_content)}")
                except Exception as e:
                    logger.error(f"无法处理需求分析智能体返回的结果类型: {type(result)}, 错误: {str(e)}")
                    await self.publish_message(
                        ResponseMessage(source="Requirement Analysis Agent", content=f"错误：无法处理需求分析智能体返回的结果类型: {type(result)}"),
                        topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                    return

            # 检查分析内容是否为空
            if not analysis_content.strip():
                logger.error("需求分析智能体返回的内容为空")
                await self.publish_message(
                    ResponseMessage(source="Requirement Analysis Agent", content="错误：需求分析智能体未返回任何内容，请检查输入或重试"),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                return

            # 发送到下一个智能体
            logger.info("需求分析完成，发送到需求输出智能体")
            await self.publish_message(
                ResponseMessage(source="Requirement Analysis Agent", content="需求分析完成，开始需求结构化"),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

            await self.publish_message(
                RequirementMessage(source=self.id.type, content=analysis_content),
                topic_id=TopicId(requirement_validation_topic_type, source=self.id.key))

        except Exception as e:
            error_msg = f"需求分析错误: {str(e)}"
            logger.error(error_msg, exc_info=True)
            await self.publish_message(
                ResponseMessage(source="Requirement Analysis Agent", content=error_msg),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))


@type_subscription(topic_type=requirement_validation_topic_type)
class RequirementValidationAgent(RoutedAgent):
    """
    需求验证智能体
    在需求分析和需求结构化之间增加质量验证环节
    """

    def __init__(self):
        super().__init__("requirement validation agent")
        logger.info("需求验证智能体初始化完成")
        self._validation_prompt = """
        # 需求质量验证规范

        ## 验证标准
        1. 模糊术语检查：禁止出现"快速"、"便捷"等不可量化描述，必须转换为具体指标
        2. 验收标准验证：每个需求必须包含≥3个可测试的验收标准
        3. 异常场景覆盖：每个功能必须包含≥1个异常流程处理
        4. 量化指标：性能需求必须包含数字指标（如响应时间≤500ms）

        ## 输出格式
        {
          "valid": true|false,
          "issues": [
            {
              "type": "模糊术语/缺少异常场景/不可验证标准",
              "location": "需求1.验收标准2",
              "original_text": "原始问题内容",
              "suggestion": "具体修改建议（含示例）"
            }
          ],
          "passed_checks": ["检查项名称"]
        }
        """

    @message_handler
    async def handle_message(self, message: RequirementMessage, _ctx: MessageContext) -> None:
        logger.info(f"需求验证智能体收到消息，内容长度: {len(message.content)} 字符")

        try:
            # 创建验证智能体
            # 创建验证智能体
            logger.info("创建需求验证智能体")
            try:
                validation_agent = AssistantAgent(
                    name="requirement_validation_agent",
                    model_client=model_client,
                    system_message=self._validation_prompt
                )
                logger.info("需求验证智能体创建成功")
            except Exception as e:
                logger.error(f"创建需求验证智能体失败: {str(e)}", exc_info=True)
                raise

            # 注意：model_config参数不被支持，需要直接配置model_client

            # 构建验证任务
            task = f"""请根据验证规范检查以下需求文档质量：

            ## 待验证内容
            {message.content}

            ## 验证要求
            1. 严格按指定JSON格式输出
            2. 发现的问题必须包含具体位置引用
            3. 修改建议需提供可替换的完整句子示例
            """

            # 执行验证
            validation_result = await validation_agent.run(task=task)
            validation_content = validation_result if isinstance(validation_result, str) else ""

            # 解析验证结果
            try:
                validation_data = json.loads(validation_content)
                if not validation_data.get("valid", False):
                    issues = validation_data.get("issues", [])
                    logger.warning(f"发现 {len(issues)} 个质量问题")

                    # 发送验证问题到前端
                    await self.publish_message(
                        ResponseMessage(
                            source="需求验证",
                            content=json.dumps({
                                "status": "validation_failed",
                                "issues": issues
                            }),
                            is_final=False
                        ),
                        topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
                    )

                    # 触发重新分析流程
                    await self.publish_message(
                        RequirementMessage(source=self.id.type, content=message.content),
                        topic_id=TopicId(requirement_analysis_topic_type, source=self.id.key)
                    )
                    return

            except json.JSONDecodeError:
                logger.error("验证结果解析失败")

            # 验证通过则继续流程
            await self.publish_message(
                RequirementMessage(source=self.id.type, content=message.content),
                topic_id=TopicId(requirement_output_topic_type, source=self.id.key)
            )

        except Exception as e:
            error_msg = f"需求验证过程错误: {str(e)}"
            logger.error(error_msg, exc_info=True)
            await self.publish_message(
                ResponseMessage(source="需求验证智能体", content=error_msg),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

@type_subscription(topic_type=requirement_output_topic_type)
class RequirementOutputAgent(RoutedAgent):
    """
    需求结构化智能体

    负责将需求分析报告转换为结构化的JSON格式需求列表，包括需求描述、测试方法、充分性要求和通过准则。
    该智能体是需求分析流程的第三步，接收来自需求分析智能体的输出。
    """
    def __init__(self):
        super().__init__("requirement output agent")
        logger.info("需求结构化智能体初始化完成")
        self._prompt = """
        ## 结构化要求
        将分析结果转换为以下严格JSON格式：

        {
          "requirements": [
            {
              "name": "功能名称（≤30字）",
              "description": "功能详细描述（含输入输出）",
              "scenarios": ["场景1", "场景2"],
              "acceptance_criteria": [
                {
                  "type": "功能正确性/性能/安全",
                  "description": "可验证的描述",
                  "metric": "量化指标（如响应时间<500ms）"
                }
              ],
              "category": "功能分类",
              "dependencies": ["依赖项"],
              "non_functional": {
                "security": ["加密要求", "权限规则"],
                "performance": "TPS≥1000"
              }
            }
          ]
        }

        ## 转换规则
        1. 每个验收标准必须包含可量化指标
        2. 用户场景需包含至少1个异常流程
        3. 非功能需求需明确测量方法
        4. 使用双引号，禁用注释
        """

    @message_handler
    async def handle_message(self, message: RequirementMessage, _ctx: MessageContext) -> None:
        """
        处理需求消息

        Args:
            message: 需求消息，包含从需求分析智能体传来的内容
            _ctx: 消息上下文
        """

        logger.info(f"需求输出智能体收到消息，内容长度: {len(message.content)} 字符")

        # 创建需求结构化智能体 - 使用能够返回JSON的模型
        logger.info("创建需求结构化LLM智能体")
        try:
            output_agent = AssistantAgent(
                name="requirement_output_agent",
                model_client=model_client,
                system_message=self._prompt,
                model_client_stream=True,
            )
            logger.info("需求结构化智能体创建成功")
        except Exception as e:
            logger.error(f"创建需求结构化智能体失败: {str(e)}", exc_info=True)
            raise

        # 发送状态消息到前端
        await self.publish_message(
            ResponseMessage(source="需求结构化智能体", content="正在进行需求结构化...\n\n"),
            topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

        # 构建任务描述
        task = f"基于需求描述，将提取的需求信息转换为结构化的JSON格式：\n\n{message.content}\n\n【重要提示】：这是全新的内容，请基于此内容进行结构化，不要使用之前的结果或缓存数据。每个需求应转换为一个单独的JSON对象。"
        output_content = ""
        logger.info("构建需求结构化任务完成")

        # 在终端打印完整的需求内容，以便确认是否正确获取了需求
        print("\n" + "="*80)
        print("【需求结构化智能体获取到的需求分析内容】")
        print(message.content[:1024] + "..." if len(message.content) > 1024 else message.content)
        print("="*80 + "\n")

        # 检查内容是否为空
        if not message.content or not message.content.strip():
            logger.warning("需求内容为空，无法进行结构化")
            print("\n" + "="*80)
            print("【警告】需求内容为空，无法进行结构化")
            print("="*80 + "\n")
            await self.publish_message(
                ResponseMessage(source="Requirement Structuring Agent", content="错误：需求内容为空，无法进行结构化"),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
            return

        try:
            # 执行任务
            logger.info("开始执行需求结构化任务")
            logger.info(f"任务内容长度: {len(task)} 字符")
            logger.info(f"任务内容前100个字符: {task[:100]}...")

            # 记录开始时间
            start_time = time.time()
            logger.info(f"需求结构化开始时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}")

            try:
                # 移除stream参数，因为当前版本的AutoGen不支持
                # stream = output_agent.run(task=task, stream=True)
                logger.info("调用output_agent.run()...")
                result = await output_agent.run(task=task)
                logger.info(f"output_agent.run()调用成功，结果类型: {type(result)}")

                # 记录结束时间
                end_time = time.time()
                logger.info(f"需求结构化结束时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(end_time))}")
                logger.info(f"需求结构化耗时: {end_time - start_time:.2f} 秒")
            except Exception as e:
                logger.error(f"output_agent.run()调用失败: {str(e)}", exc_info=True)
                # 记录失败时间
                end_time = time.time()
                logger.error(f"需求结构化失败时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(end_time))}")
                logger.error(f"需求结构化失败耗时: {end_time - start_time:.2f} 秒")
                raise

            # 处理结果
            logger.info(f"需求结构化智能体返回结果类型: {type(result)}")

            # 处理TaskResult对象
            if hasattr(result, 'response') and hasattr(result.response, 'content'):
                # 从TaskResult.response中提取内容
                output_content = result.response.content
                logger.info(f"从TaskResult.response提取内容成功，长度: {len(output_content)}")

                # 发送结果到前端
                await self.publish_message(
                    ResponseMessage(source="需求结构化智能体", content="正在处理需求结构化结果..."),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
            elif hasattr(result, 'messages') and result.messages:
                # 从TaskResult中提取最后一条消息的内容
                last_message = result.messages[-1]
                if hasattr(last_message, 'content'):
                    output_content = last_message.content
                    logger.info(f"从TaskResult.messages提取内容成功，长度: {len(output_content)}")
            elif isinstance(result, str):
                # 直接处理字符串结果
                output_content = result
                logger.info(f"收到需求结构化智能体的响应，长度: {len(output_content)}")
            else:
                # 尝试转换为字符串
                try:
                    # 打印对象的所有属性，帮助调试
                    logger.info(f"TaskResult对象属性: {dir(result)}")

                    # 尝试获取content属性
                    if hasattr(result, 'content'):
                        output_content = result.content
                    else:
                        # 转换为字符串
                        output_content = str(result)

                    logger.info(f"将结果转换为字符串，长度: {len(output_content)}")
                except Exception as e:
                    logger.error(f"无法处理需求结构化智能体返回的结果类型: {type(result)}, 错误: {str(e)}")
                    await self.publish_message(
                        ResponseMessage(source="需求结构化智能体", content=f"错误：无法处理需求结构化智能体返回的结果类型: {type(result)}"),
                        topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                    return

            # 尝试解析JSON
            try:
                # 清理输出以确保它是有效的JSON
                json_content = output_content
                logger.info(f"开始清理JSON内容，原始内容: {json_content[:100]}...")

                # 记录完整内容用于调试
                logger.info(f"完整内容: {json_content}")

                # 尝试提取JSON部分
                # 首先尝试查找JSON代码块
                if "```json" in json_content:
                    logger.info("检测到JSON代码块标记，正在提取")
                    parts = json_content.split("```json")
                    if len(parts) > 1:
                        json_content = parts[1]
                        if "```" in json_content:
                            json_content = json_content.split("```")[0]
                elif "```" in json_content:
                    # 尝试提取任何代码块
                    logger.info("检测到代码块标记，正在提取")
                    parts = json_content.split("```")
                    if len(parts) > 1:
                        # 取第二部分（第一个代码块）
                        json_content = parts[1]

                # 尝试查找JSON对象的开始和结束
                if "{" in json_content and "}" in json_content:
                    logger.info("尝试提取JSON对象")
                    start_idx = json_content.find("{")
                    end_idx = json_content.rfind("}")
                    if start_idx >= 0 and end_idx > start_idx:
                        json_content = json_content[start_idx:end_idx+1]

                # 去除空白
                json_content = json_content.strip()
                logger.info(f"JSON内容清理完成，长度: {len(json_content)} 字符，内容: {json_content[:100]}...")

                # 解析JSON以验证格式
                try:
                    # 使用LLM生成的内容
                    logger.info("尝试解析LLM生成的JSON内容")

                    # 尝试解析JSON
                    try:
                        parsed_json = json.loads(json_content)
                    except json.JSONDecodeError as json_err:
                        # 尝试修复常见的JSON错误
                        logger.warning(f"初次JSON解析失败: {str(json_err)}，尝试修复")

                        # 1. 尝试修复单引号问题
                        fixed_content = json_content.replace("'", "\"")
                        try:
                            parsed_json = json.loads(fixed_content)
                            logger.info("修复单引号后JSON解析成功")
                        except json.JSONDecodeError:
                            # 2. 尝试修复缺少双引号的键
                            import re
                            # 查找形如 {key: "value"} 的模式并修复为 {"key": "value"}
                            fixed_content = re.sub(r'([{,])\s*([a-zA-Z0-9_]+)\s*:', r'\1"\2":', fixed_content)
                            try:
                                parsed_json = json.loads(fixed_content)
                                logger.info("修复键的引号后JSON解析成功")
                            except json.JSONDecodeError:
                                # 3. 尝试构造一个基本的JSON结构
                                logger.warning("无法修复JSON，尝试构造基本结构")
                                # 提取可能的需求项
                                requirements = []
                                # 查找可能的标题和描述
                                title_matches = re.findall(r'"title"\s*:\s*"([^"]+)"', json_content)
                                desc_matches = re.findall(r'"description"\s*:\s*"([^"]+)"', json_content)

                                # 如果找到了标题和描述，构造简单的需求项
                                if title_matches and desc_matches:
                                    for i in range(min(len(title_matches), len(desc_matches))):
                                        requirements.append({
                                            "title": title_matches[i],
                                            "description": desc_matches[i],
                                            "category": "功能性",
                                            "project_id": 2
                                        })

                                if requirements:
                                    parsed_json = {"requirements": requirements}
                                    logger.info(f"成功构造了 {len(requirements)} 个需求项")
                                else:
                                    # 如果无法从内容中提取需求，则手动构建需求
                                    logger.warning("无法从内容中提取需求，手动构建需求")

                                    # 从Markdown内容中提取需求
                                    import re

                                    # 尝试提取需求ID和描述
                                    req_pattern = r'REQ-(\d+)\s*\|\s*([^|]+)\s*\|'
                                    req_matches = re.findall(req_pattern, json_content)

                                    if req_matches:
                                        for i, (req_id, desc) in enumerate(req_matches):
                                            requirements.append({
                                                "title": f"需求 REQ-{req_id}",
                                                "description": desc.strip(),
                                                "category": "功能性",
                                                "project_id": 2
                                            })

                                        parsed_json = {"requirements": requirements}
                                        logger.info(f"从Markdown表格中提取了 {len(requirements)} 个需求项")
                                    else:
                                        # 如果无法从表格中提取，尝试从列表中提取
                                        list_pattern = r'[-*]\s*(.+?)(?=\n[-*]|\n\n|\n$|$)'
                                        list_matches = re.findall(list_pattern, json_content)

                                        if list_matches and len(list_matches) > 0:
                                            for i, item in enumerate(list_matches):
                                                if len(item.strip()) > 10:  # 确保内容有意义
                                                    requirements.append({
                                                        "title": f"需求项 {i+1}",
                                                        "description": f"作为系统用户，我希望{item.strip()}，以便提高系统的可用性。",
                                                        "category": "功能性",
                                                        "project_id": 2
                                                    })

                                            parsed_json = {"requirements": requirements}
                                            logger.info(f"从列表中提取了 {len(requirements)} 个需求项")
                                        else:
                                            # 最后的尝试：直接从文本中创建需求
                                            # 将内容分成几段
                                            paragraphs = re.split(r'\n\s*\n', json_content)
                                            meaningful_paragraphs = [p for p in paragraphs if len(p.strip()) > 20]

                                            if meaningful_paragraphs:
                                                for i, para in enumerate(meaningful_paragraphs[:4]):  # 最多取4个段落
                                                    requirements.append({
                                                        "title": f"需求 {i+1}",
                                                        "description": f"作为系统用户，我希望实现以下功能：{para.strip()[:100]}...",
                                                        "category": "功能性",
                                                        "project_id": 2
                                                    })

                                                parsed_json = {"requirements": requirements}
                                                logger.info(f"从段落中创建了 {len(requirements)} 个需求项")
                                            else:
                                                # 如果所有尝试都失败，创建一个基于用户输入的简单需求
                                                parsed_json = {
                                                    "requirements": [
                                                        {
                                                            "title": "用户输入的需求",
                                                            "description": f"作为系统用户，我希望实现以下功能：{json_content[:200]}...",
                                                            "category": "功能性",
                                                            "project_id": 2
                                                        }
                                                    ]
                                                }
                                                logger.info("创建了默认需求项")

                    # 检查需求项数量
                    requirements_count = len(parsed_json.get("requirements", []))
                    logger.info(f"解析LLM生成的JSON成功，包含 {requirements_count} 个需求项")

                    # 确保每个需求项都有必要的字段
                    if requirements_count > 0:
                        for i, req in enumerate(parsed_json["requirements"]):
                            # 确保有title
                            if "title" not in req or not req["title"]:
                                req["title"] = f"需求项 {i+1}"
                            # 确保有description
                            if "description" not in req or not req["description"]:
                                req["description"] = f"需求项 {i+1} 的描述"
                            # 确保有category
                            if "category" not in req or not req["category"]:
                                req["category"] = "功能性"
                            # 确保有project_id
                            if "project_id" not in req or not req["project_id"]:
                                req["project_id"] = 2

                        # 更新JSON内容
                        json_content = json.dumps(parsed_json, ensure_ascii=False)
                        logger.info("已确保所有需求项都有必要的字段")

                    if requirements_count == 0:
                        logger.warning("JSON中没有需求项")
                        await self.publish_message(
                            ResponseMessage(source="需求结构化智能体", content="警告：结构化后的需求列表为空，请检查输入或重试"),
                            topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                except json.JSONDecodeError as json_err:
                    logger.error(f"JSON解析失败: {str(json_err)}", exc_info=True)
                    raise

                # 发送到下一个智能体
                logger.info("需求结构化完成，发送到数据库智能体")
                await self.publish_message(
                    ResponseMessage(source="需求结构化智能体", content="需求结构化完成，正在保存到数据库"),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

                await self.publish_message(
                    RequirementMessage(source=self.id.type, content=json_content),
                    topic_id=TopicId(requirement_database_topic_type, source=self.id.key))

            except json.JSONDecodeError:
                logger.error("无法修复JSON格式，需求结构化失败", exc_info=True)
                await self.publish_message(
                    ResponseMessage(source="需求结构化智能体", content="无法修复JSON格式，需求结构化失败"),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
        except Exception as e:
            error_msg = f"需求结构化过程错误: {str(e)}"
            logger.error(error_msg, exc_info=True)
            await self.publish_message(
                ResponseMessage(source="需求结构化智能体", content=error_msg),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))


@type_subscription(topic_type=requirement_database_topic_type)
class RequirementDatabaseAgent(RoutedAgent):
    """
    需求数据库智能体

    负责将结构化的需求数据保存到数据库中。
    该智能体是需求分析流程的最后一步，接收来自需求结构化智能体的JSON数据。
    """
    def __init__(self):
        super().__init__("requirement database agent")
        logger.info("需求数据库智能体初始化完成")

    @message_handler
    async def handle_message(self, message: RequirementMessage, _ctx: MessageContext) -> None:
        """
        处理需求消息

        Args:
            message: 需求消息，包含从需求结构化智能体传来的JSON内容
            _ctx: 消息上下文
        """
        logger.info(f"需求数据库智能体收到消息，内容长度: {len(message.content)} 字符")

        await self.publish_message(
            ResponseMessage(source="需求数据库智能体", content="正在验证数据..."),
            topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

        try:
            # 解析JSON
            logger.info("开始解析JSON数据")
            logger.info(f"JSON数据内容: {message.content[:200]}...")

            try:
                requirement_data = json.loads(message.content)
                logger.info(f"JSON解析成功，数据类型: {type(requirement_data)}")

                # 检查是否包含requirements字段
                if "requirements" in requirement_data:
                    logger.info(f"JSON数据包含requirements字段，类型: {type(requirement_data['requirements'])}")
                else:
                    logger.warning("JSON数据不包含requirements字段")
                    logger.info(f"JSON数据完整内容: {requirement_data}")

                requirement_list = RequirementList(**requirement_data)
                logger.info("RequirementList创建成功")

                requirements_count = len(requirement_list.requirements)
                logger.info(f"解析成功，需要保存 {requirements_count} 个需求项")
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {str(e)}", exc_info=True)
                logger.error(f"JSON数据完整内容: {message.content}")
                raise
            except Exception as e:
                logger.error(f"RequirementList创建失败: {str(e)}", exc_info=True)
                raise

            if requirements_count == 0:
                logger.warning("没有需求项可保存")
                await self.publish_message(
                    ResponseMessage(source="需求数据库智能体", content="警告：没有需求可保存", is_final=True),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                return

            # 不自动保存到数据库，只返回需求数据
            count = len(requirement_list.requirements)
            logger.info(f"需求分析完成，生成了 {count} 个需求项，但不自动保存到数据库")

            # 发送数据库保存结果（非最终消息）
            logger.info("发送数据库保存结果")
            await self.publish_message(
                ResponseMessage(
                    source="database",
                    content=requirement_list.model_dump_json(),
                    is_final=False
                ),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

            # 发送最终完成消息
            success_msg = f"需求分析完成，共生成 [{count}] 个需求，请点击保存按钮将需求保存到数据库"
            logger.info(success_msg)
            await self.publish_message(
                ResponseMessage(
                    source="需求数据库智能体",
                    content=success_msg,
                    is_final=True
                ),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
        except json.JSONDecodeError as json_err:
            error_msg = f"JSON解析错误: {str(json_err)}"
            logger.error(error_msg, exc_info=True)
            await self.publish_message(
                ResponseMessage(source="Database Agent", content=error_msg, is_final=True),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
        except Exception as e:
            error_msg = f"数据库操作错误: {str(e)}"
            logger.error(error_msg, exc_info=True)
            await self.publish_message(
                ResponseMessage(source="Database Agent", content=error_msg, is_final=True),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))


async def start_runtime(requirement_files: RequirementFilesMessage,
                        collect_result: Callable[[ClosureContext, ResponseMessage, MessageContext], Awaitable[None]],
                        user_input_func: Callable[[str, Optional[CancellationToken]], Awaitable[str]] = None):
    """
    启动需求分析运行时（完整闭环版本）
    """
    logger.info(f"启动需求分析流程: 用户ID={requirement_files.user_id}")

    # 记录用户输入的内容，用于调试
    if requirement_files.content:
        logger.info(f"用户输入的需求内容长度: {len(requirement_files.content)} 字符")
        logger.info(f"用户输入的需求内容前100个字符: {requirement_files.content[:100]}")
        # 在终端打印完整的需求内容，以便确认是否正确获取了需求
        print("\n" + "="*80)
        print("【需求获取智能体获取到的需求内容】")
        print(requirement_files.content)
        print("="*80 + "\n")
    else:
        logger.warning("用户没有输入需求内容")
        print("\n" + "="*80)
        print("【警告】用户没有提供需求内容")
        print("="*80 + "\n")

    # 创建消息队列
    message_queue = asyncio.Queue()

    # 重写RoutedAgent的publish_message方法，将消息放入队列
    class QueuedRoutedAgent(RoutedAgent):
        async def publish_message(self, message, topic_id):
            # 调用原始方法打印消息
            await super().publish_message(message, topic_id)

            # 将消息发送到前端
            if topic_id.type == task_result_topic_type:
                await collect_result(
                    ClosureContext(),
                    message,
                    MessageContext()
                )
            # 将消息放入队列，用于智能体之间的通信
            else:
                await message_queue.put({
                    "topic_type": topic_id.type,
                    "content": message
                })
                logger.info(f"消息已放入队列: {topic_id.type}, 来源: {message.source}, 内容长度: {len(message.content) if hasattr(message, 'content') else 'N/A'}")

    # 使用新的智能体类
    class QueuedRequirementAcquisitionAgent(RequirementAcquisitionAgent, QueuedRoutedAgent):
        pass

    class QueuedRequirementAnalysisAgent(RequirementAnalysisAgent, QueuedRoutedAgent):
        pass

    class QueuedRequirementValidationAgent(RequirementValidationAgent, QueuedRoutedAgent):
        pass

    class QueuedRequirementOutputAgent(RequirementOutputAgent, QueuedRoutedAgent):
        pass

    class QueuedRequirementDatabaseAgent(RequirementDatabaseAgent, QueuedRoutedAgent):
        pass

    # 初始化所有智能体
    agents = {
        "acquisition": QueuedRequirementAcquisitionAgent(input_func=user_input_func),
        "analysis": QueuedRequirementAnalysisAgent(),
        "validation": QueuedRequirementValidationAgent(),
        "output": QueuedRequirementOutputAgent(),
        "database": QueuedRequirementDatabaseAgent()
    }

    try:
        # 发送启动通知
        await collect_result(
            ClosureContext(),
            ResponseMessage(source="system", content="🚀 开始需求分析流程", is_final=False),
            MessageContext()
        )

        # 初始触发点：将用户输入送入获取智能体
        await agents["acquisition"].handle_message(
            requirement_files,
            MessageContext()
        )

        # 设置超时时间（10分钟）
        timeout = 600  # 秒
        start_time = asyncio.get_event_loop().time()

        # 消息循环处理
        while True:
            # 检查是否超时
            current_time = asyncio.get_event_loop().time()
            if current_time - start_time > timeout:
                logger.warning(f"需求分析流程超时，已运行 {timeout} 秒")
                await collect_result(
                    ClosureContext(),
                    ResponseMessage(source="system", content=f"⚠️ 需求分析流程超时，已运行 {timeout} 秒", is_final=True),
                    MessageContext()
                )
                break

            try:
                # 等待消息，设置超时时间为1秒
                msg = await asyncio.wait_for(message_queue.get(), timeout=1)
                logger.info(f"从队列获取消息: {msg['topic_type']}")

                # 添加更多日志信息
                if isinstance(msg["content"], RequirementMessage):
                    logger.info(f"消息内容类型: RequirementMessage, 来源: {msg['content'].source}, 内容长度: {len(msg['content'].content) if hasattr(msg['content'], 'content') else 'N/A'}")
                else:
                    logger.info(f"消息内容类型: {type(msg['content'])}")

                # 根据消息类型路由到对应智能体
                try:
                    if msg["topic_type"] == requirement_acquisition_topic_type:
                        logger.info("路由消息到需求获取智能体")
                        await agents["acquisition"].handle_message(msg["content"], MessageContext())
                    elif msg["topic_type"] == requirement_analysis_topic_type:
                        logger.info("路由消息到需求分析智能体")
                        await agents["analysis"].handle_message(msg["content"], MessageContext())
                    elif msg["topic_type"] == requirement_validation_topic_type:
                        logger.info("路由消息到需求验证智能体")
                        await agents["validation"].handle_message(msg["content"], MessageContext())
                    elif msg["topic_type"] == requirement_output_topic_type:
                        logger.info("路由消息到需求结构化智能体")
                        await agents["output"].handle_message(msg["content"], MessageContext())
                    elif msg["topic_type"] == requirement_database_topic_type:
                        logger.info("路由消息到需求数据库智能体")
                        await agents["database"].handle_message(msg["content"], MessageContext())
                        # 如果是数据库智能体的最终消息，结束流程
                        if hasattr(msg["content"], "is_final") and msg["content"].is_final:
                            logger.info("收到数据库智能体的最终消息，结束流程")
                            break
                    else:
                        logger.warning(f"未知的消息类型: {msg['topic_type']}")
                except Exception as e:
                    logger.error(f"处理消息时出错: {str(e)}", exc_info=True)
                    # 发送错误消息到前端
                    await collect_result(
                        ClosureContext(),
                        ResponseMessage(source="system", content=f"❌ 处理消息时出错: {str(e)}", is_final=False),
                        MessageContext()
                    )
            except asyncio.TimeoutError:
                # 超时但队列为空，检查是否所有智能体都已完成工作
                if message_queue.empty():
                    logger.info("消息队列为空，检查是否需要结束流程")
                    # 这里可以添加更多的检查逻辑
                    # 暂时不做任何操作，继续等待消息

    except Exception as e:
        logger.error(f"流程异常终止: {str(e)}", exc_info=True)
        await collect_result(
            ClosureContext(),
            ResponseMessage(source="system", content=f"❌ 流程异常: {str(e)}", is_final=True),
            MessageContext()
        )
