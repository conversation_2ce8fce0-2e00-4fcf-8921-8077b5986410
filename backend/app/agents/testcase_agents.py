import json
import logging
import asyncio
import re
from dataclasses import dataclass
from typing import Callable, Optional, Awaitable, Any, List, Dict

# 导入AutoGen相关模块
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import TextMessage
from autogen_core import RoutedAgent, type_subscription, message_handler, MessageContext, SingleThreadedAgentRuntime, \
    DefaultTopicId, TypeSubscription, ClosureAgent, CancellationToken, ClosureContext, TopicId
from pydantic import BaseModel, Field

from app.schemas.requirement import RequirementSelect
from app.agents.llms import model_client

def split_content(content: str, chunk_size: int = 500) -> List[str]:
    """
    将内容分割成多个块，每个块不超过chunk_size个字符

    Args:
        content: 要分割的内容
        chunk_size: 每个块的最大字符数

    Returns:
        分割后的内容块列表
    """
    # 如果内容为空，返回空列表
    if not content:
        return []

    # 如果内容长度小于chunk_size，直接返回
    if len(content) <= chunk_size:
        return [content]

    # 按段落分割内容
    paragraphs = re.split(r'\n\s*\n', content)

    chunks = []
    current_chunk = ""

    for paragraph in paragraphs:
        # 如果段落本身超过chunk_size，按句子分割
        if len(paragraph) > chunk_size:
            sentences = re.split(r'(?<=[.!?])\s+', paragraph)
            for sentence in sentences:
                # 如果句子本身超过chunk_size，按字符分割
                if len(sentence) > chunk_size:
                    for i in range(0, len(sentence), chunk_size):
                        chunk = sentence[i:i+chunk_size]
                        chunks.append(chunk)
                else:
                    # 如果当前块加上新句子超过chunk_size，创建新块
                    if len(current_chunk) + len(sentence) > chunk_size:
                        if current_chunk:
                            chunks.append(current_chunk)
                        current_chunk = sentence
                    else:
                        if current_chunk:
                            current_chunk += " " + sentence
                        else:
                            current_chunk = sentence
        else:
            # 如果当前块加上新段落超过chunk_size，创建新块
            if len(current_chunk) + len(paragraph) > chunk_size:
                if current_chunk:
                    chunks.append(current_chunk)
                current_chunk = paragraph
            else:
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph

    # 添加最后一个块
    if current_chunk:
        chunks.append(current_chunk)

    return chunks

# 配置日志
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 定义主题类型（用于智能体之间的消息传递）
testcase_generator_topic_type = "testcase_generator"  # 测试用例生成主题
testcase_structure_topic_type = "testcase_structure"  # 测试用例结构化主题
testcase_database_topic_type = "testcase_database"    # 测试用例数据库主题
testcase_review_topic_type = "testcase_review"        # 测试用例评审主题
testcase_finalize_topic_type = "testcase_finalize"    # 测试用例最终化主题
task_result_topic_type = "collect_result"             # 结果收集主题


class TestCaseList(BaseModel):
    """测试用例列表模型，用于存储多个测试用例项"""
    testcases: list[dict] = Field(..., description="测试用例列表")


class RequirementMessage(RequirementSelect):
    """需求消息模型，继承自RequirementSelect，用于传递需求信息"""
    scenario: str = ""  # 业务场景描述
    task: str = ""      # 任务描述
    reviewer: str = "admin"  # 默认审核人，避免None值


class ResponseMessage(BaseModel):
    """响应消息模型，用于向前端发送消息"""
    source: str     # 消息来源
    content: str    # 消息内容
    is_final: bool = False  # 是否为最终消息


@dataclass
class TestCaseMessage:
    """测试用例消息数据类，用于智能体之间传递测试用例信息"""
    source: str  # 消息来源
    content: Any  # 消息内容


@type_subscription(topic_type=testcase_generator_topic_type)
class TestCaseGeneratorAgent(RoutedAgent):
    def __init__(self, input_func=None):
        super().__init__("testcase generator agent")
        self.input_func = input_func

        self._prompt = """
        你是一位高级软件测试用例编写工程师，专注于软件质量保障与测试覆盖率最大化。请根据用户提供的软件需求描述：[[description]]，严格结合业务场景及上下文信息，高质量完成用户的任务：[[task]]

        ## Role
        **Background**：
        - 8年测试开发经验，参与过电商/金融/物联网等多领域测试架构设计
        - ISTQB认证专家，精通测试用例设计方法与质量评估模型

        **Profile**：
        - 风格：严谨的边界条件探索者，擅长发现隐藏的业务逻辑bug及漏洞
        - 语调：结构化表述，参数精确到计量单位
        - 方法论：ISTQB标准+基于等价类划分+边界值分析+场景法+错误猜测法的组合设计
        - 核心能力：需求覆盖率验证、异常路径挖掘、自动化适配

        **Skills**：
        - 全面运用**测试模式库**：边界值分析、等价类划分、因果图等
        - 深度业务场景分析与风险评估
        - 测试策略精准制定能力：API/UI/性能/安全
        - 需求到测试条件的映射能力
        - 自动化测试脚本设计（JUnit/TestNG/PyTest）
        - 性能测试方案设计（JMeter/LoadRunner）
        - 安全测试基础（OWASP Top10漏洞检测）
        - 跨浏览器/设备兼容性测试
        - 测试用例设计分析能力
        - 多种测试技术的运用能力

        **Goals**：
        - 确保需求覆盖率达到100%
        - 关键路径测试深度≥3层（正常/异常/极限场景）
        - 输出用例可被自动化测试框架直接调用
        - 尽可能多的覆盖到多种用例场景

        **Constrains**：
        - 时间限制：单需求用例设计时间≤5分钟
        - 需求锚定：严格匹配需求描述，禁止假设扩展
        - 自动化友好：步骤可脚本化，量化验证指标
        - 覆盖维度：正常流/边界值/异常流/安全基线
        - 优先级标注：高(核心路径)/中(主要功能)/低(边缘场景)
        - 范围限制：严格根据需求的场景说明文档
        - 内容约束：不编造未说明的内容
        - 测试数据具体化：具体值而非通用描述
        - 预期结果必须可量化验证

        ## Business Scenario
        [[scenario]]

        ## OutputFormat

        ### [顺序编号] 用例标题：[动作词]+[测试对象]+[预期行为]
        **用例描述**：[测试用例的详细描述]
        **测试类型**：[单元测试/接口测试/功能测试/性能测试/安全测试]
        **优先级**：[高/中/低]
        **用例状态**：[未开始/进行中/通过/失败/阻塞]
        **需求ID**：[[requirement_id]]
        **项目ID**：[[project_id]]
        **创建者**：[[creator]]
        **前置条件**：[明确环境或数据依赖]
        - [前置条件1]
        - [前置条件2]
        - ......

        **后置条件**：[明确后置条件]
        - [后置条件1]
        - [后置条件2]
        - ......

        **测试步骤**：原子化操作（步骤≤7步）
        - 步骤1：
            - [步骤描述]
            - [预期结果]
        - 步骤2：
        - ......


        ## Workflow
        1. 输入解析：提取需求文档中的功能点/业务规则
        2. 理解需求：深入理解软件的需求和功能，分析需求文档，理解用户故事
        3. 确定测试范围：确定需要测试哪些功能和特性。这可能包括正常操作，边缘情况，错误处理等。
        4. 设计测试策略：确定你将如何测试这些功能。这可能包括单元测试，集成测试，系统测试，性能测试、安全测试等。
        5. 条件拆解：
           - 划分正常流（Happy Path）
           - 识别边界条件（数值边界/状态转换）
           - 构造异常场景（无效输入/服务降级）
        6. 用例生成：
           - 根据需求特点确定测试用例的总数
           - 按[Given-When-Then]模式结构化步骤
           - 量化验证指标（时间/数量/状态码）
           - 标注测试数据准备要求
           - 根据需求特点运用不同的测试技术，如等价类划分、边界值分析、流程图遍历、决策表测试等，设计每个测试用例。
        """

    @message_handler
    async def handle_message(self, message: RequirementMessage, ctx: MessageContext) -> None:
        # 确保所有字段都有值
        # scenario = message.scenario or "进出境公路运输工具备案系统"
        # task = message.task or "生成测试用例"
        # description = message.description or "进出境公路运输工具备案系统的功能测试"
        # creator = message.reviewer or "admin"

        self._prompt = ((self._prompt.
                         replace('[[scenario]]', message.scenario).
                         replace('[[project_id]]', str(message.project_id))).
                        replace('[[requirement_id]]', str(message.id)).
                        replace("[[task]]", message.task).
                        replace('[[description]]', message.description).
                        replace('[[creator]]', message.creator))
        # 发送到前端提示
        await self.publish_message(ResponseMessage(source="user", content=f"收到用户指令，准备开始执行：{message.task}"),
                                   topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
        testcase_generator_agent = AssistantAgent(
            name="testcase_generator_agent",
            model_client=model_client.openai_client,
            system_message=self._prompt,
        )
        # 创建一个简单的配置字典，避免使用model_info
        # config_dict = {
        #     "name": "testcase_generator_agent",
        #     "system_message": self._prompt,
        #     "llm_config": {
        #         "config_list": [{"model": model_client.model}],
        #         "temperature": 0.7,
        #         "max_tokens": 2000
        #     }
        # }

        # 创建AssistantAgent实例
        # testcase_generator_agent = AssistantAgent(**config_dict)

        # 发送状态消息到前端
        await self.publish_message(
            ResponseMessage(source="测试用例生成智能体", content="正在生成测试用例...\n\n"),
            topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

        # 执行任务并流式输出
        testcase_content = ""
        try:
            # 使用自定义方法调用LLM，传递完整的需求内容
            prompt = f"""
            根据以下需求生成测试用例：

            需求标题: {message.title}
            需求描述: {message.description}
            业务场景: {message.scenario}

            请根据需求生成详细的测试用例，包括测试步骤和预期结果。
            """
            logger.info(f"发送给LLM的提示词: {prompt[:200]}...")

            try:
                response = await model_client.run(task=prompt)
                testcase_content = response
                logger.info(f"LLM返回的测试用例内容长度: {len(testcase_content)}")

                # 记录测试用例内容的前200个字符，用于调试
                if testcase_content:
                    logger.info(f"测试用例内容前200个字符: {testcase_content[:200]}...")

                # 创建消息列表
                messages = [
                    {
                        "source": "testcase_generator_agent",
                        "content": testcase_content,
                        "role": "assistant"
                    }
                ]

                # 导入AutoGenAdapter
                from app.agents.autogen_adapter import AutoGenAdapter

                # 创建TaskResult对象
                result = AutoGenAdapter.create_task_result(messages)

                # 发送结果到前端
                if result and result.messages:
                    for msg in result.messages:
                        if isinstance(msg, TextMessage) and msg.source == "testcase_generator_agent":
                            testcase_content = msg.content
                            # 分段发送内容，模拟流式输出
                            chunks = split_content(testcase_content)
                            for chunk in chunks:
                                await self.publish_message(
                                    ResponseMessage(source="测试用例生成智能体", content=chunk),
                                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                                # 添加短暂延迟，模拟流式输出
                                await asyncio.sleep(0.1)
            except Exception as api_error:
                logger.error(f"调用LLM API失败: {str(api_error)}", exc_info=True)
                await self.publish_message(
                    ResponseMessage(source="测试用例生成智能体", content=f"调用API失败: {str(api_error)}"),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                # 设置空内容，但不继续处理
                testcase_content = ""
                # 抛出异常，中断处理流程
                raise Exception(f"调用API失败: {str(api_error)}")
        except Exception as e:
            logger.error(f"执行测试用例生成任务失败: {str(e)}", exc_info=True)
            await self.publish_message(
                ResponseMessage(source="error", content=f"执行测试用例生成任务失败: {str(e)}"),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
            testcase_content = ""

        # 发送到测试用例评审智能体
        await self.publish_message(
            ResponseMessage(source="测试用例生成智能体", content="测试用例生成完成，开始进行测试用例评审"),
            topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

        # 创建测试用例评审智能体
        review_system_message = """
            你是一位资深测试用例评审专家，负责对生成的测试用例进行全面评审，确保测试用例的质量、覆盖率和可执行性。

            ## Role
            **Background**：
            - 10年测试质量保障经验，参与过大型企业级软件的测试评审工作
            - ISTQB高级测试经理认证，精通测试用例评审标准与最佳实践

            **Profile**：
            - 风格：细致入微的质量把关者，善于发现测试盲点与逻辑漏洞
            - 语调：专业、建设性，提供可操作的改进建议
            - 方法论：基于IEEE 829测试文档标准的结构化评审

            ## 评审维度
            1. **需求覆盖度**：测试用例是否完整覆盖所有功能点与业务场景
            2. **逻辑完整性**：测试路径是否合理，步骤是否清晰
            3. **数据有效性**：测试数据是否具体、多样、边界完整
            4. **预期结果**：验证标准是否明确、可量化
            5. **可执行性**：用例是否可被测试人员理解并执行
            6. **自动化适配性**：用例结构是否便于自动化转换
            7. **维护成本**：用例的可维护性与扩展性评估

            ## 输出格式
            ```markdown
            # 测试用例评审报告

            ## 1. 总体评价
            - 覆盖率评分: [1-10分]
            - 质量评分: [1-10分]
            - 可执行性评分: [1-10分]

            ## 2. 主要优点
            - [优点1]
            - [优点2]
            - ......

            ## 3. 需改进项
            #### 1. 覆盖率问题
            **问题类型**: 场景缺失
            **具体描述**: [详细说明]
            **改进建议**: [具体建议]

            #### 2. 质量问题
            **问题类型**: 步骤模糊
            **具体描述**: [详细说明]
            **改进建议**: [具体建议]

            #### 3. 可执行性问题
            **问题类型**: 验证标准不明确
            **具体描述**: [详细说明]
            **改进建议**: [具体建议]

            #### 4. 典型案例
            **用例ID**: TC_APP_Login_003
            **问题类型**: 边界值缺失
            **具体描述**: 未覆盖密码长度为[1,64]的边界校验
            **改进建议**: 增加密码长度为0/65的异常流测试用例

            #### 5. 总结建议
            - 关键风险点: [风险描述]
            - 后续行动计划: [action items]
            ```

            ## 8. Workflow
            1. **输入解析**
               - 解析测试用例文档与需求追踪矩阵(RTM)
               - 提取用例步骤/预期结果/关联需求ID

            2. **分类评审**
               ```mermaid
               graph TD
               A[需求覆盖审查] --> B[逻辑正确性审查]
               B --> C[可执行性审查]
               C --> D[可维护性审查]
               ```

            3. **问题识别**
               - 标记缺失的测试场景
               - 标注模糊的断言条件
               - 标识冗余的测试步骤

            4. **优先级划分**
               - P0: 导致流程阻断的缺陷
               - P1: 影响测试有效性的问题
               - P2: 优化类建议

            5. **案例生成**
               - 为每类问题提供典型示例
               - 包含具体定位与修复方案

            6. **总结建议**
               - 生成风险雷达图
               - 输出可量化的改进指标
            """

        # 创建一个简单的配置字典，避免使用model_info
        review_config_dict = {
            "name": "testcase_review_agent",
            "system_message": review_system_message,
            "llm_config": {
                "config_list": [{"model": model_client.model}],
                "temperature": 0.7,
                "max_tokens": 2000
            }
        }

        # 创建AssistantAgent实例
        testcase_review_agent = AssistantAgent(**review_config_dict)

        # 执行评审任务
        review_task = f"请评审以下测试用例：\n\n{testcase_content}"
        review_report = ""

        await self.publish_message(
            ResponseMessage(source="测试用例评审智能体", content="正在评审测试用例...\n\n"),
            topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

        # 执行评审任务并获取结果
        try:
            # 使用自定义方法调用LLM，确保传递测试用例内容
            logger.info(f"发送给LLM的评审任务: {review_task[:200]}...")

            try:
                response = await model_client.run(task=review_task)
                review_report = response
                logger.info(f"LLM返回的评审报告长度: {len(review_report)}")

                # 记录评审报告内容的前200个字符，用于调试
                if review_report:
                    logger.info(f"评审报告内容前200个字符: {review_report[:200]}...")

                # 创建消息列表
                messages = [
                    {
                        "source": "testcase_review_agent",
                        "content": review_report,
                        "role": "assistant"
                    }
                ]

                # 导入AutoGenAdapter
                from app.agents.autogen_adapter import AutoGenAdapter

                # 创建TaskResult对象
                review_result = AutoGenAdapter.create_task_result(messages)

                if review_result and review_result.messages:
                    for msg in review_result.messages:
                        if isinstance(msg, TextMessage) and msg.source == "testcase_review_agent":
                            review_report = msg.content

                            # 分段发送内容，模拟流式输出
                            chunks = split_content(review_report)
                            for chunk in chunks:
                                await self.publish_message(
                                    ResponseMessage(source="测试用例评审智能体", content=chunk),
                                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                                # 添加短暂延迟，模拟流式输出
                                await asyncio.sleep(0.1)
            except Exception as api_error:
                logger.error(f"调用LLM API失败: {str(api_error)}", exc_info=True)
                await self.publish_message(
                    ResponseMessage(source="测试用例评审智能体", content=f"调用API失败: {str(api_error)}"),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                # 设置空内容，但不继续处理
                review_report = ""
                # 抛出异常，中断处理流程
                raise Exception(f"调用API失败: {str(api_error)}")
        except Exception as e:
            logger.error(f"执行测试用例评审任务失败: {str(e)}", exc_info=True)
            await self.publish_message(
                ResponseMessage(source="error", content=f"执行测试用例评审任务失败: {str(e)}"),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
            review_report = ""

        # 发送给下一个智能体
        await self.publish_message(
            ResponseMessage(source="测试用例评审智能体", content="测试用例评审完成，开始进行测试用例结构化"),
            topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

        # 发送给下一个智能体
        await self.publish_message(TestCaseMessage(source=self.id.type,
                                                   content="--测试用例开始-- \n" + testcase_content + "\n--测试用例结束-- \n" +
                                                           "--评审报告开始-- \n" + review_report + "\n--评审报告结束-- \n"),
                                   topic_id=TopicId(testcase_finalize_topic_type, source=self.id.key))

@type_subscription(topic_type=testcase_review_topic_type)
class TestCaseReviewAgent(RoutedAgent):
    """
    测试用例评审智能体

    负责对生成的测试用例进行评审，确保测试用例的质量、覆盖率和可执行性
    """
    def __init__(self):
        super().__init__("testcase review agent")
        self._review_prompt = """
        你是一位资深测试用例评审专家，负责对生成的测试用例进行全面评审，确保测试用例的质量、覆盖率和可执行性。

        ## Role
        **Background**：
        - 10年测试质量保障经验，参与过大型企业级软件的测试评审工作
        - ISTQB高级测试经理认证，精通测试用例评审标准与最佳实践

        **Profile**：
        - 风格：细致入微的质量把关者，善于发现测试盲点与逻辑漏洞
        - 语调：专业、建设性，提供可操作的改进建议
        - 方法论：基于IEEE 829测试文档标准的结构化评审

        ## 评审维度
        1. **需求覆盖度**：测试用例是否完整覆盖所有功能点与业务场景
        2. **逻辑完整性**：测试路径是否合理，步骤是否清晰
        3. **数据有效性**：测试数据是否具体、多样、边界完整
        4. **预期结果**：验证标准是否明确、可量化
        5. **可执行性**：用例是否可被测试人员理解并执行
        6. **自动化适配性**：用例结构是否便于自动化转换
        7. **维护成本**：用例的可维护性与扩展性评估

        ## 输出格式
        ```markdown
        # 测试用例评审报告

        ## 1. 总体评价
        - 覆盖率评分: [1-10分]
        - 质量评分: [1-10分]
        - 可执行性评分: [1-10分]

        ## 2. 主要优点
        - [优点1]
        - [优点2]
        - ......

        ## 3. 需改进项
        #### 1. 覆盖率问题
        **问题类型**: 场景缺失
        **具体描述**: [详细说明]
        **改进建议**: [具体建议]

        #### 2. 质量问题
        **问题类型**: 步骤模糊
        **具体描述**: [详细说明]
        **改进建议**: [具体建议]

        #### 3. 可执行性问题
        **问题类型**: 验证标准不明确
        **具体描述**: [详细说明]
        **改进建议**: [具体建议]

        #### 4. 典型案例
        **用例ID**: TC_APP_Login_003
        **问题类型**: 边界值缺失
        **具体描述**: 未覆盖密码长度为[1,64]的边界校验
        **改进建议**: 增加密码长度为0/65的异常流测试用例

        #### 5. 总结建议
        - 关键风险点: [风险描述]
        - 后续行动计划: [action items]
        ```
        """

    @message_handler
    async def handle_message(self, message: TestCaseMessage, ctx: MessageContext) -> None:
        """处理测试用例评审消息"""
        # 发送状态消息
        await self.publish_message(
            ResponseMessage(source="测试用例评审智能体", content="正在评审测试用例...\n\n"),
            topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
        )

        # 构建评审提示词
        review_prompt = f"""
        请评审以下测试用例：

        {message.content}

        请提供详细的评审报告，包括测试用例的覆盖率、质量和可执行性评分，以及改进建议。
        """

        try:
            # 调用LLM生成评审报告
            review_report = await model_client.run(task=review_prompt)
            logger.info(f"LLM返回的评审报告长度: {len(review_report)}")

            # 记录评审报告内容的前200个字符，用于调试
            if review_report:
                logger.info(f"评审报告内容前200个字符: {review_report[:200]}...")

                # 分段发送内容，模拟流式输出
                chunks = split_content(review_report)
                for chunk in chunks:
                    await self.publish_message(
                        ResponseMessage(source="测试用例评审智能体", content=chunk),
                        topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
                    )
                    # 添加短暂延迟，模拟流式输出
                    await asyncio.sleep(0.1)

            # 发送评审完成消息
            await self.publish_message(
                ResponseMessage(source="测试用例评审智能体", content="测试用例评审完成，开始进行测试用例结构化"),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
            )

            # 将评审结果发送到结构化智能体
            await self.publish_message(
                TestCaseMessage(source="testcase_review_agent", content={
                    "testcase": message.content,
                    "review": review_report
                }),
                topic_id=TopicId(type=testcase_structure_topic_type, source=self.id.key)
            )

        except Exception as e:
            logger.error(f"测试用例评审失败: {str(e)}", exc_info=True)
            await self.publish_message(
                ResponseMessage(source="error", content=f"测试用例评审失败: {str(e)}"),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
            )


@type_subscription(topic_type=testcase_finalize_topic_type)
class TestCaseFinalizeAgent(RoutedAgent):
    def __init__(self):
        super().__init__("testcase finalize agent")
        self._prompt = """
        请严格按如下JSON数组格式输出，必须满足:
        1.首尾无任何多余字符
        2.不使用Markdown代码块
        3.每个测试用例必须包含required字段
        根据用户提供的测试用例及评审报告，根据如下格式生成最终的高质量测试用例。（注意：只输出下面的内容本身，去掉首尾的 ```json 和 ```）：

        [
          {
            "title": "验证用户使用有效凭据登录成功",
            "description": "验证用户能够使用有效的用户名和密码成功登录系统",
            "test_type": "功能测试",
            "priority": "高",
            "status": "未开始",
            "requirement_id": 1,
            "project_id": 1,
            "creator": "张三",
            "preconditions": [
              "系统已部署并正常运行",
              "数据库中存在有效的用户账号"
            ],
            "postconditions": [
              "用户成功登录系统",
              "系统记录登录日志"
            ],
            "test_steps": [
              {
                "description": "打开系统登录页面",
                "expected_result": "系统显示登录界面，包含用户名和密码输入框以及登录按钮"
              },
              {
                "description": "在用户名输入框中输入有效用户名",
                "expected_result": "用户名输入框显示输入的用户名"
              },
              {
                "description": "在密码输入框中输入有效密码",
                "expected_result": "密码输入框显示掩码密码"
              },
              {
                "description": "点击登录按钮",
                "expected_result": "系统验证用户凭据，显示加载指示器"
              },
              {
                "description": "等待系统处理",
                "expected_result": "系统成功验证用户凭据，重定向到系统主页，显示欢迎消息"
              }
            ]
          }
        ]
        """

    @message_handler
    async def handle_message(self, message: TestCaseMessage, ctx: MessageContext) -> None:
        await self.publish_message(ResponseMessage(source="用例结构化智能体", content="正在对测试用例结构化......"),
                                   topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

        try:
            # 解析消息内容，提取需求ID和项目ID
            requirement_id = 7  # 默认值
            project_id = 2      # 默认值

            # 尝试从消息中提取需求ID和项目ID
            try:
                import re
                # 查找包含"需求ID"的行
                if "需求ID" in message.content:
                    req_id_match = re.search(r'需求ID[：:]\s*(\d+)', message.content)
                    if req_id_match:
                        requirement_id = int(req_id_match.group(1))

                # 查找包含"项目ID"的行
                if "项目ID" in message.content:
                    proj_id_match = re.search(r'项目ID[：:]\s*(\d+)', message.content)
                    if proj_id_match:
                        project_id = int(proj_id_match.group(1))
            except Exception as e:
                logger.warning(f"从消息中提取ID失败: {str(e)}")

            # 创建一个简单的配置字典，避免使用model_info
            finalize_config_dict = {
                "name": "testcase_finalize_agent",
                "system_message": self._prompt,
                "llm_config": {
                    "config_list": [{"model": model_client.model}],
                    "temperature": 0.7,
                    "max_tokens": 2000
                }
            }

            # 创建AssistantAgent实例
            finalize_agent = AssistantAgent(**finalize_config_dict)

            # 发送状态消息到前端
            await self.publish_message(
                ResponseMessage(source="用例结构化智能体", content="正在使用AI结构化测试用例..."),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
            )

            # 准备任务内容
            task_content = message.content
            # 添加需求ID和项目ID信息
            task_content += f"\n\n请确保生成的测试用例包含以下信息：\n需求ID: {requirement_id}\n项目ID: {project_id}\n创建者: admin"

            # 使用自定义方法调用LLM，确保传递测试用例内容
            logger.info(f"发送给LLM的结构化任务: {task_content[:200]}...")

            try:
                response = await model_client.run(task=task_content)
                finalize_content = response
                logger.info(f"LLM返回的结构化内容长度: {len(finalize_content)}")

                # 记录结构化内容的前200个字符，用于调试
                if finalize_content:
                    logger.info(f"结构化内容前200个字符: {finalize_content[:200]}...")
            except Exception as api_error:
                logger.error(f"调用LLM API失败: {str(api_error)}", exc_info=True)
                await self.publish_message(
                    ResponseMessage(source="用例结构化智能体", content=f"调用API失败: {str(api_error)}"),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
                )
                # 抛出异常，中断处理流程
                raise Exception(f"调用API失败: {str(api_error)}")

            # 尝试提取JSON部分
            if "```json" in finalize_content:
                parts = finalize_content.split("```json")
                if len(parts) > 1:
                    json_part = parts[1]
                    if "```" in json_part:
                        json_part = json_part.split("```")[0]
                    finalize_content = json_part.strip()
            elif "```" in finalize_content:
                parts = finalize_content.split("```")
                if len(parts) > 1:
                    json_part = parts[1]
                    finalize_content = json_part.strip()

            # 尝试查找JSON对象的开始和结束
            if "[" in finalize_content and "]" in finalize_content:
                start_idx = finalize_content.find("[")
                end_idx = finalize_content.rfind("]")
                if start_idx >= 0 and end_idx > start_idx:
                    finalize_content = finalize_content[start_idx:end_idx+1]

            # 创建消息列表
            messages = [
                {
                    "source": "testcase_finalize_agent",
                    "content": finalize_content,
                    "role": "assistant"
                }
            ]

            # 导入AutoGenAdapter
            from app.agents.autogen_adapter import AutoGenAdapter

            # 创建TaskResult对象
            finalize_result = AutoGenAdapter.create_task_result(messages)

            # 验证JSON格式
            try:
                json.loads(finalize_content)
                logger.info("成功解析结构化后的JSON数据")
            except json.JSONDecodeError as json_err:
                logger.error(f"结构化后的内容不是有效的JSON: {str(json_err)}")
                # 尝试修复JSON格式
                finalize_content = self.fix_json_format(finalize_content, requirement_id, project_id)

            # 发送结构化内容到前端
            await self.publish_message(
                ResponseMessage(source="用例结构化智能体", content="测试用例结构化完成"),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
            )

            # 发送给下一个智能体
            await self.publish_message(
                TestCaseMessage(source=self.id.type, content=finalize_content),
                topic_id=TopicId(testcase_database_topic_type, source=self.id.key)
            )

        except Exception as e:
            logger.error(f"执行测试用例结构化任务失败: {str(e)}", exc_info=True)

            # 发送错误消息到前端
            await self.publish_message(
                ResponseMessage(source="用例结构化智能体", content=f"测试用例结构化过程中出错: {str(e)}"),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
            )

            # 创建一个最小的有效JSON
            finalize_content = f"""
            [
              {{
                "title": "测试用例结构化失败",
                "description": "由于技术原因，无法结构化测试用例",
                "test_type": "功能测试",
                "priority": "高",
                "status": "未开始",
                "requirement_id": {requirement_id},
                "project_id": {project_id},
                "creator": "admin",
                "preconditions": ["系统已部署并正常运行"],
                "postconditions": ["系统正确处理用户输入"],
                "test_steps": [
                  {{
                    "description": "请重新生成测试用例",
                    "expected_result": "系统成功生成测试用例"
                  }}
                ]
              }}
            ]
            """

            # 发送给下一个智能体
            await self.publish_message(
                TestCaseMessage(source=self.id.type, content=finalize_content),
                topic_id=TopicId(testcase_database_topic_type, source=self.id.key)
            )

    def fix_json_format(self, content: str, requirement_id: int, project_id: int) -> str:
        """尝试修复JSON格式"""
        logger.info("尝试修复JSON格式")

        # 如果内容为空，返回一个最小的有效JSON
        if not content or len(content.strip()) == 0:
            return f"""
            [
              {{
                "title": "测试用例结构化失败",
                "description": "由于技术原因，无法结构化测试用例",
                "test_type": "功能测试",
                "priority": "高",
                "status": "未开始",
                "requirement_id": {requirement_id},
                "project_id": {project_id},
                "creator": "admin",
                "preconditions": ["系统已部署并正常运行"],
                "postconditions": ["系统正确处理用户输入"],
                "test_steps": [
                  {{
                    "description": "请重新生成测试用例",
                    "expected_result": "系统成功生成测试用例"
                  }}
                ]
              }}
            ]
            """

        # 尝试修复常见的JSON格式问题
        try:
            # 替换单引号为双引号
            content = content.replace("'", '"')

            # 确保数组开始和结束
            if not content.strip().startswith("["):
                content = "[" + content
            if not content.strip().endswith("]"):
                content = content + "]"

            # 尝试解析修复后的JSON
            json.loads(content)
            logger.info("成功修复并解析JSON数据")
            return content
        except:
            # 如果修复失败，返回一个最小的有效JSON
            logger.error("无法修复JSON格式，使用默认JSON")
            return f"""
            [
              {{
                "title": "测试用例结构化失败",
                "description": "由于技术原因，无法结构化测试用例",
                "test_type": "功能测试",
                "priority": "高",
                "status": "未开始",
                "requirement_id": {requirement_id},
                "project_id": {project_id},
                "creator": "admin",
                "preconditions": ["系统已部署并正常运行"],
                "postconditions": ["系统正确处理用户输入"],
                "test_steps": [
                  {{
                    "description": "请重新生成测试用例",
                    "expected_result": "系统成功生成测试用例"
                  }}
                ]
              }}
            ]
            """

        # 我们已经在try和except块中发送了消息，这里不需要重复发送

@type_subscription(topic_type=testcase_structure_topic_type)
class TestCaseStructureAgent(RoutedAgent):
    """
    测试用例结构化智能体

    负责将测试用例和评审结果转换为结构化的JSON格式
    """
    def __init__(self):
        super().__init__("testcase structure agent")
        self._prompt = """
        请严格按如下JSON数组格式输出，必须满足:
        1.首尾无任何多余字符
        2.不使用Markdown代码块
        3.每个测试用例必须包含required字段

        根据以下测试用例及评审报告，生成最终的高质量测试用例JSON格式：

        JSON格式示例：
        [
          {
            "title": "验证用户使用有效凭据登录成功",
            "description": "验证用户能够使用有效的用户名和密码成功登录系统",
            "test_type": "功能测试",
            "priority": "高",
            "status": "未开始",
            "requirement_id": 1,
            "project_id": 1,
            "creator": "admin",
            "preconditions": [
              "系统已部署并正常运行",
              "数据库中存在有效的用户账号"
            ],
            "postconditions": [
              "用户成功登录系统",
              "系统记录登录日志"
            ],
            "test_steps": [
              {
                "description": "打开系统登录页面",
                "expected_result": "系统显示登录界面，包含用户名和密码输入框以及登录按钮"
              },
              {
                "description": "在用户名输入框中输入有效用户名",
                "expected_result": "用户名输入框显示输入的用户名"
              }
            ]
          }
        ]
        """

    @message_handler
    async def handle_message(self, message: TestCaseMessage, ctx: MessageContext) -> None:
        """处理测试用例结构化消息"""
        await self.publish_message(
            ResponseMessage(source="用例结构化智能体", content="正在对测试用例结构化......"),
            topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
        )

        try:
            # 从消息中提取测试用例和评审报告
            if isinstance(message.content, dict):
                testcase_content = message.content.get("testcase", "")
                review_report = message.content.get("review", "")
            else:
                testcase_content = message.content
                review_report = ""

            # 获取需求ID和项目ID
            requirement_id = ctx.metadata.get("requirement_id", 0) if ctx.metadata else 0
            project_id = ctx.metadata.get("project_id", 0) if ctx.metadata else 0

            # 构建结构化提示词
            finalize_prompt = f"""
            请严格按如下JSON数组格式输出，必须满足:
            1.首尾无任何多余字符
            2.不使用Markdown代码块
            3.每个测试用例必须包含required字段

            根据以下测试用例及评审报告，生成最终的高质量测试用例JSON格式：

            测试用例：
            {testcase_content}

            评审报告：
            {review_report}

            请确保生成的测试用例包含以下信息：
            需求ID: {requirement_id}
            项目ID: {project_id}
            创建者: admin
            """

            # 使用包装器的run方法直接调用
            finalize_content = await model_client.run(task=finalize_prompt)

            # 尝试提取JSON部分
            if "```json" in finalize_content:
                parts = finalize_content.split("```json")
                if len(parts) > 1:
                    json_part = parts[1]
                    if "```" in json_part:
                        json_part = json_part.split("```")[0]
                    finalize_content = json_part.strip()
            elif "```" in finalize_content:
                parts = finalize_content.split("```")
                if len(parts) > 1:
                    json_part = parts[1]
                    finalize_content = json_part.strip()

            # 尝试查找JSON对象的开始和结束
            if "[" in finalize_content and "]" in finalize_content:
                start_idx = finalize_content.find("[")
                end_idx = finalize_content.rfind("]")
                if start_idx >= 0 and end_idx > start_idx:
                    finalize_content = finalize_content[start_idx:end_idx+1]

            # 验证JSON格式
            try:
                json.loads(finalize_content)
                logger.info("成功解析结构化后的JSON数据")
            except json.JSONDecodeError as json_err:
                logger.error(f"结构化后的内容不是有效的JSON: {str(json_err)}")
                # 尝试修复JSON格式
                finalize_content = self.fix_json_format(finalize_content, requirement_id, project_id)

            # 发送结构化完成消息
            await self.publish_message(
                ResponseMessage(source="用例结构化智能体", content="测试用例结构化完成"),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
            )

            # 发送给数据库智能体
            await self.publish_message(
                TestCaseMessage(source=self.id.type, content=finalize_content),
                topic_id=TopicId(type=testcase_database_topic_type, source=self.id.key)
            )

        except Exception as e:
            logger.error(f"测试用例结构化失败: {str(e)}", exc_info=True)
            await self.publish_message(
                ResponseMessage(source="error", content=f"测试用例结构化失败: {str(e)}"),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
            )

            # 创建一个最小的有效JSON
            finalize_content = f"""
            [
              {{
                "title": "测试用例结构化失败",
                "description": "由于技术原因，无法结构化测试用例",
                "test_type": "功能测试",
                "priority": "高",
                "status": "未开始",
                "requirement_id": {requirement_id},
                "project_id": {project_id},
                "creator": "admin",
                "preconditions": ["系统已部署并正常运行"],
                "postconditions": ["系统正确处理用户输入"],
                "test_steps": [
                  {{
                    "description": "请重新生成测试用例",
                    "expected_result": "系统成功生成测试用例"
                  }}
                ]
              }}
            ]
            """

            # 发送给数据库智能体
            await self.publish_message(
                TestCaseMessage(source=self.id.type, content=finalize_content),
                topic_id=TopicId(type=testcase_database_topic_type, source=self.id.key)
            )

    def fix_json_format(self, content: str, requirement_id: int, project_id: int) -> str:
        """尝试修复JSON格式"""
        logger.info("尝试修复JSON格式")

        # 如果内容为空，返回一个最小的有效JSON
        if not content or len(content.strip()) == 0:
            return f"""
            [
              {{
                "title": "测试用例结构化失败",
                "description": "由于技术原因，无法结构化测试用例",
                "test_type": "功能测试",
                "priority": "高",
                "status": "未开始",
                "requirement_id": {requirement_id},
                "project_id": {project_id},
                "creator": "admin",
                "preconditions": ["系统已部署并正常运行"],
                "postconditions": ["系统正确处理用户输入"],
                "test_steps": [
                  {{
                    "description": "请重新生成测试用例",
                    "expected_result": "系统成功生成测试用例"
                  }}
                ]
              }}
            ]
            """

        # 尝试修复常见的JSON格式问题
        try:
            # 替换单引号为双引号
            content = content.replace("'", '"')

            # 确保数组开始和结束
            if not content.strip().startswith("["):
                content = "[" + content
            if not content.strip().endswith("]"):
                content = content + "]"

            # 尝试解析修复后的JSON
            json.loads(content)
            logger.info("成功修复并解析JSON数据")
            return content
        except:
            # 如果修复失败，返回一个最小的有效JSON
            logger.error("无法修复JSON格式，使用默认JSON")
            return f"""
            [
              {{
                "title": "测试用例结构化失败",
                "description": "由于技术原因，无法结构化测试用例",
                "test_type": "功能测试",
                "priority": "高",
                "status": "未开始",
                "requirement_id": {requirement_id},
                "project_id": {project_id},
                "creator": "admin",
                "preconditions": ["系统已部署并正常运行"],
                "postconditions": ["系统正确处理用户输入"],
                "test_steps": [
                  {{
                    "description": "请重新生成测试用例",
                    "expected_result": "系统成功生成测试用例"
                  }}
                ]
              }}
            ]
            """


@type_subscription(topic_type=testcase_database_topic_type)
class TestCaseDatabaseAgent(RoutedAgent):
    def __init__(self):
        super().__init__("testcase database agent")
        self._requirement = None

    @message_handler
    async def handle_message(self, message: TestCaseMessage, ctx: MessageContext) -> None:
        try:
            await self.publish_message(ResponseMessage(source="数据库智能体", content="正在验证测试用例数据......"),
                                       topic_id=TopicId(type=task_result_topic_type, source=self.id.key))

            # 解析JSON数据
            try:
                # 尝试解析JSON数据
                test_cases_data = json.loads(message.content)
                logger.info(f"成功解析测试用例JSON数据，共 {len(test_cases_data)} 条")

                # 导入CaseCreate模型
                from app.schemas.testcase import CaseCreate, TestStep

                # 手动转换为CaseCreate对象
                test_cases = []
                for tc_data in test_cases_data:
                    # 处理前置条件和后置条件
                    preconditions = tc_data.get("preconditions", [])
                    if isinstance(preconditions, str):
                        preconditions = preconditions.split("\\n") if preconditions else []

                    postconditions = tc_data.get("postconditions", [])
                    if isinstance(postconditions, str):
                        postconditions = postconditions.split("\\n") if postconditions else []

                    # 处理测试步骤
                    steps_data = tc_data.get("test_steps", [])
                    test_steps = []
                    for step in steps_data:
                        test_steps.append(TestStep(
                            description=step.get("description", ""),
                            expected_result=step.get("expected_result", "")
                        ))

                    # 创建CaseCreate对象
                    test_case = CaseCreate(
                        title=tc_data.get("title", ""),
                        description=tc_data.get("description", ""),
                        test_type=tc_data.get("test_type", ""),
                        priority=tc_data.get("priority", ""),
                        status=tc_data.get("status", "未开始"),
                        requirement_id=tc_data.get("requirement_id", 0),
                        project_id=tc_data.get("project_id", 0),
                        creator=tc_data.get("creator", ""),
                        preconditions=preconditions,
                        postconditions=postconditions,
                        test_steps=test_steps
                    )
                    test_cases.append(test_case)

                logger.info(f"成功转换测试用例数据，共 {len(test_cases)} 条")

                # 将测试用例数据转换为JSON格式并发送到前端
                try:
                    # 将CaseCreate对象转换为字典
                    test_cases_json = json.dumps([
                        {
                            "title": tc.title,
                            "description": tc.description,
                            "test_type": tc.test_type,
                            "priority": tc.priority,
                            "status": tc.status,
                            "requirement_id": tc.requirement_id,
                            "project_id": tc.project_id,
                            "creator": tc.creator,
                            "preconditions": tc.preconditions,
                            "postconditions": tc.postconditions,
                            "test_steps": [
                                {
                                    "description": step.description,
                                    "expected_result": step.expected_result
                                } for step in tc.test_steps
                            ]
                        } for tc in test_cases
                    ])

                    logger.info(f"测试用例JSON数据长度: {len(test_cases_json)}")
                    await self.publish_message(
                        ResponseMessage(source="generated_testcases",
                                       content=test_cases_json,
                                       is_final=False),
                        topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
                    )
                except Exception as json_error:
                    logger.error(f"序列化测试用例数据失败: {str(json_error)}", exc_info=True)
                    await self.publish_message(
                        ResponseMessage(source="数据库智能体",
                                      content=f"序列化测试用例数据失败: {str(json_error)}",
                                      is_final=False),
                        topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
                    )

                # 发送最终消息
                await self.publish_message(
                    ResponseMessage(source="数据库智能体",
                                   content=f"测试用例生成完成，共生成【{len(test_cases)}】条测试用例。请点击'保存测试用例'按钮将其保存到数据库。",
                                   is_final=True),
                    topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
                )

                logger.info(f"测试用例生成完成，共 {len(test_cases)} 条，等待用户确认保存")

            except Exception as json_error:
                logger.error(f"解析测试用例JSON数据失败: {str(json_error)}", exc_info=True)
                await self.publish_message(ResponseMessage(source="数据库智能体",
                                                          content=f"解析测试用例数据失败: {str(json_error)}",
                                                          is_final=True),
                                          topic_id=TopicId(type=task_result_topic_type, source=self.id.key))
                return

        except Exception as e:
            logger.error(f"测试用例处理失败: {str(e)}", exc_info=True)
            await self.publish_message(
                ResponseMessage(source="数据库智能体",
                               content=f"测试用例处理失败：{str(e)}",
                               is_final=True),
                topic_id=TopicId(type=task_result_topic_type, source=self.id.key)
            )



async def start_runtime(requirement: RequirementMessage,
                        collect_result: Callable[[ClosureContext, ResponseMessage, MessageContext], Awaitable[None]],
                        user_input_func: Callable[[str, Optional[CancellationToken]], Awaitable[str]]):
    # 创建运行时并注册智能体
    runtime = SingleThreadedAgentRuntime()

    # 注册所有智能体
    await TestCaseGeneratorAgent.register(
        runtime,
        testcase_generator_topic_type,
        lambda: TestCaseGeneratorAgent(input_func=user_input_func)
    )

    await TestCaseReviewAgent.register(
        runtime,
        testcase_review_topic_type,
        lambda: TestCaseReviewAgent()
    )

    await TestCaseFinalizeAgent.register(
        runtime,
        testcase_finalize_topic_type,
        lambda: TestCaseFinalizeAgent()
    )

    await TestCaseDatabaseAgent.register(
        runtime,
        testcase_database_topic_type,
        lambda: TestCaseDatabaseAgent()
    )

    # 注册结果收集智能体
    await ClosureAgent.register_closure(
        runtime,
        "closure_agent",
        collect_result,
        subscriptions=lambda: [TypeSubscription(
            topic_type=task_result_topic_type,
            agent_type="closure_agent"
        )]
    )

    # 启动运行时并发送初始消息
    runtime.start()
    await runtime.publish_message(
        requirement,
        topic_id=DefaultTopicId(type=testcase_generator_topic_type)
    )

    # 等待任务完成
    await runtime.stop_when_idle()
    await runtime.close()

