from fastapi import APIRouter

from app.api.endpoints import users, projects, requirements, testcases, requirements_batch, api_tests, performance_tests

api_router = APIRouter()
api_router.include_router(users.router, tags=["users"])
api_router.include_router(projects.router, tags=["projects"])
api_router.include_router(requirements.router, tags=["requirements"])
api_router.include_router(requirements_batch.router, tags=["requirements"])
api_router.include_router(testcases.router, tags=["testcases"])
# 启用API测试功能
api_router.include_router(api_tests.router, tags=["api_tests"])
# 启用性能测试功能
api_router.include_router(performance_tests.router, tags=["performance_tests"])
