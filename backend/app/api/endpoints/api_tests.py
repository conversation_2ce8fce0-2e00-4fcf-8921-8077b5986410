"""
接口测试API端点

提供接口文档管理、测试用例生成、脚本生成和测试执行的API接口
"""

import json
import io
import pandas as pd
from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Query, BackgroundTasks, Form
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.api_test import ApiDocument, ApiTestCase, ApiTestScript
from app.schemas.api_test import (
    ApiDocument as ApiDocumentSchema,
    ApiDocumentCreate,
    ApiDocumentUpdate,
    ApiTestCase as ApiTestCaseSchema,
    ApiTestCaseCreate,
    ApiTestCaseUpdate,
    ApiTestScript as ApiTestScriptSchema,
    ApiTestScriptCreate,
    ApiTestScriptUpdate,
    GeneratePythonScriptRequest,
    ExportApiTestCasesRequest,
    ApiTestResultResponse
)
from app.services import api_test_service, project_service
from app.agents.api_test_agents import (
    parse_api_document_with_ai,
    generate_api_testcases_with_ai,
    generate_python_script_with_ai,
    execute_api_test,
    generate_api_testcases_from_document
)
from app.utils.script_generator import PythonScriptGenerator

router = APIRouter()


# ==================== 接口文档管理 ====================

@router.get("/api-documents", response_model=List[ApiDocumentSchema])
def get_api_documents(
    *,
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    project_id: Optional[int] = Query(None),
    title: Optional[str] = Query(None),
    format_type: Optional[str] = Query(None),
) -> Any:
    """
    获取接口文档列表
    """
    documents = api_test_service.get_api_documents(
        db=db,
        skip=skip,
        limit=limit,
        project_id=project_id,
        title=title,
        format_type=format_type
    )
    return documents


@router.get("/api-documents/{document_id}", response_model=ApiDocumentSchema)
def get_api_document(
    *,
    db: Session = Depends(get_db),
    document_id: int,
) -> Any:
    """
    获取接口文档详情
    """
    document = api_test_service.get_api_document(db=db, document_id=document_id)
    if not document:
        raise HTTPException(status_code=404, detail="接口文档不存在")
    return document


@router.post("/api-documents", response_model=ApiDocumentSchema)
def create_api_document(
    *,
    db: Session = Depends(get_db),
    document_in: ApiDocumentCreate,
) -> Any:
    """
    创建接口文档
    """
    # 检查项目是否存在
    project = project_service.get_project(db, project_id=document_in.project_id)
    if not project:
        raise HTTPException(status_code=404, detail="项目不存在")

    document = api_test_service.create_api_document(db=db, document=document_in)
    return document


@router.post("/api-documents/upload")
async def upload_api_document(
    *,
    db: Session = Depends(get_db),
    file: UploadFile = File(...),
    project_id: int = Form(...),
    title: Optional[str] = Form(None),
    description: Optional[str] = Form(None),
    creator: str = Form("admin")
) -> Any:
    """
    上传接口文档文件
    """
    # 检查项目是否存在
    project = project_service.get_project(db, project_id=project_id)
    if not project:
        raise HTTPException(status_code=404, detail="项目不存在")

    # 读取文件内容
    content = await file.read()
    
    # 根据文件扩展名确定格式类型
    filename = file.filename or ""
    if filename.endswith('.json'):
        format_type = "openapi"
        try:
            # 验证JSON格式
            json.loads(content.decode('utf-8'))
            content_str = content.decode('utf-8')
        except:
            raise HTTPException(status_code=400, detail="无效的JSON文件")
    elif filename.endswith('.yaml') or filename.endswith('.yml'):
        format_type = "openapi"
        content_str = content.decode('utf-8')
    else:
        raise HTTPException(status_code=400, detail="不支持的文件格式，请上传JSON或YAML文件")

    # 创建文档
    document_in = ApiDocumentCreate(
        title=title or filename,
        description=description,
        format_type=format_type,
        content=content_str,
        project_id=project_id,
        creator=creator
    )
    
    document = api_test_service.create_api_document(db=db, document=document_in)
    return {"message": "文档上传成功", "document_id": document.id}


@router.put("/api-documents/{document_id}", response_model=ApiDocumentSchema)
def update_api_document(
    *,
    db: Session = Depends(get_db),
    document_id: int,
    document_in: ApiDocumentUpdate,
) -> Any:
    """
    更新接口文档
    """
    document = api_test_service.update_api_document(
        db=db, document_id=document_id, document=document_in
    )
    if not document:
        raise HTTPException(status_code=404, detail="接口文档不存在")
    return document


@router.delete("/api-documents/{document_id}")
def delete_api_document(
    *,
    db: Session = Depends(get_db),
    document_id: int,
) -> Any:
    """
    删除接口文档
    """
    document = api_test_service.delete_api_document(db=db, document_id=document_id)
    if not document:
        raise HTTPException(status_code=404, detail="接口文档不存在")
    return {"message": "接口文档删除成功"}


# ==================== AI解析和生成 ====================

@router.post("/api-documents/{document_id}/parse")
async def parse_api_document(
    *,
    db: Session = Depends(get_db),
    document_id: int,
    background_tasks: BackgroundTasks,
) -> Any:
    """
    使用AI解析接口文档
    """
    document = api_test_service.get_api_document(db=db, document_id=document_id)
    if not document:
        raise HTTPException(status_code=404, detail="接口文档不存在")

    # 添加后台任务进行解析
    background_tasks.add_task(parse_api_document_with_ai, document)
    
    return {"message": "开始解析接口文档，请稍候..."}


@router.post("/api-documents/{document_id}/generate-testcases")
async def generate_testcases_from_document(
    *,
    db: Session = Depends(get_db),
    document_id: int,
    background_tasks: BackgroundTasks,
) -> Any:
    """
    根据接口文档生成测试用例
    """
    document = api_test_service.get_api_document(db=db, document_id=document_id)
    if not document:
        raise HTTPException(status_code=404, detail="接口文档不存在")

    # 添加后台任务进行生成
    background_tasks.add_task(
        generate_api_testcases_with_ai,
        document.id,
        document.project_id,
        document.content
    )
    
    return {"message": "开始生成测试用例，请稍候..."}


# ==================== 测试用例管理 ====================

@router.get("/api-testcases", response_model=List[ApiTestCaseSchema])
def get_api_testcases(
    *,
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    project_id: Optional[int] = Query(None),
    api_document_id: Optional[int] = Query(None),
    title: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
) -> Any:
    """
    获取接口测试用例列表
    """
    testcases = api_test_service.get_api_testcases(
        db=db,
        skip=skip,
        limit=limit,
        project_id=project_id,
        api_document_id=api_document_id,
        title=title,
        status=status
    )
    return testcases


@router.get("/api-testcases/{testcase_id}", response_model=ApiTestCaseSchema)
def get_api_testcase(
    *,
    db: Session = Depends(get_db),
    testcase_id: int,
) -> Any:
    """
    获取接口测试用例详情
    """
    testcase = api_test_service.get_api_testcase(db=db, testcase_id=testcase_id)
    if not testcase:
        raise HTTPException(status_code=404, detail="测试用例不存在")
    return testcase


@router.post("/api-testcases", response_model=ApiTestCaseSchema)
def create_api_testcase(
    *,
    db: Session = Depends(get_db),
    testcase_in: ApiTestCaseCreate,
) -> Any:
    """
    创建接口测试用例
    """
    # 检查文档是否存在
    document = api_test_service.get_api_document(db, document_id=testcase_in.api_document_id)
    if not document:
        raise HTTPException(status_code=404, detail="接口文档不存在")

    testcase = api_test_service.create_api_testcase(db=db, testcase=testcase_in)
    return testcase


@router.put("/api-testcases/{testcase_id}", response_model=ApiTestCaseSchema)
def update_api_testcase(
    *,
    db: Session = Depends(get_db),
    testcase_id: int,
    testcase_in: ApiTestCaseUpdate,
) -> Any:
    """
    更新接口测试用例
    """
    testcase = api_test_service.update_api_testcase(
        db=db, testcase_id=testcase_id, testcase=testcase_in
    )
    if not testcase:
        raise HTTPException(status_code=404, detail="测试用例不存在")
    return testcase


@router.delete("/api-testcases/{testcase_id}")
def delete_api_testcase(
    *,
    db: Session = Depends(get_db),
    testcase_id: int,
) -> Any:
    """
    删除接口测试用例
    """
    testcase = api_test_service.delete_api_testcase(db=db, testcase_id=testcase_id)
    if not testcase:
        raise HTTPException(status_code=404, detail="测试用例不存在")
    return {"message": "测试用例删除成功"}


# ==================== Python脚本生成 ====================

@router.post("/generate-python-script")
async def generate_python_script(
    *,
    db: Session = Depends(get_db),
    request: GeneratePythonScriptRequest,
) -> Any:
    """
    生成Python测试脚本
    """
    # 检查测试用例是否存在
    testcases = api_test_service.get_api_testcases_by_ids(db, request.testcase_ids)
    if len(testcases) != len(request.testcase_ids):
        raise HTTPException(status_code=404, detail="部分测试用例不存在")

    try:
        # 使用脚本生成器生成脚本
        generator = PythonScriptGenerator()
        script_content = generator.generate_script(testcases, request.script_type)

        # 保存脚本到数据库
        script_title = request.title or f"{request.script_type}_test_script"
        script_description = request.description or f"基于{len(testcases)}个测试用例生成的{request.script_type}测试脚本"

        script_in = ApiTestScriptCreate(
            title=script_title,
            description=script_description,
            script_type=request.script_type,
            script_content=script_content,
            requirements=f"{request.script_type}\nrequests\njson",
            project_id=request.project_id,
            creator=request.creator
        )

        script = api_test_service.create_api_test_script(db=db, script=script_in)

        return {
            "message": f"成功生成{request.script_type}脚本",
            "script_id": script.id,
            "script_content": script_content
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成脚本失败: {str(e)}")


@router.get("/api-test-scripts", response_model=List[ApiTestScriptSchema])
def get_api_test_scripts(
    *,
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    project_id: Optional[int] = Query(None),
    script_type: Optional[str] = Query(None),
    title: Optional[str] = Query(None),
) -> Any:
    """
    获取测试脚本列表
    """
    scripts = api_test_service.get_api_test_scripts(
        db=db,
        skip=skip,
        limit=limit,
        project_id=project_id,
        script_type=script_type,
        title=title
    )
    return scripts


@router.get("/api-test-scripts/{script_id}", response_model=ApiTestScriptSchema)
def get_api_test_script(
    *,
    db: Session = Depends(get_db),
    script_id: int,
) -> Any:
    """
    获取测试脚本详情
    """
    script = api_test_service.get_api_test_script(db=db, script_id=script_id)
    if not script:
        raise HTTPException(status_code=404, detail="测试脚本不存在")
    return script


@router.post("/api-test-scripts", response_model=ApiTestScriptSchema)
def create_api_test_script(
    *,
    db: Session = Depends(get_db),
    script_in: ApiTestScriptCreate,
) -> Any:
    """
    创建测试脚本
    """
    script = api_test_service.create_api_test_script(db=db, script=script_in)
    return script


@router.put("/api-test-scripts/{script_id}", response_model=ApiTestScriptSchema)
def update_api_test_script(
    *,
    db: Session = Depends(get_db),
    script_id: int,
    script_in: ApiTestScriptUpdate,
) -> Any:
    """
    更新测试脚本
    """
    script = api_test_service.update_api_test_script(
        db=db, script_id=script_id, script=script_in
    )
    if not script:
        raise HTTPException(status_code=404, detail="测试脚本不存在")
    return script


@router.delete("/api-test-scripts/{script_id}")
def delete_api_test_script(
    *,
    db: Session = Depends(get_db),
    script_id: int,
) -> Any:
    """
    删除测试脚本
    """
    script = api_test_service.delete_api_test_script(db=db, script_id=script_id)
    if not script:
        raise HTTPException(status_code=404, detail="测试脚本不存在")
    return {"message": "测试脚本删除成功"}


@router.get("/api-test-scripts/{script_id}/download")
def download_api_test_script(
    *,
    db: Session = Depends(get_db),
    script_id: int,
) -> Any:
    """
    下载测试脚本
    """
    script = api_test_service.get_api_test_script(db=db, script_id=script_id)
    if not script:
        raise HTTPException(status_code=404, detail="测试脚本不存在")

    # 创建文件流
    script_content = script.script_content.encode('utf-8')
    script_io = io.BytesIO(script_content)

    # 确定文件扩展名
    extension = "py"
    if script.script_type == "pytest":
        filename = f"test_{script.title}.py"
    elif script.script_type == "unittest":
        filename = f"test_{script.title}.py"
    else:
        filename = f"{script.title}.py"

    return StreamingResponse(
        io.BytesIO(script_content),
        media_type="application/octet-stream",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )


# ==================== 测试执行 ====================

@router.post("/api-testcases/execute")
async def execute_api_testcases(
    *,
    db: Session = Depends(get_db),
    testcase_ids: List[int],
    environment: str = "test",
    base_url: Optional[str] = None,
    executed_by: str = "admin",
    background_tasks: BackgroundTasks,
) -> Any:
    """
    执行接口测试用例
    """
    # 检查测试用例是否存在
    testcases = api_test_service.get_api_testcases_by_ids(db, testcase_ids)
    if len(testcases) != len(testcase_ids):
        raise HTTPException(status_code=404, detail="部分测试用例不存在")

    # 添加后台任务进行执行
    background_tasks.add_task(
        execute_api_test,
        testcases,
        environment,
        base_url,
        executed_by,
        db
    )

    return {"message": f"开始执行{len(testcases)}个测试用例，请稍候..."}


# ==================== 导出功能 ====================

@router.post("/api-testcases/export")
def export_api_testcases(
    *,
    db: Session = Depends(get_db),
    request: ExportApiTestCasesRequest,
) -> Any:
    """
    导出接口测试用例
    """
    # 获取测试用例
    if request.testcase_ids:
        testcases = api_test_service.get_api_testcases_by_ids(db, request.testcase_ids)
    else:
        testcases = api_test_service.get_api_testcases(
            db=db,
            project_id=request.project_id,
            limit=1000  # 限制导出数量
        )

    if not testcases:
        raise HTTPException(status_code=404, detail="没有找到测试用例")

    # 转换为字典格式
    testcases_data = api_test_service.export_api_testcases_to_dict(testcases)

    if request.format_type == "excel":
        # 导出为Excel
        df = pd.DataFrame(testcases_data)
        excel_buffer = io.BytesIO()
        df.to_excel(excel_buffer, index=False, engine='openpyxl')
        excel_buffer.seek(0)

        return StreamingResponse(
            io.BytesIO(excel_buffer.getvalue()),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": "attachment; filename=api_testcases.xlsx"}
        )

    elif request.format_type == "csv":
        # 导出为CSV
        df = pd.DataFrame(testcases_data)
        csv_buffer = io.StringIO()
        df.to_csv(csv_buffer, index=False, encoding='utf-8')
        csv_content = csv_buffer.getvalue().encode('utf-8')

        return StreamingResponse(
            io.BytesIO(csv_content),
            media_type="text/csv",
            headers={"Content-Disposition": "attachment; filename=api_testcases.csv"}
        )

    elif request.format_type == "json":
        # 导出为JSON
        json_content = json.dumps(testcases_data, ensure_ascii=False, indent=2).encode('utf-8')

        return StreamingResponse(
            io.BytesIO(json_content),
            media_type="application/json",
            headers={"Content-Disposition": "attachment; filename=api_testcases.json"}
        )

    else:
        raise HTTPException(status_code=400, detail="不支持的导出格式")


# ==================== 导入功能 ====================

@router.post("/api-testcases/import")
async def import_api_testcases(
    *,
    db: Session = Depends(get_db),
    file: UploadFile = File(...),
    project_id: int,
    api_document_id: int,
    creator: str = "admin"
) -> Any:
    """
    导入接口测试用例
    """
    # 检查项目和文档是否存在
    project = project_service.get_project(db, project_id=project_id)
    if not project:
        raise HTTPException(status_code=404, detail="项目不存在")

    document = api_test_service.get_api_document(db, document_id=api_document_id)
    if not document:
        raise HTTPException(status_code=404, detail="接口文档不存在")

    # 读取文件内容
    content = await file.read()
    filename = file.filename or ""

    try:
        if filename.endswith('.xlsx') or filename.endswith('.xls'):
            # 读取Excel文件
            df = pd.read_excel(io.BytesIO(content))
        elif filename.endswith('.csv'):
            # 读取CSV文件
            df = pd.read_csv(io.BytesIO(content))
        elif filename.endswith('.json'):
            # 读取JSON文件
            data = json.loads(content.decode('utf-8'))
            df = pd.DataFrame(data)
        else:
            raise HTTPException(status_code=400, detail="不支持的文件格式")

        # 验证必要字段
        required_fields = ['title', 'api_path', 'method', 'expected_status']
        missing_fields = [field for field in required_fields if field not in df.columns]
        if missing_fields:
            raise HTTPException(
                status_code=400,
                detail=f"缺少必要字段: {', '.join(missing_fields)}"
            )

        # 创建测试用例
        created_count = 0
        for _, row in df.iterrows():
            testcase_data = {
                "title": row.get('title', ''),
                "description": row.get('description', ''),
                "api_path": row.get('api_path', ''),
                "method": row.get('method', 'GET'),
                "headers": json.loads(row.get('headers', '{}')) if pd.notna(row.get('headers')) else {},
                "params": json.loads(row.get('params', '{}')) if pd.notna(row.get('params')) else {},
                "body": json.loads(row.get('body', '{}')) if pd.notna(row.get('body')) else {},
                "expected_status": int(row.get('expected_status', 200)),
                "expected_response": json.loads(row.get('expected_response', '{}')) if pd.notna(row.get('expected_response')) else {},
                "validation_rules": json.loads(row.get('validation_rules', '[]')) if pd.notna(row.get('validation_rules')) else [],
                "test_data": json.loads(row.get('test_data', '{}')) if pd.notna(row.get('test_data')) else {},
                "priority": row.get('priority', '中'),
                "tags": json.loads(row.get('tags', '[]')) if pd.notna(row.get('tags')) else [],
                "api_document_id": api_document_id,
                "project_id": project_id,
                "status": row.get('status', '未执行'),
                "creator": creator
            }

            testcase_in = ApiTestCaseCreate(**testcase_data)
            api_test_service.create_api_testcase(db=db, testcase=testcase_in)
            created_count += 1

        return {"message": f"成功导入{created_count}个测试用例"}

    except Exception as e:
        raise HTTPException(status_code=400, detail=f"导入失败: {str(e)}")
