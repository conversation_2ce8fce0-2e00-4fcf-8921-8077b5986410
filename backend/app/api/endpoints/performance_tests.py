"""
性能测试API端点

提供性能测试用例的CRUD操作、生成、执行等功能
"""
from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Response
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.performance_test import PerformanceTestCase
from app.schemas.performance_test import (
    PerformanceTestCaseCreate,
    PerformanceTestCaseUpdate,
    PerformanceTestCaseResponse,
    PerformanceTestExecutionCreate,
    PerformanceTestExecutionResponse
)
from app.services import performance_test_service, requirement_service
from app.agents.performance_test_agents import (
    generate_performance_testcases_from_requirement,
    execute_performance_testcases
)

router = APIRouter()


# ==================== 性能测试用例CRUD ====================

@router.get("/performance-testcases", response_model=List[PerformanceTestCaseResponse])
def get_performance_testcases(
    *,
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    project_id: Optional[int] = None,
    requirement_id: Optional[int] = None,
    title: Optional[str] = None,
    test_type: Optional[str] = None,
    status: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    response: Response,
) -> Any:
    """
    获取性能测试用例列表
    """
    # 获取总数
    total = performance_test_service.get_performance_testcases_count(
        db=db,
        project_id=project_id,
        requirement_id=requirement_id,
        title=title,
        test_type=test_type,
        status=status,
        start_date=start_date,
        end_date=end_date
    )
    
    # 设置响应头
    response.headers["X-Total-Count"] = str(total)
    
    # 获取数据
    testcases = performance_test_service.get_performance_testcases(
        db=db,
        skip=skip,
        limit=limit,
        project_id=project_id,
        requirement_id=requirement_id,
        title=title,
        test_type=test_type,
        status=status,
        start_date=start_date,
        end_date=end_date
    )
    
    return testcases


@router.get("/performance-testcases/{testcase_id}", response_model=PerformanceTestCaseResponse)
def get_performance_testcase(
    *,
    db: Session = Depends(get_db),
    testcase_id: int,
) -> Any:
    """
    获取性能测试用例详情
    """
    testcase = performance_test_service.get_performance_testcase(db=db, testcase_id=testcase_id)
    if not testcase:
        raise HTTPException(status_code=404, detail="性能测试用例不存在")
    return testcase


@router.post("/performance-testcases", response_model=PerformanceTestCaseResponse)
def create_performance_testcase(
    *,
    db: Session = Depends(get_db),
    testcase_in: PerformanceTestCaseCreate,
) -> Any:
    """
    创建性能测试用例
    """
    testcase = performance_test_service.create_performance_testcase(db=db, testcase=testcase_in)
    return testcase


@router.put("/performance-testcases/{testcase_id}", response_model=PerformanceTestCaseResponse)
def update_performance_testcase(
    *,
    db: Session = Depends(get_db),
    testcase_id: int,
    testcase_in: PerformanceTestCaseUpdate,
) -> Any:
    """
    更新性能测试用例
    """
    testcase = performance_test_service.get_performance_testcase(db=db, testcase_id=testcase_id)
    if not testcase:
        raise HTTPException(status_code=404, detail="性能测试用例不存在")
    
    testcase = performance_test_service.update_performance_testcase(
        db=db, testcase_id=testcase_id, testcase=testcase_in
    )
    return testcase


@router.delete("/performance-testcases/{testcase_id}")
def delete_performance_testcase(
    *,
    db: Session = Depends(get_db),
    testcase_id: int,
) -> Any:
    """
    删除性能测试用例
    """
    testcase = performance_test_service.get_performance_testcase(db=db, testcase_id=testcase_id)
    if not testcase:
        raise HTTPException(status_code=404, detail="性能测试用例不存在")
    
    performance_test_service.delete_performance_testcase(db=db, testcase_id=testcase_id)
    return {"message": "性能测试用例删除成功"}


# ==================== 性能测试用例生成 ====================

@router.post("/performance-testcases/generate")
def generate_performance_testcases(
    *,
    db: Session = Depends(get_db),
    requirement_id: int,
    project_id: int,
    creator: str = "admin",
) -> Any:
    """
    根据需求生成性能测试用例
    """
    # 检查需求是否存在
    requirement = requirement_service.get_requirement(db=db, requirement_id=requirement_id)
    if not requirement:
        raise HTTPException(status_code=404, detail="需求不存在")
    
    # 生成性能测试用例
    try:
        testcases = generate_performance_testcases_from_requirement(
            requirement=requirement,
            db=db,
            creator=creator
        )
        
        return {
            "message": f"成功生成{len(testcases)}个性能测试用例",
            "testcases_count": len(testcases),
            "testcase_ids": [tc.id for tc in testcases],
            "requirement_id": requirement_id
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成性能测试用例失败: {str(e)}")


# ==================== 性能测试执行 ====================

@router.post("/performance-testcases/execute")
async def execute_performance_testcases_endpoint(
    *,
    db: Session = Depends(get_db),
    testcase_ids: List[int],
    environment: str = "test",
    executed_by: str = "admin",
    background_tasks: BackgroundTasks,
) -> Any:
    """
    执行性能测试用例
    """
    # 检查测试用例是否存在
    testcases = []
    for testcase_id in testcase_ids:
        testcase = performance_test_service.get_performance_testcase(db=db, testcase_id=testcase_id)
        if not testcase:
            raise HTTPException(status_code=404, detail=f"性能测试用例 {testcase_id} 不存在")
        testcases.append(testcase)
    
    # 添加后台任务进行执行
    background_tasks.add_task(
        execute_performance_testcases,
        testcases,
        environment,
        executed_by,
        db
    )
    
    return {"message": f"开始执行{len(testcases)}个性能测试用例，请稍候..."}


# ==================== 性能测试执行结果 ====================

@router.get("/performance-testcases/{testcase_id}/executions", response_model=List[PerformanceTestExecutionResponse])
def get_performance_test_executions(
    *,
    db: Session = Depends(get_db),
    testcase_id: int,
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    获取性能测试执行结果列表
    """
    # 检查测试用例是否存在
    testcase = performance_test_service.get_performance_testcase(db=db, testcase_id=testcase_id)
    if not testcase:
        raise HTTPException(status_code=404, detail="性能测试用例不存在")
    
    executions = performance_test_service.get_performance_test_executions(
        db=db, testcase_id=testcase_id, skip=skip, limit=limit
    )
    
    return executions


@router.get("/performance-executions/{execution_id}", response_model=PerformanceTestExecutionResponse)
def get_performance_test_execution(
    *,
    db: Session = Depends(get_db),
    execution_id: int,
) -> Any:
    """
    获取性能测试执行结果详情
    """
    execution = performance_test_service.get_performance_test_execution(db=db, execution_id=execution_id)
    if not execution:
        raise HTTPException(status_code=404, detail="性能测试执行结果不存在")
    return execution
