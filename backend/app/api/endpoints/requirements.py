from typing import Any, List, Optional
import os
import uuid
import json
import io
import csv
import datetime
from pathlib import Path

from fastapi import APIRouter, Depends, HTTPException, Query, File, UploadFile, WebSocket, WebSocketDisconnect, Form
from starlette.websockets import WebSocketState
from sqlalchemy.orm import Session
from starlette.responses import JSONResponse, StreamingResponse
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side

from app.db.session import get_db
from app.services import requirement_service, project_service
from app.schemas.requirement import Requirement, RequirementCreate, RequirementUpdate, RequirementAnalysis, RequirementImport
from app.schemas import Success

# Import AI agents
from app.api.agent.requirement_agents import RequirementFilesMessage, ResponseMessage, start_runtime
from autogen_agentchat.messages import TextMessage
from autogen_core import CancellationToken, ClosureContext, MessageContext

router = APIRouter()


@router.get("/requirements/export", name="export_requirements")
def export_requirements(
    project_id: Optional[int] = None,
    title: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Export requirements to Excel file.
    """
    try:
        # 打印调试信息
        print(f"导出需求，参数: project_id={project_id}, title={title}, start_date={start_date}, end_date={end_date}")
        print(f"参数类型: project_id={type(project_id)}, title={type(title)}, start_date={type(start_date)}, end_date={type(end_date)}")

        # 参数处理
        if project_id and not isinstance(project_id, int):
            try:
                project_id = int(project_id)
                print(f"转换project_id为整数: {project_id}")
            except (ValueError, TypeError) as e:
                print(f"project_id转换失败: {str(e)}")
                project_id = None

        # 获取需求列表
        print("开始查询需求列表...")
        requirements = requirement_service.get_requirements(
            db,
            skip=0,
            limit=1000,  # 限制导出数量
            project_id=project_id,
            title=title,
            start_date=start_date,
            end_date=end_date
        )

        print(f"查询到 {len(requirements)} 条需求记录")

        # 创建Excel工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = "需求列表"

        # 设置表头
        headers = ["ID", "需求标题", "所属项目", "需求描述", "类别", "审核人", "创建时间"]
        for col_num, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col_num)
            cell.value = header
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center', vertical='center')

        # 填充数据
        for row_num, req in enumerate(requirements, 2):
            try:
                # ID
                ws.cell(row=row_num, column=1).value = req.id

                # 需求标题
                ws.cell(row=row_num, column=2).value = req.title

                # 所属项目
                ws.cell(row=row_num, column=3).value = req.project.name if req.project else ""

                # 需求描述
                ws.cell(row=row_num, column=4).value = req.description

                # 类别
                ws.cell(row=row_num, column=5).value = req.category

                # 审核人
                ws.cell(row=row_num, column=6).value = req.reviewer

                # 创建时间
                ws.cell(row=row_num, column=7).value = req.created_at.strftime("%Y-%m-%d") if req.created_at else ""

            except Exception as cell_error:
                print(f"处理第 {row_num} 行数据时出错: {str(cell_error)}")
                # 继续处理下一行

        # 调整列宽
        column_widths = [10, 30, 20, 50, 15, 15, 15]
        for i, width in enumerate(column_widths):
            ws.column_dimensions[chr(65 + i)].width = width

        # 保存到内存
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        # 生成文件名 - 使用英文文件名避免编码问题
        current_time = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        filename = f"requirements_list_{current_time}.xlsx"

        # 返回文件
        headers = {
            'Content-Disposition': f'attachment; filename="{filename}"',
            'Access-Control-Expose-Headers': 'Content-Disposition'
        }

        print(f"导出成功，文件名: {filename}")

        return StreamingResponse(
            output,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers=headers
        )
    except Exception as e:
        import traceback
        traceback.print_exc()
        error_msg = f"导出需求失败: {str(e)}"
        print(error_msg)
        print("错误堆栈:")
        traceback.print_exc()

        # 检查是否是pandas或openpyxl相关错误
        if "pandas" in str(e).lower() or "openpyxl" in str(e).lower():
            error_msg = f"导出Excel文件失败，可能是依赖库问题: {str(e)}"

        raise HTTPException(
            status_code=500,
            detail=error_msg
        )


@router.get("/requirements", response_model=List[Requirement])
def read_requirements(
    skip: int = 0,
    limit: int = 100,
    project_id: Optional[int] = None,
    title: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    db: Session = Depends(get_db),
) -> Any:
    """
    Retrieve requirements with optional filtering.
    """
    try:
        print(f"Retrieving requirements with parameters: skip={skip}, limit={limit}, project_id={project_id}, title={title}, start_date={start_date}, end_date={end_date}")

        # 获取需求列表
        requirements = requirement_service.get_requirements(
            db,
            skip=skip,
            limit=limit,
            project_id=project_id,
            title=title,
            start_date=start_date,
            end_date=end_date
        )

        # 获取总数
        total_count = requirement_service.get_requirements_count(
            db,
            project_id=project_id,
            title=title,
            start_date=start_date,
            end_date=end_date
        )

        print(f"Total requirements count: {total_count}")

        # 手动将项目信息添加到响应中
        result = []
        for req in requirements:
            # 创建一个字典，包含需求的所有属性
            req_dict = {
                "id": req.id,
                "project_id": req.project_id,
                "title": req.title,
                "description": req.description,
                "refined_content": req.refined_content,
                "category": req.category,
                "reviewer": req.reviewer,
                "created_at": req.created_at,
                # 添加项目信息
                "project": {
                    "id": req.project.id,
                    "name": req.project.name,
                    "description": req.project.description
                } if req.project else None
            }

            # 为了兼容性，添加已废弃的字段
            req_dict["original_content"] = req.description
            req_dict["analysis_result"] = req.refined_content
            result.append(req_dict)

        # 使用自定义响应，添加X-Total-Count头
        from fastapi.responses import JSONResponse
        from fastapi.encoders import jsonable_encoder

        print(f"Retrieved and processed {len(result)} requirements, total: {total_count}")

        return JSONResponse(
            content=jsonable_encoder(result),
            headers={"X-Total-Count": str(total_count), "Access-Control-Expose-Headers": "X-Total-Count"}
        )
    except Exception as e:
        print(f"Error retrieving requirements: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@router.post("/requirements", response_model=Requirement)
def create_requirement(
    *,
    requirement_in: RequirementCreate,
    db: Session = Depends(get_db),
) -> Any:
    """
    Create new requirement.
    """
    # Check if project exists
    project = project_service.get_project(db, project_id=requirement_in.project_id)
    if not project:
        raise HTTPException(
            status_code=404,
            detail="The project with this ID does not exist in the system",
        )

    requirement = requirement_service.create_requirement(db, requirement=requirement_in)
    return requirement


@router.get("/requirements/{requirement_id}", response_model=Requirement)
def read_requirement(
    *,
    requirement_id: int,
    db: Session = Depends(get_db),
) -> Any:
    """
    Get requirement by ID.
    """
    requirement = requirement_service.get_requirement(db, requirement_id=requirement_id)
    if not requirement:
        raise HTTPException(
            status_code=404,
            detail="The requirement with this ID does not exist in the system",
        )

    # 手动将项目信息添加到响应中
    req_dict = {
        "id": requirement.id,
        "project_id": requirement.project_id,
        "title": requirement.title,
        "description": requirement.description,
        "refined_content": requirement.refined_content,
        "category": requirement.category,
        "reviewer": requirement.reviewer,
        "created_at": requirement.created_at,
        # 添加项目信息
        "project": {
            "id": requirement.project.id,
            "name": requirement.project.name,
            "description": requirement.project.description
        } if requirement.project else None
    }

    # 为了兼容性，添加已废弃的字段
    req_dict["original_content"] = requirement.description
    req_dict["analysis_result"] = requirement.refined_content

    return req_dict


@router.put("/requirements/{requirement_id}", response_model=Requirement)
def update_requirement(
    *,
    requirement_id: int,
    requirement_in: RequirementUpdate,
    db: Session = Depends(get_db),
) -> Any:
    """
    Update a requirement.
    """
    requirement = requirement_service.get_requirement(db, requirement_id=requirement_id)
    if not requirement:
        raise HTTPException(
            status_code=404,
            detail="The requirement with this ID does not exist in the system",
        )

    requirement = requirement_service.update_requirement(db, requirement_id=requirement_id, requirement=requirement_in)
    return requirement


@router.delete("/requirements/{requirement_id}", response_model=Requirement)
def delete_requirement(
    *,
    requirement_id: int,
    db: Session = Depends(get_db),
) -> Any:
    """
    Delete a requirement.
    """
    requirement = requirement_service.get_requirement(db, requirement_id=requirement_id)
    if not requirement:
        raise HTTPException(
            status_code=404,
            detail="The requirement with this ID does not exist in the system",
        )
    requirement = requirement_service.delete_requirement(db, requirement_id=requirement_id)
    return requirement


@router.post("/requirements/analyze-http", response_model=Success)
async def analyze_requirements_http(
    *,
    db: Session = Depends(get_db),
    data: dict
):
    """
    HTTP端点，用于处理需求分析请求

    这个端点接收前端发送的需求文件和内容，然后启动需求分析流程。
    分析结果会通过HTTP响应返回给前端。
    """
    import logging
    logger = logging.getLogger("requirements_http")

    try:
        # 验证文件路径
        files = []
        for file in data.get('files', []):
            file_path = file.get('path', '')
            if file_path:
                # 检查文件是否存在
                import os
                if os.path.exists(file_path):
                    files.append(file_path)
                    logger.info(f"文件存在: {file_path}")
                else:
                    logger.warning(f"文件不存在: {file_path}")
                    return JSONResponse(
                        status_code=400,
                        content={"detail": f"文件不存在: {file_path}"}
                    )

        # 创建需求文件消息
        from app.api.agent.requirement_agents import RequirementFilesMessage
        requirement_files = RequirementFilesMessage(
            user_id=str(data.get("userId", "")),
            files=files,
            content=data.get("content", ""),
            task=data.get("task", "Analyze requirement document")
        )

        logger.info(f"创建需求文件消息: 用户ID={requirement_files.user_id}, 文件数量={len(requirement_files.files)}")

        # 使用真实的AI进行需求分析
        from app.api.agent.requirement_agents import analyze_requirement_sync
        from app.agents.llms import model_client

        # 检查模型客户端是否可用
        if not model_client:
            logger.error("模型客户端不可用，无法进行需求分析")
            raise HTTPException(status_code=500, detail="AI模型不可用，请检查配置")

        # 使用AI进行需求分析
        try:
            # 记录用户输入的内容，用于调试
            user_content = data.get("content", "")
            logger.info(f"用户输入的需求内容长度: {len(user_content)} 字符")
            if user_content:
                logger.info(f"用户输入的需求内容前100个字符: {user_content[:100]}")
            else:
                logger.warning("用户没有输入需求内容")

            logger.info("开始使用AI进行需求分析")
            analysis_result = analyze_requirement_sync(
                user_id=str(data.get("userId", "")),
                content=user_content,
                files=files,
                task=data.get("task", "Analyze requirement document"),
                model_client=model_client
            )

            # 如果没有返回结果，创建一个默认结果
            if not analysis_result or not isinstance(analysis_result, dict):
                logger.warning("AI分析没有返回有效结果，使用默认结果")
                analysis_result = {
                    "success": True,
                    "message": "需求分析完成（使用默认结果）",
                    "data": {
                        "requirements": [
                            {
                                "id": 1,
                                "title": "需求分析失败",
                                "description": "AI未能生成有效的需求分析结果，请检查输入内容或重试",
                                "priority": "高",
                                "status": "待处理"
                            }
                        ]
                    }
                }

            logger.info(f"AI需求分析完成: {analysis_result}")
        except Exception as e:
            logger.error(f"AI需求分析失败: {str(e)}", exc_info=True)
            analysis_result = {
                "success": False,
                "message": f"需求分析失败: {str(e)}",
                "data": {
                    "requirements": [
                        {
                            "id": 1,
                            "title": "需求分析错误",
                            "description": f"发生错误: {str(e)}",
                            "priority": "高",
                            "status": "错误"
                        }
                    ]
                }
            }

        # 不自动保存到数据库，让用户决定是否保存
        # 只返回分析结果，前端会显示结果并提供保存按钮
        logger.info("需求分析完成，返回结果但不自动保存到数据库")

        return Success(data=analysis_result)
    except Exception as e:
        logger.error(f"需求分析错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/requirements/upload", response_model=Success)
async def upload_file(
    user_id: int = Query(..., description="User ID"),
    file: UploadFile = File(..., description="Uploaded file")
):
    """
    Handle file upload and return storage path.
    """
    try:
        upload_dir = Path("uploads") / str(user_id)
        upload_dir.mkdir(parents=True, exist_ok=True)

        # Generate unique filename
        file_ext = Path(file.filename).suffix
        uuid_name = f"{uuid.uuid4().hex}{file_ext}"
        file_path = upload_dir / uuid_name

        # Stream write file and control size
        max_size = 10 * 1024 * 1024  # 10MB
        total_size = 0

        with open(file_path, "wb") as buffer:
            while chunk := await file.read(8192):
                total_size += len(chunk)
                if total_size > max_size:
                    buffer.close()
                    os.remove(file_path)
                    raise HTTPException(413, detail="File size exceeds 10MB limit")
                buffer.write(chunk)

        return Success(data={
            "filePath": str(file_path),
            "fileId": uuid_name,
            "fileName": file.filename
        })
    except Exception as e:
        raise HTTPException(500, detail=str(e))


@router.post("/requirements/import", response_model=Success)
async def import_requirements(
    file: UploadFile = File(...),
    project_id: int = Form(...),
    db: Session = Depends(get_db)
):
    """
    Import requirements from Excel or CSV file.
    """
    try:
        # 打印调试信息
        print(f"收到导入请求: file={file.filename}, project_id={project_id}, type={type(project_id)}")
        print(f"文件内容类型: {file.content_type}")
        print(f"文件大小: {file.size if hasattr(file, 'size') else '未知'}")

        # 检查项目ID
        if not isinstance(project_id, int):
            try:
                project_id = int(project_id)
                print(f"转换project_id为整数: {project_id}")
            except (ValueError, TypeError) as e:
                error_msg = f"项目ID必须是整数，当前值: {project_id}, 错误: {str(e)}"
                print(f"项目ID错误: {error_msg}")
                raise HTTPException(
                    status_code=400,
                    detail=error_msg
                )

        # 检查文件类型
        filename = file.filename.lower()
        print(f"文件名: {filename}")

        if not (filename.endswith('.xlsx') or filename.endswith('.xls') or filename.endswith('.csv')):
            error_msg = f"只支持Excel(.xlsx/.xls)或CSV文件，当前文件: {filename}"
            print(f"文件类型错误: {error_msg}")
            raise HTTPException(
                status_code=400,
                detail=error_msg
            )

        # 检查项目是否存在
        project = project_service.get_project(db, project_id=project_id)
        if not project:
            raise HTTPException(
                status_code=404,
                detail="项目不存在"
            )

        # 读取文件内容
        content = await file.read()

        # 根据文件类型解析数据
        requirements_data = []

        if filename.endswith('.csv'):
            # 解析CSV文件
            csv_text = content.decode('utf-8-sig')  # 处理BOM
            csv_reader = csv.DictReader(io.StringIO(csv_text))

            for row in csv_reader:
                # 检查必要字段
                if 'title' not in row or 'description' not in row:
                    raise HTTPException(
                        status_code=400,
                        detail="CSV文件格式错误，必须包含'title'和'description'列"
                    )

                requirement = {
                    'project_id': project_id,
                    'title': row.get('title', '').strip(),
                    'description': row.get('description', '').strip(),
                    'category': row.get('category', '').strip() or None,
                    'reviewer': row.get('reviewer', '').strip() or None
                }

                # 验证必填字段
                if not requirement['title'] or not requirement['description']:
                    continue  # 跳过空行

                requirements_data.append(requirement)
        else:
            # 解析Excel文件
            try:
                df = pd.read_excel(io.BytesIO(content))

                # 检查必要字段
                if 'title' not in df.columns or 'description' not in df.columns:
                    raise HTTPException(
                        status_code=400,
                        detail="Excel文件格式错误，必须包含'title'和'description'列"
                    )

                for _, row in df.iterrows():
                    title = str(row.get('title', '')).strip()
                    description = str(row.get('description', '')).strip()

                    # 跳过空行
                    if not title or not description or title == 'nan' or description == 'nan':
                        continue

                    requirement = {
                        'project_id': project_id,
                        'title': title,
                        'description': description,
                        'category': str(row.get('category', '')).strip() if pd.notna(row.get('category', '')) else None,
                        'reviewer': str(row.get('reviewer', '')).strip() if pd.notna(row.get('reviewer', '')) else None
                    }

                    requirements_data.append(requirement)
            except Exception as e:
                raise HTTPException(
                    status_code=400,
                    detail=f"Excel文件解析错误: {str(e)}"
                )

        # 检查是否有有效数据
        if not requirements_data:
            raise HTTPException(
                status_code=400,
                detail="没有找到有效的需求数据"
            )

        # 保存到数据库
        imported_count = 0
        for req_data in requirements_data:
            try:
                # 创建需求对象
                requirement_in = RequirementCreate(
                    project_id=req_data['project_id'],
                    title=req_data['title'],
                    description=req_data['description'],
                    category=req_data['category'],
                    reviewer=req_data['reviewer']
                )

                # 保存到数据库
                requirement_service.create_requirement(db, requirement=requirement_in)
                imported_count += 1
            except Exception as e:
                print(f"导入需求失败: {str(e)}")
                # 继续处理下一条

        return Success(data={
            "imported_count": imported_count,
            "total_count": len(requirements_data)
        })
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        traceback.print_exc()
        error_msg = f"导入需求失败: {str(e)}"
        print(error_msg)
        print("错误堆栈:")
        traceback.print_exc()

        raise HTTPException(
            status_code=500,
            detail=error_msg
        )


# 导出API已移至文件顶部


@router.websocket("/requirements/analyze")
async def analyze_requirements(websocket: WebSocket):
    """
    WebSocket端点，用于处理需求分析请求
    """
    import logging
    import sys
    import time
    import asyncio
    import json

    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)

    # 创建WebSocket专用日志记录器
    logger = logging.getLogger("requirements_websocket")
    logger.setLevel(logging.INFO)

    # 确保有控制台处理器
    if not logger.handlers:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

        # 添加文件处理器
        file_handler = logging.FileHandler("websocket.log")
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    # 记录客户端信息
    client_host = websocket.client.host
    client_port = websocket.client.port
    logger.info(f"收到WebSocket连接请求: /api/v1/requirements/analyze, 客户端: {client_host}:{client_port}")

    # 记录请求头信息
    headers = websocket.headers
    logger.info(f"请求头: {dict(headers)}")

    try:
        # 接受WebSocket连接
        await websocket.accept()
        logger.info(f"WebSocket连接已接受, 客户端: {client_host}:{client_port}")

        # 设置WebSocket连接的ping超时时间为10分钟
        logger.info("WebSocket连接已建立，建议客户端设置较长的超时时间")
        logger.info("建议的WebSocket设置: ping_interval=30秒, ping_timeout=600秒(10分钟)")

        # 发送测试消息
        test_message = json.dumps({
            "type": "info",
            "content": "WebSocket连接已建立",
            "source": "system"
        })
        await websocket.send_text(test_message)
        logger.info("发送测试消息成功")

        # 定义收集结果的函数
        async def collect_result(_agent: ClosureContext, message: ResponseMessage, ctx: MessageContext) -> None:
            """收集智能体结果并发送到前端"""
            try:
                # 检查WebSocket连接是否仍然打开
                if websocket.client_state == WebSocketState.DISCONNECTED:
                    logger.warning("WebSocket连接已关闭，无法发送消息")
                    return

                msg = message.model_dump()
                logger.info(f"发送消息到前端: {msg['source']}, 是否最终消息: {msg['is_final']}")

                # 将消息转换为JSON字符串
                json_str = json.dumps(msg)
                logger.info(f"JSON字符串长度: {len(json_str)}")

                # 使用send_text而不是send_json，确保发送的是文本帧
                try:
                    await websocket.send_text(json_str)
                except RuntimeError as e:
                    if "Cannot call" in str(e) and "close message" in str(e):
                        logger.warning("WebSocket连接已关闭，无法发送消息")
                        return
                    else:
                        raise
            except WebSocketDisconnect:
                logger.warning("WebSocket连接已断开，无法发送消息")
                return
            except Exception as e:
                logger.error(f"发送消息到前端失败: {str(e)}", exc_info=True)
                try:
                    # 检查WebSocket连接是否仍然打开
                    if websocket.client_state == WebSocketState.DISCONNECTED:
                        logger.warning("WebSocket连接已关闭，无法发送错误消息")
                        return

                    error_msg = json.dumps({
                        "source": "system",
                        "content": f"发送消息失败: {str(e)}",
                        "is_final": False
                    })
                    await websocket.send_text(error_msg)
                except:
                    logger.error("无法发送错误消息", exc_info=True)

        # 定义用户输入函数
        async def _user_input(prompt: str, cancellation_token: CancellationToken | None) -> str:
            """等待用户输入（阻塞执行），类似于input()"""
            try:
                logger.info(f"请求用户输入: {prompt}")
                data = await websocket.receive_json()
                message = TextMessage.model_validate(data)
                logger.info("收到用户输入")
                return message.content
            except Exception as e:
                logger.error(f"处理用户输入失败: {str(e)}", exc_info=True)
                return ""

        # 创建心跳任务
        async def heartbeat_task():
            """定期发送心跳包，保持WebSocket连接活跃"""
            # 记录上次心跳时间
            last_heartbeat_time = time.time()

            while True:
                try:
                    # 更频繁地发送心跳包
                    await asyncio.sleep(15)  # 每15秒发送一次心跳包

                    # 检查WebSocket连接状态
                    if websocket.client_state == WebSocketState.DISCONNECTED:
                        logger.warning("WebSocket连接已断开，停止心跳任务")
                        break

                    # 记录当前时间
                    current_time = time.time()
                    elapsed = current_time - last_heartbeat_time

                    # 发送心跳包
                    try:
                        # 先尝试发送简单的文本心跳包
                        await websocket.send_text("ping")
                        logger.debug(f"发送简单心跳包，距离上次心跳: {elapsed:.2f}秒")

                        # 更新上次心跳时间
                        last_heartbeat_time = current_time
                    except Exception as e:
                        logger.error(f"发送简单心跳包失败: {str(e)}")

                        # 如果简单心跳包失败，尝试发送JSON格式的心跳包
                        try:
                            await websocket.send_json({
                                "type": "heartbeat",
                                "source": "system",
                                "timestamp": current_time
                            })
                            logger.debug(f"发送JSON心跳包成功，距离上次心跳: {elapsed:.2f}秒")

                            # 更新上次心跳时间
                            last_heartbeat_time = current_time
                        except Exception as e:
                            logger.error(f"发送JSON心跳包也失败: {str(e)}")

                            # 如果连续3次心跳失败，则退出心跳任务
                            if not hasattr(heartbeat_task, "fail_count"):
                                heartbeat_task.fail_count = 0

                            heartbeat_task.fail_count += 1
                            logger.warning(f"心跳包发送失败次数: {heartbeat_task.fail_count}")

                            if heartbeat_task.fail_count >= 3:
                                logger.error("连续3次心跳包发送失败，停止心跳任务")
                                break
                except Exception as e:
                    logger.error(f"心跳任务异常: {str(e)}")
                    break

        # 启动心跳任务
        heartbeat = asyncio.create_task(heartbeat_task())

        try:
            while True:
                logger.info("等待前端发送需求分析请求")

                # 使用receive_text()而不是receive_json()，以便处理非JSON消息
                try:
                    # 设置接收超时
                    raw_data = await asyncio.wait_for(websocket.receive_text(), timeout=1800)  # 30分钟超时
                    logger.info(f"收到前端消息: {raw_data[:100]}...")  # 只记录前100个字符

                    # 处理简单的ping心跳包
                    if raw_data == 'ping':
                        logger.debug("收到简单心跳包，发送pong响应")
                        try:
                            await websocket.send_text('pong')
                            continue
                        except Exception as e:
                            logger.error(f"发送pong响应失败: {str(e)}")
                            continue

                    # 尝试解析JSON
                    try:
                        data = json.loads(raw_data)
                        logger.info(f"成功解析JSON数据")

                        # 检查是否是心跳包
                        if data.get("type") == "heartbeat":
                            logger.debug(f"收到JSON心跳包")
                            # 立即回复心跳包
                            try:
                                await websocket.send_json({
                                    "type": "heartbeat",
                                    "source": "system",
                                    "content": "pong",
                                    "timestamp": time.time()
                                })
                                logger.debug("回复心跳包成功")
                            except Exception as e:
                                logger.error(f"回复心跳包失败: {str(e)}")
                            continue
                    except json.JSONDecodeError:
                        logger.error(f"无法解析JSON数据: {raw_data[:100]}...")
                        try:
                            await websocket.send_json({
                                "source": "system",
                                "content": "无法解析JSON数据，请发送有效的JSON格式",
                                "is_final": False
                            })
                        except Exception as e:
                            logger.error(f"发送JSON解析错误消息失败: {str(e)}")
                        continue
                except asyncio.TimeoutError:
                    logger.error("接收消息超时（30分钟）")
                    try:
                        await websocket.send_json({
                            "source": "system",
                            "content": "接收消息超时（30分钟），连接将被关闭",
                            "is_final": True
                        })
                        await websocket.close(code=1001, reason="接收消息超时（30分钟）")
                    except Exception as e:
                        logger.error(f"发送超时消息或关闭连接失败: {str(e)}")
                    return

                logger.info(f"处理前端请求: {str(data)[:100]}...")  # 只记录前100个字符

                # 验证文件路径
                files = []
                for file in data.get('files', []):
                    file_path = file.get('path', '')
                    if file_path:
                        # 检查文件是否存在
                        import os
                        if os.path.exists(file_path):
                            files.append(file_path)
                            logger.info(f"文件存在: {file_path}")
                        else:
                            logger.warning(f"文件不存在: {file_path}")
                            await websocket.send_json({
                                "type": "warning",
                                "content": f"文件不存在: {file_path}",
                                "source": "system"
                            })

                # 记录用户输入的内容，用于调试
                user_content = data.get("content", "")
                logger.info(f"用户输入的需求内容长度: {len(user_content)} 字符")
                if user_content:
                    logger.info(f"用户输入的需求内容前100个字符: {user_content[:100]}")
                    # 在终端打印完整的需求内容，以便确认是否正确获取了需求
                    print("\n" + "="*80)
                    print("【WebSocket获取到的需求内容】")
                    print(user_content)
                    print("="*80 + "\n")
                else:
                    logger.warning("用户没有输入需求内容")
                    print("\n" + "="*80)
                    print("【警告】用户没有输入需求内容，分析结果可能不准确")
                    print("="*80 + "\n")
                    await websocket.send_json({
                        "type": "warning",
                        "content": "没有输入需求内容，分析结果可能不准确",
                        "source": "system"
                    })

                # 创建需求文件消息
                requirement_files = RequirementFilesMessage(
                    user_id=str(data.get("userId", "")),
                    files=files,
                    content=user_content,
                    task=data.get("task", "Analyze requirement document")
                )

                logger.info(f"创建需求文件消息: 用户ID={requirement_files.user_id}, 文件数量={len(requirement_files.files)}")

                try:
                    # 启动需求分析运行时
                    logger.info("开始启动需求分析运行时")
                    try:
                        # 记录用户输入的内容，用于调试
                        user_content = requirement_files.content
                        logger.info(f"用户输入的需求内容长度: {len(user_content)} 字符")
                        if user_content:
                            logger.info(f"用户输入的需求内容前100个字符: {user_content[:100]}")
                        else:
                            logger.warning("用户没有输入需求内容")

                        # 设置超时时间
                        # 创建一个任务来运行需求分析
                        analysis_task = asyncio.create_task(
                            start_runtime(
                                requirement_files=requirement_files,
                                collect_result=collect_result,
                                user_input_func=_user_input
                            )
                        )

                        # 等待任务完成，设置超时时间为30分钟
                        try:
                            # 使用更长的超时时间
                            await asyncio.wait_for(analysis_task, timeout=1800)  # 30分钟超时
                            logger.info("需求分析运行时完成")
                        except asyncio.TimeoutError:
                            logger.error("需求分析运行时超时（30分钟）")
                            # 发送超时消息到客户端
                            try:
                                timeout_message = {
                                    "source": "system",
                                    "content": "需求分析运行时超时（30分钟），请尝试减少需求内容或分批次分析。",
                                    "is_final": True
                                }
                                await websocket.send_json(timeout_message)
                            except Exception as e:
                                logger.error(f"发送超时消息失败: {str(e)}", exc_info=True)

                            # 关闭WebSocket连接
                            try:
                                await websocket.close(code=1011, reason="需求分析运行时超时（30分钟）")
                            except Exception as e:
                                logger.error(f"关闭WebSocket连接失败: {str(e)}", exc_info=True)
                            return
                    except Exception as e:
                        logger.error(f"需求分析运行时错误: {str(e)}", exc_info=True)
                        # 发送错误消息到客户端
                        error_message = {
                            "source": "system",
                            "content": f"错误: {str(e)}",
                            "is_final": True
                        }
                        await websocket.send_json(error_message)
                        # 错误后重新启用输入
                        await websocket.send_json({
                            "source": "system",
                            "content": "发生错误，请重试。",
                            "is_final": False
                        })
                        # 关闭WebSocket连接
                        await websocket.close(code=1011, reason=f"需求分析运行时错误: {str(e)}")
                        return
                except Exception as e:
                    logger.error(f"需求分析运行时错误: {str(e)}", exc_info=True)
                    # 发送错误消息到客户端
                    error_message = {
                        "source": "system",
                        "content": f"错误: {str(e)}",
                        "is_final": True
                    }
                    await websocket.send_json(error_message)
                    # 错误后重新启用输入
                    await websocket.send_json({
                        "source": "system",
                        "content": "发生错误，请重试。",
                        "is_final": False
                    })
                    # 关闭WebSocket连接
                    await websocket.close(code=1011, reason=f"需求分析运行时错误: {str(e)}")
                    return

        except WebSocketDisconnect:
            logger.info("客户端断开连接")
        except Exception as e:
            logger.error(f"处理WebSocket消息时发生错误: {str(e)}", exc_info=True)
            try:
                await websocket.send_json({
                    "type": "error",
                    "source": "system",
                    "content": f"处理消息时发生错误: {str(e)}"
                })
            except:
                logger.error("无法发送错误消息")
        finally:
            # 取消心跳任务
            heartbeat.cancel()
            try:
                await heartbeat
            except asyncio.CancelledError:
                pass

    except Exception as e:
        logger.error(f"WebSocket连接接受失败: {str(e)}", exc_info=True)
        return
