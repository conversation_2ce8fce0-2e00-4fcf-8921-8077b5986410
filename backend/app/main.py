"""
主应用程序入口文件
这个文件是FastAPI应用程序的主入口点，负责初始化应用、配置中间件、注册路由和启动服务器。
"""

import os
import logging
from fastapi import FastAPI, Depends, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware  # 导入CORS中间件，用于处理跨域请求
from sqlalchemy.orm import Session  # SQLAlchemy会话，用于数据库操作

# 导入项目内部模块
from app.api.api import api_router  # 导入API路由集合
from app.core.config import settings  # 导入应用配置
from app.db.base import Base  # 导入SQLAlchemy基类
from app.db.session import engine, get_db  # 导入数据库引擎和会话获取函数
from app.core.database import create_initial_user  # 导入创建初始用户的函数

# 配置日志系统
# 设置日志级别为INFO，格式包含时间、名称、级别和消息
# 同时输出到控制台和文件
logging.basicConfig(
    level=logging.INFO,  # 设置日志级别为INFO
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',  # 设置日志格式
    handlers=[
        logging.StreamHandler(),  # 输出到控制台
        logging.FileHandler("app.log", encoding="utf-8")  # 输出到文件，使用UTF-8编码
    ]
)
logger = logging.getLogger("app.main")  # 创建当前模块的日志记录器

# 创建数据库表
# 使用SQLAlchemy的元数据创建所有在Base中注册的模型对应的表
# 如果表已存在，则不会重新创建
Base.metadata.create_all(bind=engine)

# 创建FastAPI应用实例
app = FastAPI(
    title=settings.PROJECT_NAME,  # 设置应用标题，用于OpenAPI文档
    openapi_url=f"{settings.API_V1_STR}/openapi.json"  # 设置OpenAPI JSON文档的URL
)

# 设置CORS (跨域资源共享) - 允许特定来源访问API
# 这对于前端应用能够访问后端API是必要的
# 注意：当allow_credentials=True时，allow_origins不能是["*"]
origins = [
    "http://localhost:5173",  # Vite开发服务器默认端口
    "http://localhost:5174",  # Vite开发服务器备用端口
    "http://127.0.0.1:5173",
    "http://127.0.0.1:5174",
    "http://localhost:3000",  # 其他可能的前端端口
    "http://127.0.0.1:3000",
]

# 添加CORS中间件到应用
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,  # 允许的来源列表
    allow_credentials=True,  # 允许发送凭证（如cookies）
    allow_methods=["*"],  # 允许所有HTTP方法
    allow_headers=["*"],  # 允许所有HTTP头
    expose_headers=["X-Total-Count", "*"],  # 明确暴露X-Total-Count头部，用于分页
)

# 包含API路由
# 将所有API路由注册到应用，并添加前缀
app.include_router(api_router, prefix=settings.API_V1_STR)


@app.on_event("startup")
async def startup_event():
    """
    应用启动事件处理函数
    在FastAPI应用启动时执行，用于初始化必要的资源和配置
    """
    logger.info("应用启动中...")

    # 创建上传目录
    # 确保文件上传目录存在，如果不存在则创建
    uploads_dir = "uploads"
    os.makedirs(uploads_dir, exist_ok=True)
    logger.info(f"创建上传目录: {uploads_dir}")

    # 创建初始管理员用户
    # 确保系统中至少有一个管理员用户
    try:
        db = next(get_db())  # 获取数据库会话
        create_initial_user(db)  # 创建初始管理员用户
        logger.info("初始管理员用户创建成功")
    except Exception as e:
        logger.error(f"创建初始管理员用户失败: {str(e)}", exc_info=True)

    # 检查AI模型配置
    # 确保AI模型配置正确
    from app.agents.llms import DEFAULT_MODEL, DEFAULT_API_BASE
    logger.info(f"AI模型配置: 模型={DEFAULT_MODEL}, API基础URL={DEFAULT_API_BASE}")

    logger.info("应用启动完成")


@app.get("/")
def read_root():
    """
    根路径处理函数
    返回一个简单的欢迎消息，用于检查API是否正常运行

    Returns:
        dict: 包含欢迎消息的字典
    """
    return {"message": "欢迎使用AI测试管理系统"}


@app.websocket("/ws-test")
async def websocket_test(websocket: WebSocket):
    """
    WebSocket测试端点
    提供一个简单的WebSocket连接测试，接收客户端消息并回显

    Args:
        websocket (WebSocket): WebSocket连接对象
    """
    logger.info("收到WebSocket测试连接请求")
    await websocket.accept()  # 接受WebSocket连接
    logger.info("WebSocket测试连接已接受")

    try:
        # 发送欢迎消息
        await websocket.send_json({"message": "WebSocket连接已建立"})
        logger.info("发送欢迎消息成功")

        # 等待并回显消息
        # 持续监听客户端消息，并将收到的消息回显给客户端
        while True:
            data = await websocket.receive_text()  # 接收文本消息
            logger.info(f"收到消息: {data}")
            await websocket.send_text(f"服务器收到: {data}")  # 回显消息
    except Exception as e:
        logger.error(f"WebSocket测试错误: {str(e)}", exc_info=True)
    finally:
        logger.info("WebSocket测试连接已关闭")


# 当直接运行此文件时执行
if __name__ == "__main__":
    import uvicorn
    # 使用uvicorn启动应用
    # host="0.0.0.0" 表示监听所有网络接口
    # port=8000 表示监听8000端口
    # reload=True 表示代码变更时自动重新加载
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True)
