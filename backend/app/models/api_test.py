"""
接口测试相关数据模型

包含以下模型：
1. ApiDocument - 接口文档模型
2. ApiTestCase - 接口测试用例模型
3. ApiTestExecution - 接口测试执行结果模型
4. ApiTestScript - 接口测试脚本模型
"""

from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime, JSON, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base


class ApiDocument(Base):
    """
    接口文档模型

    用于存储导入的接口文档信息，支持多种格式（Swagger/OpenAPI/Postman等）
    """
    __tablename__ = "api_documents"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False, comment="接口文档标题")
    description = Column(Text, nullable=True, comment="接口文档描述")
    format_type = Column(String(50), nullable=False, comment="文档格式类型(Swagger/OpenAPI/Postman等)")
    content = Column(Text, nullable=False, comment="接口文档内容(JSON/YAML格式)")
    base_url = Column(String(255), nullable=True, comment="接口基础URL")
    version = Column(String(50), nullable=True, comment="接口版本")
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False, comment="所属项目ID")
    creator = Column(String(50), nullable=False, comment="创建者")
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关联关系
    # project = relationship("Project", back_populates="api_documents")
    api_testcases = relationship("ApiTestCase", back_populates="api_document", cascade="all, delete-orphan")


class ApiTestCase(Base):
    """
    接口测试用例模型

    存储从接口文档生成或手动创建的测试用例信息
    """
    __tablename__ = "api_testcases"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False, comment="测试用例标题")
    description = Column(Text, nullable=True, comment="测试用例描述")
    api_path = Column(String(255), nullable=False, comment="接口路径")
    method = Column(String(20), nullable=False, comment="HTTP方法(GET/POST/PUT/DELETE等)")
    headers = Column(JSON, nullable=True, comment="请求头(JSON格式)")
    params = Column(JSON, nullable=True, comment="请求参数(JSON格式)")
    body = Column(JSON, nullable=True, comment="请求体(JSON格式)")
    expected_status = Column(Integer, nullable=False, comment="预期状态码")
    expected_response = Column(JSON, nullable=True, comment="预期响应(JSON格式)")
    validation_rules = Column(JSON, nullable=True, comment="验证规则(JSON格式)")
    test_data = Column(JSON, nullable=True, comment="测试数据(JSON格式)")
    priority = Column(String(20), default="中", comment="优先级(高/中/低)")
    tags = Column(JSON, nullable=True, comment="标签列表")
    api_document_id = Column(Integer, ForeignKey("api_documents.id"), nullable=False, comment="所属接口文档ID")
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False, comment="所属项目ID")
    status = Column(String(50), default="未执行", comment="测试状态(未执行/通过/失败)")
    creator = Column(String(50), nullable=False, comment="创建者")
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关联关系
    api_document = relationship("ApiDocument", back_populates="api_testcases")
    # project = relationship("Project", back_populates="api_testcases")
    execution_results = relationship("ApiTestExecution", back_populates="api_testcase", cascade="all, delete-orphan")


class ApiTestExecution(Base):
    """
    接口测试执行结果模型

    记录测试用例的执行结果和详细信息
    """
    __tablename__ = "api_test_executions"

    id = Column(Integer, primary_key=True, index=True)
    api_testcase_id = Column(Integer, ForeignKey("api_testcases.id"), nullable=False, comment="测试用例ID")
    status = Column(String(50), nullable=False, comment="执行状态(通过/失败)")
    actual_status = Column(Integer, nullable=True, comment="实际状态码")
    actual_response = Column(JSON, nullable=True, comment="实际响应(JSON格式)")
    execution_time = Column(Integer, nullable=True, comment="执行时间(毫秒)")
    error_message = Column(Text, nullable=True, comment="错误信息")
    environment = Column(String(100), nullable=True, comment="执行环境")
    base_url = Column(String(255), nullable=True, comment="执行时使用的基础URL")
    executed_at = Column(DateTime, default=func.now(), comment="执行时间")
    executed_by = Column(String(50), nullable=False, comment="执行者")

    # 关联关系
    api_testcase = relationship("ApiTestCase", back_populates="execution_results")


class ApiTestScript(Base):
    """
    接口测试脚本模型

    存储生成的Python测试脚本代码
    """
    __tablename__ = "api_test_scripts"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False, comment="脚本标题")
    description = Column(Text, nullable=True, comment="脚本描述")
    script_type = Column(String(50), default="pytest", comment="脚本类型(pytest/unittest/requests)")
    script_content = Column(Text, nullable=False, comment="脚本内容")
    requirements = Column(Text, nullable=True, comment="依赖包列表")
    api_document_id = Column(Integer, ForeignKey("api_documents.id"), nullable=True, comment="关联的接口文档ID")
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False, comment="所属项目ID")
    is_executable = Column(Boolean, default=True, comment="是否可执行")
    creator = Column(String(50), nullable=False, comment="创建者")
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关联关系
    api_document = relationship("ApiDocument")
    # project = relationship("Project")
