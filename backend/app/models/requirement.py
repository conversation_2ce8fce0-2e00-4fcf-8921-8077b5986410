from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from app.db.base import Base


class Requirement(Base):
    __tablename__ = "requirements"

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    title = Column(String, nullable=False)
    description = Column(Text, nullable=False)  # 需求描述，与原始需求内容相同
    refined_content = Column(Text, nullable=True)  # AI分析结果
    category = Column(String, nullable=True)
    reviewer = Column(String, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 以下字段已废弃，保留是为了兼容性
    original_content = Column(Text, nullable=True)  # 已废弃，使用description替代
    analysis_result = Column(Text, nullable=True)  # 已废弃，使用refined_content替代
    temp_description = Column(Text, nullable=True)  # 临时字段
    temp_analysis = Column(Text, nullable=True)  # 临时字段

    # Relationships
    project = relationship("Project", back_populates="requirements")
    testcases = relationship("TestCase", back_populates="requirement")
    performance_testcases = relationship("PerformanceTestCase", back_populates="requirement")
