from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Union
from datetime import datetime

from app.schemas.project import Project


# 接口文档模型
class ApiDocumentBase(BaseModel):
    title: str
    description: Optional[str] = None
    format_type: str  # Swagger/OpenAPI/Postman等
    content: str  # JSON/YAML格式
    base_url: Optional[str] = None
    version: Optional[str] = None
    project_id: int
    creator: Optional[str] = "admin"


class ApiDocumentCreate(ApiDocumentBase):
    pass


class ApiDocumentUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    format_type: Optional[str] = None
    content: Optional[str] = None
    base_url: Optional[str] = None
    version: Optional[str] = None


class ApiDocumentInDBBase(ApiDocumentBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ApiDocument(ApiDocumentInDBBase):
    # project: Optional[Project] = None
    pass


class ApiDocumentInDB(ApiDocumentInDBBase):
    pass


# 接口测试用例模型
class ApiTestCaseBase(BaseModel):
    title: str
    description: Optional[str] = None
    api_path: str
    method: str  # GET/POST/PUT/DELETE等
    headers: Optional[Dict[str, str]] = None
    params: Optional[Dict[str, Any]] = None
    body: Optional[Dict[str, Any]] = None
    expected_status: int
    expected_response: Optional[Dict[str, Any]] = None
    validation_rules: Optional[List[Dict[str, Any]]] = None
    test_data: Optional[Dict[str, Any]] = None
    priority: str = "中"
    tags: Optional[List[str]] = None
    api_document_id: int
    project_id: int
    status: str = "未执行"
    creator: Optional[str] = "admin"


class ApiTestCaseCreate(ApiTestCaseBase):
    pass


class ApiTestCaseUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    api_path: Optional[str] = None
    method: Optional[str] = None
    headers: Optional[Dict[str, str]] = None
    params: Optional[Dict[str, Any]] = None
    body: Optional[Dict[str, Any]] = None
    expected_status: Optional[int] = None
    expected_response: Optional[Dict[str, Any]] = None
    validation_rules: Optional[List[Dict[str, Any]]] = None
    status: Optional[str] = None


class ApiTestCaseInDBBase(ApiTestCaseBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ApiTestCase(ApiTestCaseInDBBase):
    api_document: Optional[ApiDocument] = None
    # project: Optional[Project] = None


class ApiTestCaseInDB(ApiTestCaseInDBBase):
    pass


# 接口测试执行结果模型
class ApiTestExecutionBase(BaseModel):
    api_testcase_id: int
    status: str  # 通过/失败
    actual_status: Optional[int] = None
    actual_response: Optional[Dict[str, Any]] = None
    execution_time: Optional[int] = None  # 毫秒
    error_message: Optional[str] = None
    executed_by: str = "admin"


class ApiTestExecutionCreate(ApiTestExecutionBase):
    pass


class ApiTestExecutionInDBBase(ApiTestExecutionBase):
    id: int
    executed_at: datetime

    class Config:
        from_attributes = True


class ApiTestExecution(ApiTestExecutionInDBBase):
    pass


class ApiTestExecutionInDB(ApiTestExecutionInDBBase):
    pass


# 导入接口文档请求模型
class ImportApiDocumentRequest(BaseModel):
    title: str
    description: Optional[str] = None
    format_type: str
    content: str
    project_id: int
    creator: Optional[str] = "admin"


# 生成接口测试用例请求模型
class GenerateApiTestCasesRequest(BaseModel):
    api_document_id: int
    project_id: int
    creator: Optional[str] = "admin"


# 执行接口测试请求模型
class ExecuteApiTestRequest(BaseModel):
    testcase_ids: List[int]
    environment: Optional[str] = "测试环境"
    base_url: Optional[str] = None
    executed_by: Optional[str] = "admin"


# 接口测试脚本模型
class ApiTestScriptBase(BaseModel):
    title: str
    description: Optional[str] = None
    script_type: str = "pytest"
    script_content: str
    requirements: Optional[str] = None
    api_document_id: Optional[int] = None
    project_id: int
    is_executable: bool = True
    creator: Optional[str] = "admin"


class ApiTestScriptCreate(ApiTestScriptBase):
    pass


class ApiTestScriptUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    script_type: Optional[str] = None
    script_content: Optional[str] = None
    requirements: Optional[str] = None
    is_executable: Optional[bool] = None


class ApiTestScriptInDBBase(ApiTestScriptBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ApiTestScript(ApiTestScriptInDBBase):
    api_document: Optional[ApiDocument] = None
    # project: Optional[Project] = None


class ApiTestScriptInDB(ApiTestScriptInDBBase):
    pass


# 生成Python脚本请求模型
class GeneratePythonScriptRequest(BaseModel):
    testcase_ids: List[int]
    script_type: str = "pytest"  # pytest/unittest/requests
    project_id: int
    title: Optional[str] = None
    description: Optional[str] = None
    creator: Optional[str] = "admin"


# 接口测试结果响应模型
class ApiTestResultResponse(BaseModel):
    success: bool
    message: str
    results: Optional[List[ApiTestExecution]] = None


# 导出请求模型
class ExportApiTestCasesRequest(BaseModel):
    testcase_ids: Optional[List[int]] = None
    project_id: Optional[int] = None
    format_type: str = "excel"  # excel/csv/json
    include_results: bool = False
