from pydantic import BaseModel, Field, field_validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
import json

from app.schemas.project import Project
from app.schemas.requirement import Requirement


# 性能测试用例模型
class PerformanceTestCaseBase(BaseModel):
    title: str
    description: Optional[str] = None
    test_type: str  # 负载测试/压力测试/耐久测试等
    target_url: str
    method: str  # GET/POST/PUT/DELETE等
    headers: Optional[Dict[str, str]] = None
    params: Optional[Dict[str, Any]] = None
    body: Optional[Dict[str, Any]] = None
    virtual_users: int
    ramp_up_period: int  # 秒
    duration: int  # 秒
    think_time: Optional[int] = None  # 毫秒
    expected_response_time: Optional[int] = None  # 毫秒
    expected_throughput: Optional[float] = None  # 每秒请求数
    expected_error_rate: Optional[float] = None  # %
    test_script: Optional[str] = None
    requirement_id: Optional[int] = None
    project_id: int
    status: str = "未执行"
    creator: Optional[str] = "admin"


class PerformanceTestCaseCreate(PerformanceTestCaseBase):
    pass


class PerformanceTestCaseUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    test_type: Optional[str] = None
    target_url: Optional[str] = None
    method: Optional[str] = None
    headers: Optional[Dict[str, str]] = None
    params: Optional[Dict[str, Any]] = None
    body: Optional[Dict[str, Any]] = None
    virtual_users: Optional[int] = None
    ramp_up_period: Optional[int] = None
    duration: Optional[int] = None
    think_time: Optional[int] = None
    expected_response_time: Optional[int] = None
    expected_throughput: Optional[float] = None
    expected_error_rate: Optional[float] = None
    test_script: Optional[str] = None
    status: Optional[str] = None


class PerformanceTestCaseInDBBase(PerformanceTestCaseBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class PerformanceTestCase(PerformanceTestCaseInDBBase):
    requirement: Optional[Requirement] = None
    project: Optional[Project] = None


class PerformanceTestCaseInDB(PerformanceTestCaseInDBBase):
    pass


# 性能测试执行结果模型
class PerformanceTestExecutionBase(BaseModel):
    performance_testcase_id: int
    status: str  # 通过/失败
    actual_response_time: Optional[Dict[str, Any]] = None
    actual_throughput: Optional[float] = None  # 每秒请求数
    actual_error_rate: Optional[float] = None  # %
    concurrent_users: Optional[int] = None
    total_requests: Optional[int] = None
    successful_requests: Optional[int] = None
    failed_requests: Optional[int] = None
    min_response_time: Optional[int] = None  # 毫秒
    max_response_time: Optional[int] = None  # 毫秒
    avg_response_time: Optional[int] = None  # 毫秒
    percentile_90: Optional[int] = None  # 毫秒
    percentile_95: Optional[int] = None  # 毫秒
    percentile_99: Optional[int] = None  # 毫秒
    test_environment: Optional[str] = None
    result_details: Optional[Dict[str, Any]] = None
    executed_by: str = "admin"


class PerformanceTestExecutionCreate(PerformanceTestExecutionBase):
    pass


class PerformanceTestExecutionInDBBase(PerformanceTestExecutionBase):
    id: int
    executed_at: datetime

    class Config:
        from_attributes = True


class PerformanceTestExecution(PerformanceTestExecutionInDBBase):
    pass


class PerformanceTestExecutionInDB(PerformanceTestExecutionInDBBase):
    pass


# 生成性能测试用例请求模型
class GeneratePerformanceTestCasesRequest(BaseModel):
    requirement_id: int
    project_id: int
    creator: Optional[str] = "admin"


# 执行性能测试请求模型
class ExecutePerformanceTestRequest(BaseModel):
    testcase_ids: List[int]
    environment: Optional[str] = "测试环境"
    base_url: Optional[str] = None
    executed_by: Optional[str] = "admin"


# 性能测试用例响应模型
class PerformanceTestCaseResponse(PerformanceTestCase):
    """性能测试用例响应模型"""
    pass


# 性能测试执行响应模型
class PerformanceTestExecutionResponse(PerformanceTestExecution):
    """性能测试执行响应模型"""
    pass


# 性能测试结果响应模型
class PerformanceTestResultResponse(BaseModel):
    success: bool
    message: str
    results: Optional[List[PerformanceTestExecution]] = None
