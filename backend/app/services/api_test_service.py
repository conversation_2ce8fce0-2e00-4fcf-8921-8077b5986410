from typing import List, Optional, Dict, Any
import json
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import desc

from app.models.api_test import ApiDocument, ApiTestCase, ApiTestExecution, ApiTestScript
from app.schemas.api_test import (
    ApiDocumentCreate, ApiDocumentUpdate,
    ApiTestCaseCreate, ApiTestCaseUpdate,
    ApiTestExecutionCreate, ApiTestScriptCreate, ApiTestScriptUpdate
)


def _parse_testcase_json_fields(testcase: ApiTestCase):
    """解析测试用例的JSON字段"""
    import json

    # 解析headers
    if testcase.headers and isinstance(testcase.headers, str):
        try:
            testcase.headers = json.loads(testcase.headers)
        except:
            testcase.headers = {}

    # 解析params
    if testcase.params and isinstance(testcase.params, str):
        try:
            testcase.params = json.loads(testcase.params)
        except:
            testcase.params = {}

    # 解析body
    if testcase.body and isinstance(testcase.body, str):
        try:
            testcase.body = json.loads(testcase.body)
        except:
            testcase.body = {}

    # 解析expected_response
    if testcase.expected_response and isinstance(testcase.expected_response, str):
        try:
            testcase.expected_response = json.loads(testcase.expected_response)
        except:
            testcase.expected_response = {}

    # 解析validation_rules
    if testcase.validation_rules and isinstance(testcase.validation_rules, str):
        try:
            testcase.validation_rules = json.loads(testcase.validation_rules)
        except:
            testcase.validation_rules = []

    # 解析test_data
    if testcase.test_data and isinstance(testcase.test_data, str):
        try:
            testcase.test_data = json.loads(testcase.test_data)
        except:
            testcase.test_data = {}

    # 解析tags
    if testcase.tags and isinstance(testcase.tags, str):
        try:
            testcase.tags = json.loads(testcase.tags)
        except:
            testcase.tags = []


# 接口文档服务
def get_api_document(db: Session, document_id: int) -> Optional[ApiDocument]:
    """获取接口文档"""
    return db.query(ApiDocument).options(
        joinedload(ApiDocument.project)
    ).filter(ApiDocument.id == document_id).first()


def get_api_documents(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    project_id: Optional[int] = None,
    title: Optional[str] = None,
    format_type: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None
) -> List[ApiDocument]:
    """获取接口文档列表"""
    query = db.query(ApiDocument).options(
        joinedload(ApiDocument.project)
    )

    if project_id:
        query = query.filter(ApiDocument.project_id == project_id)

    if title:
        query = query.filter(ApiDocument.title.ilike(f"%{title}%"))

    if format_type:
        query = query.filter(ApiDocument.format_type == format_type)

    if start_date and end_date:
        query = query.filter(ApiDocument.created_at.between(start_date, end_date))
    elif start_date:
        query = query.filter(ApiDocument.created_at >= start_date)
    elif end_date:
        query = query.filter(ApiDocument.created_at <= end_date)

    return query.order_by(desc(ApiDocument.created_at)).offset(skip).limit(limit).all()


def get_api_documents_count(
    db: Session,
    project_id: Optional[int] = None,
    title: Optional[str] = None,
    format_type: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None
) -> int:
    """获取接口文档总数"""
    query = db.query(ApiDocument)

    if project_id:
        query = query.filter(ApiDocument.project_id == project_id)

    if title:
        query = query.filter(ApiDocument.title.ilike(f"%{title}%"))

    if format_type:
        query = query.filter(ApiDocument.format_type == format_type)

    if start_date and end_date:
        query = query.filter(ApiDocument.created_at.between(start_date, end_date))
    elif start_date:
        query = query.filter(ApiDocument.created_at >= start_date)
    elif end_date:
        query = query.filter(ApiDocument.created_at <= end_date)

    return query.count()


def create_api_document(db: Session, document: ApiDocumentCreate) -> ApiDocument:
    """创建接口文档"""
    db_document = ApiDocument(
        title=document.title,
        description=document.description,
        format_type=document.format_type,
        content=document.content,
        base_url=document.base_url,
        version=document.version,
        project_id=document.project_id,
        creator=document.creator
    )
    db.add(db_document)
    db.commit()
    db.refresh(db_document)
    return db_document


def update_api_document(db: Session, document_id: int, document: ApiDocumentUpdate) -> Optional[ApiDocument]:
    """更新接口文档"""
    db_document = get_api_document(db, document_id)
    if not db_document:
        return None

    update_data = document.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_document, key, value)

    db.add(db_document)
    db.commit()
    db.refresh(db_document)
    return db_document


def delete_api_document(db: Session, document_id: int) -> Optional[ApiDocument]:
    """删除接口文档"""
    db_document = get_api_document(db, document_id)
    if not db_document:
        return None

    db.delete(db_document)
    db.commit()
    return db_document


# 接口测试用例服务
def get_api_testcase(db: Session, testcase_id: int) -> Optional[ApiTestCase]:
    """获取接口测试用例"""
    testcase = db.query(ApiTestCase).options(
        joinedload(ApiTestCase.api_document)
    ).filter(ApiTestCase.id == testcase_id).first()

    if testcase:
        _parse_testcase_json_fields(testcase)

    return testcase


def get_api_testcases(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    project_id: Optional[int] = None,
    api_document_id: Optional[int] = None,
    title: Optional[str] = None,
    status: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None
) -> List[ApiTestCase]:
    """获取接口测试用例列表"""
    query = db.query(ApiTestCase).options(
        joinedload(ApiTestCase.api_document)
    )

    if project_id:
        query = query.filter(ApiTestCase.project_id == project_id)

    if api_document_id:
        query = query.filter(ApiTestCase.api_document_id == api_document_id)

    if title:
        query = query.filter(ApiTestCase.title.ilike(f"%{title}%"))

    if status:
        query = query.filter(ApiTestCase.status == status)

    if start_date and end_date:
        query = query.filter(ApiTestCase.created_at.between(start_date, end_date))
    elif start_date:
        query = query.filter(ApiTestCase.created_at >= start_date)
    elif end_date:
        query = query.filter(ApiTestCase.created_at <= end_date)

    testcases = query.order_by(desc(ApiTestCase.created_at)).offset(skip).limit(limit).all()

    # 解析JSON字段
    for testcase in testcases:
        _parse_testcase_json_fields(testcase)

    return testcases


def get_api_testcases_count(
    db: Session,
    project_id: Optional[int] = None,
    api_document_id: Optional[int] = None,
    title: Optional[str] = None,
    status: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None
) -> int:
    """获取接口测试用例总数"""
    query = db.query(ApiTestCase)

    if project_id:
        query = query.filter(ApiTestCase.project_id == project_id)

    if api_document_id:
        query = query.filter(ApiTestCase.api_document_id == api_document_id)

    if title:
        query = query.filter(ApiTestCase.title.ilike(f"%{title}%"))

    if status:
        query = query.filter(ApiTestCase.status == status)

    if start_date and end_date:
        query = query.filter(ApiTestCase.created_at.between(start_date, end_date))
    elif start_date:
        query = query.filter(ApiTestCase.created_at >= start_date)
    elif end_date:
        query = query.filter(ApiTestCase.created_at <= end_date)

    return query.count()


def create_api_testcase(db: Session, testcase: ApiTestCaseCreate) -> ApiTestCase:
    """创建接口测试用例"""
    # 将字典类型的字段转换为JSON字符串
    headers = json.dumps(testcase.headers) if testcase.headers else None
    params = json.dumps(testcase.params) if testcase.params else None
    body = json.dumps(testcase.body) if testcase.body else None
    expected_response = json.dumps(testcase.expected_response) if testcase.expected_response else None
    validation_rules = json.dumps(testcase.validation_rules) if testcase.validation_rules else None
    test_data = json.dumps(testcase.test_data) if testcase.test_data else None
    tags = json.dumps(testcase.tags) if testcase.tags else None

    db_testcase = ApiTestCase(
        title=testcase.title,
        description=testcase.description,
        api_path=testcase.api_path,
        method=testcase.method,
        headers=headers,
        params=params,
        body=body,
        expected_status=testcase.expected_status,
        expected_response=expected_response,
        validation_rules=validation_rules,
        test_data=test_data,
        priority=testcase.priority,
        tags=tags,
        api_document_id=testcase.api_document_id,
        project_id=testcase.project_id,
        status=testcase.status,
        creator=testcase.creator
    )
    db.add(db_testcase)
    db.commit()
    db.refresh(db_testcase)
    return db_testcase


def update_api_testcase(db: Session, testcase_id: int, testcase: ApiTestCaseUpdate) -> Optional[ApiTestCase]:
    """更新接口测试用例"""
    db_testcase = get_api_testcase(db, testcase_id)
    if not db_testcase:
        return None

    update_data = testcase.model_dump(exclude_unset=True)
    
    # 将字典类型的字段转换为JSON字符串
    if "headers" in update_data and update_data["headers"] is not None:
        update_data["headers"] = json.dumps(update_data["headers"])
    if "params" in update_data and update_data["params"] is not None:
        update_data["params"] = json.dumps(update_data["params"])
    if "body" in update_data and update_data["body"] is not None:
        update_data["body"] = json.dumps(update_data["body"])
    if "expected_response" in update_data and update_data["expected_response"] is not None:
        update_data["expected_response"] = json.dumps(update_data["expected_response"])
    if "validation_rules" in update_data and update_data["validation_rules"] is not None:
        update_data["validation_rules"] = json.dumps(update_data["validation_rules"])

    for key, value in update_data.items():
        setattr(db_testcase, key, value)

    db.add(db_testcase)
    db.commit()
    db.refresh(db_testcase)
    return db_testcase


def delete_api_testcase(db: Session, testcase_id: int) -> Optional[ApiTestCase]:
    """删除接口测试用例"""
    db_testcase = get_api_testcase(db, testcase_id)
    if not db_testcase:
        return None

    db.delete(db_testcase)
    db.commit()
    return db_testcase


# 接口测试执行结果服务
def get_api_test_execution(db: Session, execution_id: int) -> Optional[ApiTestExecution]:
    """获取接口测试执行结果"""
    return db.query(ApiTestExecution).options(
        joinedload(ApiTestExecution.api_testcase)
    ).filter(ApiTestExecution.id == execution_id).first()


def get_api_test_executions(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    api_testcase_id: Optional[int] = None,
    status: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None
) -> List[ApiTestExecution]:
    """获取接口测试执行结果列表"""
    query = db.query(ApiTestExecution).options(
        joinedload(ApiTestExecution.api_testcase)
    )

    if api_testcase_id:
        query = query.filter(ApiTestExecution.api_testcase_id == api_testcase_id)

    if status:
        query = query.filter(ApiTestExecution.status == status)

    if start_date and end_date:
        query = query.filter(ApiTestExecution.executed_at.between(start_date, end_date))
    elif start_date:
        query = query.filter(ApiTestExecution.executed_at >= start_date)
    elif end_date:
        query = query.filter(ApiTestExecution.executed_at <= end_date)

    return query.order_by(desc(ApiTestExecution.executed_at)).offset(skip).limit(limit).all()


def create_api_test_execution(db: Session, execution: ApiTestExecutionCreate) -> ApiTestExecution:
    """创建接口测试执行结果"""
    # 将字典类型的字段转换为JSON字符串
    actual_response = json.dumps(execution.actual_response) if execution.actual_response else None

    db_execution = ApiTestExecution(
        api_testcase_id=execution.api_testcase_id,
        status=execution.status,
        actual_status=execution.actual_status,
        actual_response=actual_response,
        execution_time=execution.execution_time,
        error_message=execution.error_message,
        executed_by=execution.executed_by
    )
    db.add(db_execution)
    db.commit()
    db.refresh(db_execution)
    return db_execution


# 接口测试脚本服务
def get_api_test_script(db: Session, script_id: int) -> Optional[ApiTestScript]:
    """获取接口测试脚本"""
    return db.query(ApiTestScript).options(
        joinedload(ApiTestScript.api_document),
        joinedload(ApiTestScript.project)
    ).filter(ApiTestScript.id == script_id).first()


def get_api_test_scripts(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    project_id: Optional[int] = None,
    api_document_id: Optional[int] = None,
    script_type: Optional[str] = None,
    title: Optional[str] = None
) -> List[ApiTestScript]:
    """获取接口测试脚本列表"""
    query = db.query(ApiTestScript).options(
        joinedload(ApiTestScript.api_document),
        joinedload(ApiTestScript.project)
    )

    if project_id:
        query = query.filter(ApiTestScript.project_id == project_id)

    if api_document_id:
        query = query.filter(ApiTestScript.api_document_id == api_document_id)

    if script_type:
        query = query.filter(ApiTestScript.script_type == script_type)

    if title:
        query = query.filter(ApiTestScript.title.ilike(f"%{title}%"))

    return query.order_by(desc(ApiTestScript.created_at)).offset(skip).limit(limit).all()


def create_api_test_script(db: Session, script: ApiTestScriptCreate) -> ApiTestScript:
    """创建接口测试脚本"""
    db_script = ApiTestScript(
        title=script.title,
        description=script.description,
        script_type=script.script_type,
        script_content=script.script_content,
        requirements=script.requirements,
        api_document_id=script.api_document_id,
        project_id=script.project_id,
        is_executable=script.is_executable,
        creator=script.creator
    )
    db.add(db_script)
    db.commit()
    db.refresh(db_script)
    return db_script


def update_api_test_script(db: Session, script_id: int, script: ApiTestScriptUpdate) -> Optional[ApiTestScript]:
    """更新接口测试脚本"""
    db_script = get_api_test_script(db, script_id)
    if not db_script:
        return None

    update_data = script.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_script, key, value)

    db.add(db_script)
    db.commit()
    db.refresh(db_script)
    return db_script


def delete_api_test_script(db: Session, script_id: int) -> Optional[ApiTestScript]:
    """删除接口测试脚本"""
    db_script = get_api_test_script(db, script_id)
    if not db_script:
        return None

    db.delete(db_script)
    db.commit()
    return db_script


# 批量操作服务
def create_api_testcases_batch(db: Session, testcases: List[ApiTestCaseCreate]) -> List[ApiTestCase]:
    """批量创建接口测试用例"""
    db_testcases = []
    for testcase in testcases:
        db_testcase = create_api_testcase(db, testcase)
        db_testcases.append(db_testcase)
    return db_testcases


def get_api_testcases_by_ids(db: Session, testcase_ids: List[int]) -> List[ApiTestCase]:
    """根据ID列表获取测试用例"""
    return db.query(ApiTestCase).filter(ApiTestCase.id.in_(testcase_ids)).all()


def export_api_testcases_to_dict(testcases: List[ApiTestCase]) -> List[Dict[str, Any]]:
    """将测试用例导出为字典格式"""
    result = []
    for testcase in testcases:
        testcase_dict = {
            "id": testcase.id,
            "title": testcase.title,
            "description": testcase.description,
            "api_path": testcase.api_path,
            "method": testcase.method,
            "headers": json.loads(testcase.headers) if testcase.headers else {},
            "params": json.loads(testcase.params) if testcase.params else {},
            "body": json.loads(testcase.body) if testcase.body else {},
            "expected_status": testcase.expected_status,
            "expected_response": json.loads(testcase.expected_response) if testcase.expected_response else {},
            "validation_rules": json.loads(testcase.validation_rules) if testcase.validation_rules else [],
            "test_data": json.loads(testcase.test_data) if testcase.test_data else {},
            "priority": testcase.priority,
            "tags": json.loads(testcase.tags) if testcase.tags else [],
            "status": testcase.status,
            "creator": testcase.creator,
            "created_at": testcase.created_at.isoformat() if testcase.created_at else None,
            "updated_at": testcase.updated_at.isoformat() if testcase.updated_at else None
        }
        result.append(testcase_dict)
    return result
