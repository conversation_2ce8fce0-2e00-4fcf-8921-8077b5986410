"""
Python测试脚本生成器

该模块提供了生成不同类型Python测试脚本的功能，支持：
1. pytest框架脚本
2. unittest框架脚本  
3. requests库脚本
"""

import json
from typing import List, Dict, Any
from app.models.api_test import ApiTestCase


class PythonScriptGenerator:
    """Python测试脚本生成器"""
    
    def __init__(self):
        self.base_imports = {
            'pytest': [
                'import pytest',
                'import requests',
                'import json',
                'from typing import Dict, Any'
            ],
            'unittest': [
                'import unittest',
                'import requests', 
                'import json',
                'from typing import Dict, Any'
            ],
            'requests': [
                'import requests',
                'import json',
                'from typing import Dict, Any'
            ]
        }
    
    def generate_script(self, testcases: List[ApiTestCase], script_type: str = 'pytest') -> str:
        """
        生成Python测试脚本
        
        Args:
            testcases: 测试用例列表
            script_type: 脚本类型 (pytest/unittest/requests)
            
        Returns:
            生成的Python脚本代码
        """
        if script_type == 'pytest':
            return self._generate_pytest_script(testcases)
        elif script_type == 'unittest':
            return self._generate_unittest_script(testcases)
        elif script_type == 'requests':
            return self._generate_requests_script(testcases)
        else:
            raise ValueError(f"不支持的脚本类型: {script_type}")
    
    def _generate_pytest_script(self, testcases: List[ApiTestCase]) -> str:
        """生成pytest格式的测试脚本"""
        script_lines = []
        
        # 添加文件头注释
        script_lines.extend([
            '"""',
            'API接口测试脚本 - Pytest版本',
            '',
            '该脚本使用pytest框架进行API接口测试',
            '运行方式: pytest test_api.py -v',
            '"""',
            ''
        ])
        
        # 添加导入语句
        script_lines.extend(self.base_imports['pytest'])
        script_lines.extend([
            '',
            '',
            'class TestAPIEndpoints:',
            '    """API接口测试类"""',
            '    ',
            '    @pytest.fixture(scope="class")',
            '    def base_url(self):',
            '        """基础URL配置"""',
            '        return "http://localhost:8000"  # 请根据实际情况修改',
            '    ',
            '    @pytest.fixture(scope="class")',
            '    def session(self):',
            '        """HTTP会话配置"""',
            '        session = requests.Session()',
            '        session.headers.update({"Content-Type": "application/json"})',
            '        return session',
            ''
        ])
        
        # 为每个测试用例生成测试方法
        for i, testcase in enumerate(testcases):
            method_name = self._sanitize_method_name(testcase.title)
            script_lines.extend(self._generate_pytest_method(testcase, method_name))
            if i < len(testcases) - 1:
                script_lines.append('')
        
        return '\n'.join(script_lines)
    
    def _generate_unittest_script(self, testcases: List[ApiTestCase]) -> str:
        """生成unittest格式的测试脚本"""
        script_lines = []
        
        # 添加文件头注释
        script_lines.extend([
            '"""',
            'API接口测试脚本 - Unittest版本',
            '',
            '该脚本使用unittest框架进行API接口测试',
            '运行方式: python test_api.py',
            '"""',
            ''
        ])
        
        # 添加导入语句
        script_lines.extend(self.base_imports['unittest'])
        script_lines.extend([
            '',
            '',
            'class TestAPIEndpoints(unittest.TestCase):',
            '    """API接口测试类"""',
            '    ',
            '    def setUp(self):',
            '        """测试前置设置"""',
            '        self.base_url = "http://localhost:8000"  # 请根据实际情况修改',
            '        self.session = requests.Session()',
            '        self.session.headers.update({"Content-Type": "application/json"})',
            '    ',
            '    def tearDown(self):',
            '        """测试后置清理"""',
            '        self.session.close()',
            ''
        ])
        
        # 为每个测试用例生成测试方法
        for i, testcase in enumerate(testcases):
            method_name = self._sanitize_method_name(testcase.title)
            script_lines.extend(self._generate_unittest_method(testcase, method_name))
            if i < len(testcases) - 1:
                script_lines.append('')
        
        # 添加main函数
        script_lines.extend([
            '',
            '',
            'if __name__ == "__main__":',
            '    unittest.main()'
        ])
        
        return '\n'.join(script_lines)
    
    def _generate_requests_script(self, testcases: List[ApiTestCase]) -> str:
        """生成requests库格式的测试脚本"""
        script_lines = []
        
        # 添加文件头注释
        script_lines.extend([
            '"""',
            'API接口测试脚本 - Requests版本',
            '',
            '该脚本使用requests库进行API接口测试',
            '运行方式: python test_api.py',
            '"""',
            ''
        ])
        
        # 添加导入语句
        script_lines.extend(self.base_imports['requests'])
        script_lines.extend([
            '',
            '',
            '# 配置',
            'BASE_URL = "http://localhost:8000"  # 请根据实际情况修改',
            'SESSION = requests.Session()',
            'SESSION.headers.update({"Content-Type": "application/json"})',
            '',
            '',
            'def run_all_tests():',
            '    """运行所有测试"""',
            '    print("开始执行API接口测试...")',
            '    print("=" * 50)',
            '    ',
            '    total_tests = 0',
            '    passed_tests = 0',
            '    failed_tests = 0',
            ''
        ])
        
        # 为每个测试用例生成测试函数
        for testcase in testcases:
            function_name = self._sanitize_method_name(testcase.title)
            script_lines.extend(self._generate_requests_function(testcase, function_name))
            script_lines.extend([
                f'    # 执行测试: {testcase.title}',
                f'    total_tests += 1',
                f'    try:',
                f'        {function_name}()',
                f'        passed_tests += 1',
                f'        print(f"✓ {testcase.title} - 通过")',
                f'    except Exception as e:',
                f'        failed_tests += 1',
                f'        print(f"✗ {testcase.title} - 失败: {{e}}")',
                f'    ',
            ])
        
        # 添加结果统计
        script_lines.extend([
            '    print("=" * 50)',
            '    print(f"测试完成! 总计: {total_tests}, 通过: {passed_tests}, 失败: {failed_tests}")',
            '',
            '',
        ])
        
        # 为每个测试用例生成具体的测试函数
        for testcase in testcases:
            function_name = self._sanitize_method_name(testcase.title)
            script_lines.extend(self._generate_requests_test_function(testcase, function_name))
            script_lines.append('')
        
        # 添加main函数
        script_lines.extend([
            '',
            'if __name__ == "__main__":',
            '    run_all_tests()'
        ])
        
        return '\n'.join(script_lines)
    
    def _generate_pytest_method(self, testcase: ApiTestCase, method_name: str) -> List[str]:
        """生成pytest测试方法"""
        lines = []
        
        # 解析JSON字段
        headers = self._parse_json_field(testcase.headers)
        params = self._parse_json_field(testcase.params)
        body = self._parse_json_field(testcase.body)
        
        lines.extend([
            f'    def test_{method_name}(self, base_url, session):',
            f'        """测试: {testcase.title}"""',
            f'        url = f"{{base_url}}{testcase.api_path}"',
            f'        ',
        ])
        
        # 添加请求参数
        if headers:
            lines.append(f'        headers = {json.dumps(headers, ensure_ascii=False, indent=8)[8:]}')
        if params:
            lines.append(f'        params = {json.dumps(params, ensure_ascii=False, indent=8)[8:]}')
        if body:
            lines.append(f'        data = {json.dumps(body, ensure_ascii=False, indent=8)[8:]}')
        
        lines.append('        ')
        
        # 添加请求调用
        method = testcase.method.lower()
        request_args = ['url']
        if headers:
            request_args.append('headers=headers')
        if params:
            request_args.append('params=params')
        if body and method in ['post', 'put', 'patch']:
            request_args.append('json=data')
        
        lines.extend([
            f'        response = session.{method}({", ".join(request_args)})',
            f'        ',
            f'        # 验证响应',
            f'        assert response.status_code == {testcase.expected_status}, f"期望状态码{{testcase.expected_status}}，实际{{response.status_code}}"',
            f'        ',
            f'        # 可以添加更多验证逻辑',
            f'        print(f"测试通过: {testcase.title}")'
        ])
        
        return lines
    
    def _generate_unittest_method(self, testcase: ApiTestCase, method_name: str) -> List[str]:
        """生成unittest测试方法"""
        lines = []
        
        # 解析JSON字段
        headers = self._parse_json_field(testcase.headers)
        params = self._parse_json_field(testcase.params)
        body = self._parse_json_field(testcase.body)
        
        lines.extend([
            f'    def test_{method_name}(self):',
            f'        """测试: {testcase.title}"""',
            f'        url = f"{{self.base_url}}{testcase.api_path}"',
            f'        ',
        ])
        
        # 添加请求参数
        if headers:
            lines.append(f'        headers = {json.dumps(headers, ensure_ascii=False, indent=8)[8:]}')
        if params:
            lines.append(f'        params = {json.dumps(params, ensure_ascii=False, indent=8)[8:]}')
        if body:
            lines.append(f'        data = {json.dumps(body, ensure_ascii=False, indent=8)[8:]}')
        
        lines.append('        ')
        
        # 添加请求调用
        method = testcase.method.lower()
        request_args = ['url']
        if headers:
            request_args.append('headers=headers')
        if params:
            request_args.append('params=params')
        if body and method in ['post', 'put', 'patch']:
            request_args.append('json=data')
        
        lines.extend([
            f'        response = self.session.{method}({", ".join(request_args)})',
            f'        ',
            f'        # 验证响应',
            f'        self.assertEqual(response.status_code, {testcase.expected_status}, f"期望状态码{{testcase.expected_status}}，实际{{response.status_code}}")',
            f'        ',
            f'        # 可以添加更多验证逻辑',
            f'        print(f"测试通过: {testcase.title}")'
        ])
        
        return lines
    
    def _generate_requests_function(self, testcase: ApiTestCase, function_name: str) -> List[str]:
        """生成requests测试函数声明"""
        return []  # 在run_all_tests中已经处理
    
    def _generate_requests_test_function(self, testcase: ApiTestCase, function_name: str) -> List[str]:
        """生成requests具体测试函数"""
        lines = []
        
        # 解析JSON字段
        headers = self._parse_json_field(testcase.headers)
        params = self._parse_json_field(testcase.params)
        body = self._parse_json_field(testcase.body)
        
        lines.extend([
            f'def {function_name}():',
            f'    """测试: {testcase.title}"""',
            f'    url = f"{{BASE_URL}}{testcase.api_path}"',
            f'    ',
        ])
        
        # 添加请求参数
        if headers:
            lines.append(f'    headers = {json.dumps(headers, ensure_ascii=False, indent=4)[4:]}')
        if params:
            lines.append(f'    params = {json.dumps(params, ensure_ascii=False, indent=4)[4:]}')
        if body:
            lines.append(f'    data = {json.dumps(body, ensure_ascii=False, indent=4)[4:]}')
        
        lines.append('    ')
        
        # 添加请求调用
        method = testcase.method.lower()
        request_args = ['url']
        if headers:
            request_args.append('headers=headers')
        if params:
            request_args.append('params=params')
        if body and method in ['post', 'put', 'patch']:
            request_args.append('json=data')
        
        lines.extend([
            f'    response = SESSION.{method}({", ".join(request_args)})',
            f'    ',
            f'    # 验证响应',
            f'    if response.status_code != {testcase.expected_status}:',
            f'        raise AssertionError(f"期望状态码{{testcase.expected_status}}，实际{{response.status_code}}")',
            f'    ',
            f'    # 可以添加更多验证逻辑',
            f'    return True'
        ])
        
        return lines
    
    def _sanitize_method_name(self, title: str) -> str:
        """清理方法名，确保符合Python命名规范"""
        # 移除特殊字符，替换为下划线
        import re
        name = re.sub(r'[^\w\u4e00-\u9fff]', '_', title)
        # 移除连续的下划线
        name = re.sub(r'_+', '_', name)
        # 移除开头和结尾的下划线
        name = name.strip('_')
        # 如果为空或以数字开头，添加前缀
        if not name or name[0].isdigit():
            name = f'test_{name}'
        return name.lower()
    
    def _parse_json_field(self, field_value) -> Dict[str, Any]:
        """解析JSON字段"""
        if not field_value:
            return {}
        
        if isinstance(field_value, str):
            try:
                return json.loads(field_value)
            except:
                return {}
        elif isinstance(field_value, dict):
            return field_value
        else:
            return {}
