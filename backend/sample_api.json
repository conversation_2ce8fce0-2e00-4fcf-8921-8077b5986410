{"openapi": "3.0.0", "info": {"title": "用户管理API", "description": "一个简单的用户管理系统API", "version": "1.0.0"}, "servers": [{"url": "http://localhost:8000", "description": "开发服务器"}], "paths": {"/api/users": {"get": {"summary": "获取用户列表", "description": "获取系统中所有用户的列表", "parameters": [{"name": "page", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "limit", "in": "query", "description": "每页数量", "required": false, "schema": {"type": "integer", "default": 10}}], "responses": {"200": {"description": "成功返回用户列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"users": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}, "total": {"type": "integer"}}}}}}}}, "post": {"summary": "创建新用户", "description": "在系统中创建一个新用户", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreate"}}}}, "responses": {"201": {"description": "用户创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "400": {"description": "请求参数错误"}}}}, "/api/users/{userId}": {"get": {"summary": "获取用户详情", "description": "根据用户ID获取用户详细信息", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功返回用户详情", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "404": {"description": "用户不存在"}}}, "put": {"summary": "更新用户信息", "description": "更新指定用户的信息", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdate"}}}}, "responses": {"200": {"description": "用户更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "404": {"description": "用户不存在"}}}, "delete": {"summary": "删除用户", "description": "删除指定的用户", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "integer"}}], "responses": {"204": {"description": "用户删除成功"}, "404": {"description": "用户不存在"}}}}}, "components": {"schemas": {"User": {"type": "object", "properties": {"id": {"type": "integer", "description": "用户ID"}, "username": {"type": "string", "description": "用户名"}, "email": {"type": "string", "format": "email", "description": "邮箱地址"}, "full_name": {"type": "string", "description": "全名"}, "is_active": {"type": "boolean", "description": "是否激活"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}}}, "UserCreate": {"type": "object", "required": ["username", "email", "password"], "properties": {"username": {"type": "string", "description": "用户名"}, "email": {"type": "string", "format": "email", "description": "邮箱地址"}, "password": {"type": "string", "description": "密码"}, "full_name": {"type": "string", "description": "全名"}}}, "UserUpdate": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "description": "邮箱地址"}, "full_name": {"type": "string", "description": "全名"}, "is_active": {"type": "boolean", "description": "是否激活"}}}}}}