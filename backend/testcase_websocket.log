2025-05-17 23:06:04,693 - testcases_websocket - INFO - 收到WebSocket连接请求: /api/v1/testcases/generate, 客户端: 127.0.0.1:54235
2025-05-17 23:06:04,694 - testcases_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5173', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'Adj59wiD6otcMkWzXz2TBg==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-05-17 23:06:04,694 - testcases_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:54235
2025-05-17 23:06:04,697 - testcases_websocket - INFO - 发送测试消息成功
2025-05-17 23:06:04,698 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-17 23:06:04,704 - testcases_websocket - INFO - 收到前端请求: {'id': 15, 'project_id': 2, 'title': '密码重置功能', 'description': '实现用户密码重置功能', 'reviewer': '王五', 'scenario': '实现用户密码重置功能', 'task': '根据需求生成测试用例'}
2025-05-17 23:06:04,704 - testcases_websocket - INFO - 开始生成测试用例，需求ID: 15
2025-05-17 23:06:04,704 - testcases_websocket - INFO - 开始启动测试用例生成运行时
2025-05-17 23:06:04,705 - testcases_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-05-17 23:06:04,705 - testcases_websocket - INFO - JSON字符串长度: 196
2025-05-17 23:06:04,705 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-17 23:06:04,705 - testcases_websocket - INFO - JSON字符串长度: 157
2025-05-17 23:07:17,001 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-17 23:07:17,001 - testcases_websocket - INFO - JSON字符串长度: 1820
2025-05-17 23:07:17,103 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-17 23:07:17,104 - testcases_websocket - INFO - JSON字符串长度: 1329
2025-05-17 23:07:17,206 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-17 23:07:17,207 - testcases_websocket - INFO - JSON字符串长度: 1545
2025-05-17 23:07:17,309 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-17 23:07:17,309 - testcases_websocket - INFO - JSON字符串长度: 1288
2025-05-17 23:07:17,410 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-17 23:07:17,410 - testcases_websocket - INFO - JSON字符串长度: 1281
2025-05-17 23:07:17,512 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-17 23:07:17,513 - testcases_websocket - INFO - JSON字符串长度: 1298
2025-05-17 23:07:17,614 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-17 23:07:17,615 - testcases_websocket - INFO - JSON字符串长度: 1236
2025-05-17 23:07:17,717 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-17 23:07:17,717 - testcases_websocket - INFO - JSON字符串长度: 1642
2025-05-17 23:07:17,819 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-17 23:07:17,820 - testcases_websocket - INFO - JSON字符串长度: 216
2025-05-17 23:07:17,821 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-17 23:07:17,821 - testcases_websocket - INFO - JSON字符串长度: 157
2025-05-17 23:07:59,486 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-17 23:07:59,486 - testcases_websocket - INFO - JSON字符串长度: 1377
2025-05-17 23:07:59,589 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-17 23:07:59,589 - testcases_websocket - INFO - JSON字符串长度: 2007
2025-05-17 23:07:59,691 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-17 23:07:59,692 - testcases_websocket - INFO - JSON字符串长度: 1786
2025-05-17 23:07:59,794 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-17 23:07:59,795 - testcases_websocket - INFO - JSON字符串长度: 1180
2025-05-17 23:07:59,897 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-17 23:07:59,898 - testcases_websocket - INFO - JSON字符串长度: 222
2025-05-17 23:07:59,899 - testcases_websocket - INFO - 发送消息到前端: 用例结构化智能体, 是否最终消息: False
2025-05-17 23:07:59,899 - testcases_websocket - INFO - JSON字符串长度: 162
2025-05-17 23:09:11,701 - testcases_websocket - INFO - 发送消息到前端: 用例结构化智能体, 是否最终消息: False
2025-05-17 23:09:11,701 - testcases_websocket - INFO - JSON字符串长度: 150
2025-05-17 23:09:11,702 - testcases_websocket - INFO - 发送消息到前端: 数据库智能体, 是否最终消息: False
2025-05-17 23:09:11,702 - testcases_websocket - INFO - JSON字符串长度: 150
2025-05-17 23:09:11,702 - testcases_websocket - INFO - 发送消息到前端: generated_testcases, 是否最终消息: False
2025-05-17 23:09:11,702 - testcases_websocket - INFO - JSON字符串长度: 10192
2025-05-17 23:09:11,703 - testcases_websocket - INFO - 发送消息到前端: 数据库智能体, 是否最终消息: True
2025-05-17 23:09:11,703 - testcases_websocket - INFO - JSON字符串长度: 326
2025-05-17 23:09:11,703 - testcases_websocket - INFO - 测试用例生成运行时完成
2025-05-17 23:09:11,704 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-17 23:09:11,722 - testcases_websocket - INFO - 客户端断开连接
2025-05-18 10:57:36,544 - testcases_websocket - INFO - 收到WebSocket连接请求: /api/v1/testcases/generate, 客户端: 127.0.0.1:53630
2025-05-18 10:57:36,546 - testcases_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5173', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'mtR4PWSyRtVyGVh5c98gKw==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-05-18 10:57:36,546 - testcases_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:53630
2025-05-18 10:57:36,547 - testcases_websocket - INFO - 发送测试消息成功
2025-05-18 10:57:36,548 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-18 10:57:36,553 - testcases_websocket - INFO - 收到前端请求: {'id': 16, 'project_id': 2, 'title': '用户注册功能', 'description': '实现新用户注册功能，包括信息验证', 'reviewer': '李四', 'scenario': '实现新用户注册功能，包括信息验证', 'task': '根据需求生成测试用例'}
2025-05-18 10:57:36,553 - testcases_websocket - INFO - 开始生成测试用例，需求ID: 16
2025-05-18 10:57:36,553 - testcases_websocket - INFO - 开始启动测试用例生成运行时
2025-05-18 10:57:36,554 - testcases_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-05-18 10:57:36,554 - testcases_websocket - INFO - JSON字符串长度: 196
2025-05-18 10:57:36,555 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 10:57:36,555 - testcases_websocket - INFO - JSON字符串长度: 157
2025-05-18 10:58:53,025 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 10:58:53,025 - testcases_websocket - INFO - JSON字符串长度: 1084
2025-05-18 10:58:53,127 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 10:58:53,127 - testcases_websocket - INFO - JSON字符串长度: 1940
2025-05-18 10:58:53,229 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 10:58:53,230 - testcases_websocket - INFO - JSON字符串长度: 1896
2025-05-18 10:58:53,333 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 10:58:53,333 - testcases_websocket - INFO - JSON字符串长度: 1716
2025-05-18 10:58:53,436 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 10:58:53,436 - testcases_websocket - INFO - JSON字符串长度: 1698
2025-05-18 10:58:53,538 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 10:58:53,538 - testcases_websocket - INFO - JSON字符串长度: 1650
2025-05-18 10:58:53,640 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 10:58:53,641 - testcases_websocket - INFO - JSON字符串长度: 1702
2025-05-18 10:58:53,743 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 10:58:53,744 - testcases_websocket - INFO - JSON字符串长度: 1321
2025-05-18 10:58:53,846 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 10:58:53,846 - testcases_websocket - INFO - JSON字符串长度: 1144
2025-05-18 10:58:53,948 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 10:58:53,948 - testcases_websocket - INFO - JSON字符串长度: 216
2025-05-18 10:58:53,949 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-18 10:58:53,949 - testcases_websocket - INFO - JSON字符串长度: 157
2025-05-18 10:59:28,808 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-18 10:59:28,808 - testcases_websocket - INFO - JSON字符串长度: 938
2025-05-18 10:59:28,910 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-18 10:59:28,911 - testcases_websocket - INFO - JSON字符串长度: 1920
2025-05-18 10:59:29,013 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-18 10:59:29,014 - testcases_websocket - INFO - JSON字符串长度: 1603
2025-05-18 10:59:29,116 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-18 10:59:29,116 - testcases_websocket - INFO - JSON字符串长度: 626
2025-05-18 10:59:29,218 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-18 10:59:29,219 - testcases_websocket - INFO - JSON字符串长度: 222
2025-05-18 10:59:29,219 - testcases_websocket - INFO - 发送消息到前端: 用例结构化智能体, 是否最终消息: False
2025-05-18 10:59:29,219 - testcases_websocket - INFO - JSON字符串长度: 162
2025-05-18 11:00:51,659 - testcases_websocket - INFO - 发送消息到前端: 用例结构化智能体, 是否最终消息: False
2025-05-18 11:00:51,659 - testcases_websocket - INFO - JSON字符串长度: 150
2025-05-18 11:00:51,660 - testcases_websocket - INFO - 发送消息到前端: 数据库智能体, 是否最终消息: False
2025-05-18 11:00:51,660 - testcases_websocket - INFO - JSON字符串长度: 150
2025-05-18 11:00:51,660 - testcases_websocket - INFO - 发送消息到前端: generated_testcases, 是否最终消息: False
2025-05-18 11:00:51,661 - testcases_websocket - INFO - JSON字符串长度: 12459
2025-05-18 11:00:51,661 - testcases_websocket - INFO - 发送消息到前端: 数据库智能体, 是否最终消息: True
2025-05-18 11:00:51,661 - testcases_websocket - INFO - JSON字符串长度: 326
2025-05-18 11:00:51,661 - testcases_websocket - INFO - 测试用例生成运行时完成
2025-05-18 11:00:51,663 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-18 11:00:51,682 - testcases_websocket - INFO - 客户端断开连接
2025-05-18 11:29:33,569 - testcases_websocket - INFO - 收到WebSocket连接请求: /api/v1/testcases/generate, 客户端: 127.0.0.1:57002
2025-05-18 11:29:33,570 - testcases_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5173', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'pV2JsAz7bhmTrnmI4SKRRA==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-05-18 11:29:33,571 - testcases_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:57002
2025-05-18 11:29:33,572 - testcases_websocket - INFO - 发送测试消息成功
2025-05-18 11:29:33,572 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-18 11:29:33,575 - testcases_websocket - INFO - 收到前端请求: {'id': 15, 'project_id': 2, 'title': '密码重置功能', 'description': '实现用户密码重置功能', 'reviewer': '王五', 'scenario': '实现用户密码重置功能', 'task': '根据需求生成测试用例'}
2025-05-18 11:29:33,576 - testcases_websocket - INFO - 开始生成测试用例，需求ID: 15
2025-05-18 11:29:33,576 - testcases_websocket - INFO - 开始启动测试用例生成运行时
2025-05-18 11:29:33,576 - testcases_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-05-18 11:29:33,576 - testcases_websocket - INFO - JSON字符串长度: 196
2025-05-18 11:29:33,577 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 11:29:33,577 - testcases_websocket - INFO - JSON字符串长度: 157
2025-05-18 11:30:49,124 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 11:30:49,125 - testcases_websocket - INFO - JSON字符串长度: 1892
2025-05-18 11:30:49,227 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 11:30:49,227 - testcases_websocket - INFO - JSON字符串长度: 1503
2025-05-18 11:30:49,328 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 11:30:49,329 - testcases_websocket - INFO - JSON字符串长度: 1998
2025-05-18 11:30:49,432 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 11:30:49,433 - testcases_websocket - INFO - JSON字符串长度: 1905
2025-05-18 11:30:49,535 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 11:30:49,535 - testcases_websocket - INFO - JSON字符串长度: 1888
2025-05-18 11:30:49,636 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 11:30:49,637 - testcases_websocket - INFO - JSON字符串长度: 1903
2025-05-18 11:30:49,738 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 11:30:49,738 - testcases_websocket - INFO - JSON字符串长度: 1808
2025-05-18 11:30:49,840 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 11:30:49,840 - testcases_websocket - INFO - JSON字符串长度: 216
2025-05-18 11:30:49,841 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-18 11:30:49,841 - testcases_websocket - INFO - JSON字符串长度: 157
2025-05-18 11:31:41,039 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-18 11:31:41,040 - testcases_websocket - INFO - JSON字符串长度: 1780
2025-05-18 11:31:41,142 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-18 11:31:41,143 - testcases_websocket - INFO - JSON字符串长度: 1623
2025-05-18 11:31:41,245 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-18 11:31:41,245 - testcases_websocket - INFO - JSON字符串长度: 1370
2025-05-18 11:31:41,346 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-18 11:31:41,348 - testcases_websocket - INFO - JSON字符串长度: 1659
2025-05-18 11:31:41,450 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-18 11:31:41,450 - testcases_websocket - INFO - JSON字符串长度: 1256
2025-05-18 11:31:41,552 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-18 11:31:41,552 - testcases_websocket - INFO - JSON字符串长度: 222
2025-05-18 11:31:41,553 - testcases_websocket - INFO - 发送消息到前端: 用例结构化智能体, 是否最终消息: False
2025-05-18 11:31:41,553 - testcases_websocket - INFO - JSON字符串长度: 162
2025-05-18 11:32:30,087 - testcases_websocket - INFO - 发送消息到前端: 用例结构化智能体, 是否最终消息: False
2025-05-18 11:32:30,088 - testcases_websocket - INFO - JSON字符串长度: 150
2025-05-18 11:32:30,088 - testcases_websocket - INFO - 发送消息到前端: 数据库智能体, 是否最终消息: False
2025-05-18 11:32:30,088 - testcases_websocket - INFO - JSON字符串长度: 150
2025-05-18 11:32:30,089 - testcases_websocket - INFO - 发送消息到前端: generated_testcases, 是否最终消息: False
2025-05-18 11:32:30,089 - testcases_websocket - INFO - JSON字符串长度: 6980
2025-05-18 11:32:30,090 - testcases_websocket - INFO - 发送消息到前端: 数据库智能体, 是否最终消息: True
2025-05-18 11:32:30,090 - testcases_websocket - INFO - JSON字符串长度: 326
2025-05-18 11:32:30,091 - testcases_websocket - INFO - 测试用例生成运行时完成
2025-05-18 11:32:30,091 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-18 11:32:30,109 - testcases_websocket - INFO - 客户端断开连接
2025-05-18 11:50:01,761 - testcases_websocket - INFO - 收到WebSocket连接请求: /api/v1/testcases/generate, 客户端: 127.0.0.1:59313
2025-05-18 11:50:01,763 - testcases_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5173', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'PBOdPOYfBfs3FmkW4n+a+Q==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-05-18 11:50:01,764 - testcases_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:59313
2025-05-18 11:50:01,766 - testcases_websocket - INFO - 发送测试消息成功
2025-05-18 11:50:01,767 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-18 11:50:01,770 - testcases_websocket - INFO - 收到前端请求: {'id': 14, 'project_id': 2, 'title': '文献数据补录', 'description': '支持文献数据补录，逐篇补录和规范化处理，整理完成一本刊的所有文献信息后，标记该刊数据补录完成', 'reviewer': None, 'scenario': '支持文献数据补录，逐篇补录和规范化处理，整理完成一本刊的所有文献信息后，标记该刊数据补录完成', 'task': '根据需求生成测试用例'}
2025-05-18 11:50:01,770 - testcases_websocket - INFO - 设置默认reviewer值为'admin'
2025-05-18 11:50:01,771 - testcases_websocket - INFO - 开始生成测试用例，需求ID: 14
2025-05-18 11:50:01,771 - testcases_websocket - INFO - 开始启动测试用例生成运行时
2025-05-18 11:50:01,777 - testcases_websocket - INFO - 发送消息到前端: error, 是否最终消息: False
2025-05-18 11:50:01,777 - testcases_websocket - INFO - JSON字符串长度: 157
2025-05-18 11:50:01,777 - testcases_websocket - INFO - 发送消息到前端: 数据库智能体, 是否最终消息: True
2025-05-18 11:50:01,777 - testcases_websocket - INFO - JSON字符串长度: 161
2025-05-18 11:50:01,778 - testcases_websocket - ERROR - 测试用例生成运行时错误: 'RequirementMessage' object has no attribute 'creator'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/augment-projects/aiTesting/backend/app/api/endpoints/testcases.py", line 1287, in generate_testcase
    await start_runtime(
  File "/Users/<USER>/Documents/augment-projects/aiTesting/backend/app/api/agent/testcase_agents.py", line 36, in start_runtime
    await original_start_runtime(
  File "/Users/<USER>/Documents/augment-projects/aiTesting/backend/app/agents/testcase_agents.py", line 1290, in start_runtime
    "creator": requirement.creator
               ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/aiTesting/.venv/lib/python3.11/site-packages/pydantic/main.py", line 989, in __getattr__
    raise AttributeError(f'{type(self).__name__!r} object has no attribute {item!r}')
AttributeError: 'RequirementMessage' object has no attribute 'creator'
2025-05-18 11:50:01,780 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-18 11:50:01,783 - testcases_websocket - INFO - 客户端断开连接
2025-05-18 13:29:18,842 - testcases_websocket - INFO - 收到WebSocket连接请求: /api/v1/testcases/generate, 客户端: 127.0.0.1:60300
2025-05-18 13:29:18,843 - testcases_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5173', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': '57rJDAt8I0N+79a/8MIrbA==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-05-18 13:29:18,843 - testcases_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:60300
2025-05-18 13:29:18,845 - testcases_websocket - INFO - 发送测试消息成功
2025-05-18 13:29:18,845 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-18 13:29:18,850 - testcases_websocket - INFO - 收到前端请求: {'id': 14, 'project_id': 2, 'title': '文献数据补录', 'description': '支持文献数据补录，逐篇补录和规范化处理，整理完成一本刊的所有文献信息后，标记该刊数据补录完成', 'reviewer': None, 'scenario': '支持文献数据补录，逐篇补录和规范化处理，整理完成一本刊的所有文献信息后，标记该刊数据补录完成', 'task': '根据需求生成测试用例'}
2025-05-18 13:29:18,850 - testcases_websocket - INFO - 设置默认reviewer值为'admin'
2025-05-18 13:29:18,851 - testcases_websocket - INFO - 开始生成测试用例，需求ID: 14
2025-05-18 13:29:18,851 - testcases_websocket - INFO - 开始启动测试用例生成运行时
2025-05-18 13:29:18,854 - testcases_websocket - INFO - 发送消息到前端: error, 是否最终消息: False
2025-05-18 13:29:18,854 - testcases_websocket - INFO - JSON字符串长度: 157
2025-05-18 13:29:18,855 - testcases_websocket - INFO - 发送消息到前端: 数据库智能体, 是否最终消息: True
2025-05-18 13:29:18,855 - testcases_websocket - INFO - JSON字符串长度: 161
2025-05-18 13:29:18,856 - testcases_websocket - ERROR - 测试用例生成运行时错误: 'RequirementMessage' object has no attribute 'creator'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/augment-projects/aiTesting/backend/app/api/endpoints/testcases.py", line 1287, in generate_testcase
    await start_runtime(
  File "/Users/<USER>/Documents/augment-projects/aiTesting/backend/app/api/agent/testcase_agents.py", line 36, in start_runtime
    await original_start_runtime(
  File "/Users/<USER>/Documents/augment-projects/aiTesting/backend/app/agents/testcase_agents.py", line 1290, in start_runtime
    "creator": requirement.creator
               ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/aiTesting/.venv/lib/python3.11/site-packages/pydantic/main.py", line 989, in __getattr__
    raise AttributeError(f'{type(self).__name__!r} object has no attribute {item!r}')
AttributeError: 'RequirementMessage' object has no attribute 'creator'
2025-05-18 13:29:18,857 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-18 13:29:18,859 - testcases_websocket - INFO - 客户端断开连接
2025-05-18 13:43:54,069 - testcases_websocket - INFO - 收到WebSocket连接请求: /api/v1/testcases/generate, 客户端: 127.0.0.1:61751
2025-05-18 13:43:54,069 - testcases_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5173', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'TNYRwgTStYw1emWiY6fDcg==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-05-18 13:43:54,070 - testcases_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:61751
2025-05-18 13:43:54,071 - testcases_websocket - INFO - 发送测试消息成功
2025-05-18 13:43:54,071 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-18 13:43:54,074 - testcases_websocket - INFO - 收到前端请求: {'id': 14, 'project_id': 2, 'title': '文献数据补录', 'description': '支持文献数据补录，逐篇补录和规范化处理，整理完成一本刊的所有文献信息后，标记该刊数据补录完成', 'reviewer': None, 'scenario': '支持文献数据补录，逐篇补录和规范化处理，整理完成一本刊的所有文献信息后，标记该刊数据补录完成', 'task': '根据需求生成测试用例'}
2025-05-18 13:43:54,075 - testcases_websocket - INFO - 设置默认reviewer值为'admin'
2025-05-18 13:43:54,075 - testcases_websocket - INFO - 开始生成测试用例，需求ID: 14
2025-05-18 13:43:54,075 - testcases_websocket - INFO - 开始启动测试用例生成运行时
2025-05-18 13:43:54,078 - testcases_websocket - INFO - 发送消息到前端: error, 是否最终消息: False
2025-05-18 13:43:54,079 - testcases_websocket - INFO - JSON字符串长度: 193
2025-05-18 13:43:54,079 - testcases_websocket - INFO - 发送消息到前端: 数据库智能体, 是否最终消息: True
2025-05-18 13:43:54,079 - testcases_websocket - INFO - JSON字符串长度: 161
2025-05-18 13:43:54,081 - testcases_websocket - ERROR - 测试用例生成运行时错误: SingleThreadedAgentRuntime.publish_message() got an unexpected keyword argument 'metadata'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/augment-projects/aiTesting/backend/app/api/endpoints/testcases.py", line 1287, in generate_testcase
    await start_runtime(
  File "/Users/<USER>/Documents/augment-projects/aiTesting/backend/app/api/agent/testcase_agents.py", line 36, in start_runtime
    await original_start_runtime(
  File "/Users/<USER>/Documents/augment-projects/aiTesting/backend/app/agents/testcase_agents.py", line 1297, in start_runtime
    await runtime.publish_message(
          ^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: SingleThreadedAgentRuntime.publish_message() got an unexpected keyword argument 'metadata'
2025-05-18 13:43:54,084 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-18 13:43:54,086 - testcases_websocket - INFO - 客户端断开连接
2025-05-18 14:09:58,093 - testcases_websocket - INFO - 收到WebSocket连接请求: /api/v1/testcases/generate, 客户端: 127.0.0.1:64462
2025-05-18 14:09:58,093 - testcases_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5173', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'EpBcBRveH/2xj9KI3gML0w==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-05-18 14:09:58,094 - testcases_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:64462
2025-05-18 14:09:58,096 - testcases_websocket - INFO - 发送测试消息成功
2025-05-18 14:09:58,098 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-18 14:09:58,099 - testcases_websocket - INFO - 收到前端请求: {'id': 15, 'project_id': 2, 'title': '密码重置功能', 'description': '实现用户密码重置功能', 'reviewer': '王五', 'scenario': '实现用户密码重置功能', 'task': '根据需求生成测试用例'}
2025-05-18 14:09:58,100 - testcases_websocket - INFO - 开始生成测试用例，需求ID: 15
2025-05-18 14:09:58,100 - testcases_websocket - INFO - 开始启动测试用例生成运行时
2025-05-18 14:09:58,102 - testcases_websocket - INFO - 发送消息到前端: error, 是否最终消息: False
2025-05-18 14:09:58,102 - testcases_websocket - INFO - JSON字符串长度: 193
2025-05-18 14:09:58,103 - testcases_websocket - INFO - 发送消息到前端: 数据库智能体, 是否最终消息: True
2025-05-18 14:09:58,103 - testcases_websocket - INFO - JSON字符串长度: 161
2025-05-18 14:09:58,104 - testcases_websocket - ERROR - 测试用例生成运行时错误: SingleThreadedAgentRuntime.publish_message() got an unexpected keyword argument 'metadata'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/augment-projects/aiTesting/backend/app/api/endpoints/testcases.py", line 1287, in generate_testcase
    await start_runtime(
  File "/Users/<USER>/Documents/augment-projects/aiTesting/backend/app/api/agent/testcase_agents.py", line 36, in start_runtime
    await original_start_runtime(
  File "/Users/<USER>/Documents/augment-projects/aiTesting/backend/app/agents/testcase_agents.py", line 1209, in start_runtime
    await runtime.publish_message(
          ^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: SingleThreadedAgentRuntime.publish_message() got an unexpected keyword argument 'metadata'
2025-05-18 14:09:58,107 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-18 14:09:58,109 - testcases_websocket - INFO - 客户端断开连接
2025-05-18 14:11:21,358 - testcases_websocket - INFO - 收到WebSocket连接请求: /api/v1/testcases/generate, 客户端: 127.0.0.1:64638
2025-05-18 14:11:21,359 - testcases_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5173', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'JcI26JPwHoflJcIj1ds4ZA==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-05-18 14:11:21,360 - testcases_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:64638
2025-05-18 14:11:21,362 - testcases_websocket - INFO - 发送测试消息成功
2025-05-18 14:11:21,362 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-18 14:11:21,366 - testcases_websocket - INFO - 收到前端请求: {'id': 15, 'project_id': 2, 'title': '密码重置功能', 'description': '实现用户密码重置功能', 'reviewer': '王五', 'scenario': '实现用户密码重置功能', 'task': '根据需求生成测试用例'}
2025-05-18 14:11:21,366 - testcases_websocket - INFO - 开始生成测试用例，需求ID: 15
2025-05-18 14:11:21,366 - testcases_websocket - INFO - 开始启动测试用例生成运行时
2025-05-18 14:12:08,725 - testcases_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-05-18 14:12:08,725 - testcases_websocket - INFO - JSON字符串长度: 196
2025-05-18 14:12:08,727 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 14:12:08,727 - testcases_websocket - INFO - JSON字符串长度: 157
2025-05-18 14:12:08,733 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 14:12:08,734 - testcases_websocket - INFO - JSON字符串长度: 1674
2025-05-18 14:12:08,828 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 14:12:08,828 - testcases_websocket - INFO - JSON字符串长度: 1381
2025-05-18 14:12:08,934 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 14:12:08,935 - testcases_websocket - INFO - JSON字符串长度: 1748
2025-05-18 14:12:09,033 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 14:12:09,033 - testcases_websocket - INFO - JSON字符串长度: 1717
2025-05-18 14:12:09,135 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 14:12:09,135 - testcases_websocket - INFO - JSON字符串长度: 788
2025-05-18 14:12:09,244 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 14:12:09,244 - testcases_websocket - INFO - JSON字符串长度: 216
2025-05-18 14:12:09,244 - testcases_websocket - INFO - 测试用例生成运行时完成
2025-05-18 14:12:09,244 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-18 14:36:22,199 - testcases_websocket - INFO - 客户端断开连接
2025-05-18 15:10:50,993 - testcases_websocket - INFO - 收到WebSocket连接请求: /api/v1/testcases/generate, 客户端: 127.0.0.1:50717
2025-05-18 15:10:50,995 - testcases_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5173', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'uOMNuVbVbjkxIwZEP0Pn7w==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-05-18 15:10:50,995 - testcases_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:50717
2025-05-18 15:10:50,996 - testcases_websocket - INFO - 发送测试消息成功
2025-05-18 15:10:50,997 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-18 15:10:51,001 - testcases_websocket - INFO - 收到前端请求: {'id': 15, 'project_id': 2, 'title': '密码重置功能', 'description': '实现用户密码重置功能', 'reviewer': '王五', 'scenario': '实现用户密码重置功能', 'task': '根据需求生成测试用例'}
2025-05-18 15:10:51,001 - testcases_websocket - INFO - 开始生成测试用例，需求ID: 15
2025-05-18 15:10:51,001 - testcases_websocket - INFO - 开始启动测试用例生成运行时
2025-05-18 15:10:51,013 - testcases_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-05-18 15:10:51,014 - testcases_websocket - INFO - JSON字符串长度: 196
2025-05-18 15:10:51,015 - testcases_websocket - INFO - 测试用例生成运行时完成
2025-05-18 15:10:51,015 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-18 15:43:05,863 - testcases_websocket - INFO - 客户端断开连接
2025-05-18 16:48:42,088 - testcases_websocket - INFO - 收到WebSocket连接请求: /api/v1/testcases/generate, 客户端: 127.0.0.1:56776
2025-05-18 16:48:42,088 - testcases_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5173', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'O1gF6mm4VzkVW73qEOGzVw==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-05-18 16:48:42,089 - testcases_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:56776
2025-05-18 16:48:42,090 - testcases_websocket - INFO - 发送测试消息成功
2025-05-18 16:48:42,090 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-18 16:48:42,096 - testcases_websocket - INFO - 收到前端请求: {'id': 15, 'project_id': 2, 'title': '密码重置功能', 'description': '实现用户密码重置功能', 'reviewer': '王五', 'scenario': '实现用户密码重置功能', 'task': '根据需求生成测试用例'}
2025-05-18 16:48:42,097 - testcases_websocket - INFO - 开始生成测试用例，需求ID: 15
2025-05-18 16:48:42,097 - testcases_websocket - INFO - 开始启动测试用例生成运行时
2025-05-18 16:48:42,119 - testcases_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-05-18 16:48:42,119 - testcases_websocket - INFO - JSON字符串长度: 196
2025-05-18 16:48:42,119 - testcases_websocket - INFO - 测试用例生成运行时完成
2025-05-18 16:48:42,119 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-18 16:52:24,887 - testcases_websocket - INFO - 客户端断开连接
2025-05-18 16:54:55,472 - testcases_websocket - INFO - 收到WebSocket连接请求: /api/v1/testcases/generate, 客户端: 127.0.0.1:59201
2025-05-18 16:54:55,473 - testcases_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5173', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'vX6Jd57H/852fqZ8P4CA2Q==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-05-18 16:54:55,473 - testcases_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:59201
2025-05-18 16:54:55,474 - testcases_websocket - INFO - 发送测试消息成功
2025-05-18 16:54:55,474 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-18 16:54:55,477 - testcases_websocket - INFO - 收到前端请求: {'id': 15, 'project_id': 2, 'title': '密码重置功能', 'description': '实现用户密码重置功能', 'reviewer': '王五', 'scenario': '实现用户密码重置功能', 'task': '根据需求生成测试用例'}
2025-05-18 16:54:55,477 - testcases_websocket - INFO - 开始生成测试用例，需求ID: 15
2025-05-18 16:54:55,477 - testcases_websocket - INFO - 开始启动测试用例生成运行时
2025-05-18 16:54:55,487 - testcases_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-05-18 16:54:55,487 - testcases_websocket - INFO - JSON字符串长度: 196
2025-05-18 16:54:55,487 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 16:54:55,488 - testcases_websocket - INFO - JSON字符串长度: 157
2025-05-18 16:54:55,488 - testcases_websocket - INFO - 发送消息到前端: error, 是否最终消息: False
2025-05-18 16:54:55,488 - testcases_websocket - INFO - JSON字符串长度: 156
2025-05-18 16:54:55,489 - testcases_websocket - INFO - 测试用例生成运行时完成
2025-05-18 16:54:55,489 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-18 17:21:30,263 - testcases_websocket - INFO - 客户端断开连接
2025-05-18 21:20:29,063 - testcases_websocket - INFO - 收到WebSocket连接请求: /api/v1/testcases/generate, 客户端: 127.0.0.1:57425
2025-05-18 21:20:29,063 - testcases_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5174', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'LreiKOeWUOhdFy4h0byFfQ==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-05-18 21:20:29,063 - testcases_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:57425
2025-05-18 21:20:29,065 - testcases_websocket - INFO - 发送测试消息成功
2025-05-18 21:20:29,065 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-18 21:20:29,069 - testcases_websocket - INFO - 收到前端请求: {'id': 15, 'project_id': 2, 'title': '密码重置功能', 'description': '实现用户密码重置功能', 'reviewer': '王五', 'scenario': '实现用户密码重置功能', 'task': '根据需求生成测试用例'}
2025-05-18 21:20:29,070 - testcases_websocket - INFO - 开始生成测试用例，需求ID: 15
2025-05-18 21:20:29,070 - testcases_websocket - INFO - 开始启动测试用例生成运行时
2025-05-18 21:20:29,077 - testcases_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-05-18 21:20:29,077 - testcases_websocket - INFO - JSON字符串长度: 196
2025-05-18 21:20:29,077 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 21:20:29,078 - testcases_websocket - INFO - JSON字符串长度: 157
2025-05-18 21:20:29,078 - testcases_websocket - INFO - 发送消息到前端: error, 是否最终消息: False
2025-05-18 21:20:29,078 - testcases_websocket - INFO - JSON字符串长度: 156
2025-05-18 21:20:29,079 - testcases_websocket - INFO - 测试用例生成运行时完成
2025-05-18 21:20:29,079 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-18 21:28:47,472 - testcases_websocket - INFO - 客户端断开连接
2025-05-18 22:11:07,943 - testcases_websocket - INFO - 收到WebSocket连接请求: /api/v1/testcases/generate, 客户端: 127.0.0.1:54484
2025-05-18 22:11:07,944 - testcases_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5174', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'KU1FmD5ZGv/jck8qEfeTIA==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-05-18 22:11:07,944 - testcases_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:54484
2025-05-18 22:11:07,945 - testcases_websocket - INFO - 发送测试消息成功
2025-05-18 22:11:07,945 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-18 22:11:07,954 - testcases_websocket - INFO - 收到前端请求: {'id': 15, 'project_id': 2, 'title': '密码重置功能', 'description': '实现用户密码重置功能', 'reviewer': '王五', 'scenario': '实现用户密码重置功能', 'task': '根据需求生成测试用例'}
2025-05-18 22:11:07,955 - testcases_websocket - INFO - 开始生成测试用例，需求ID: 15
2025-05-18 22:11:07,956 - testcases_websocket - INFO - 开始启动测试用例生成运行时
2025-05-18 22:12:04,327 - testcases_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-05-18 22:12:04,327 - testcases_websocket - INFO - JSON字符串长度: 196
2025-05-18 22:12:04,331 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 22:12:04,331 - testcases_websocket - INFO - JSON字符串长度: 157
2025-05-18 22:12:04,332 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 22:12:04,332 - testcases_websocket - INFO - JSON字符串长度: 1600
2025-05-18 22:12:04,427 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 22:12:04,427 - testcases_websocket - INFO - JSON字符串长度: 1234
2025-05-18 22:12:04,530 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 22:12:04,530 - testcases_websocket - INFO - JSON字符串长度: 1776
2025-05-18 22:12:04,632 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 22:12:04,633 - testcases_websocket - INFO - JSON字符串长度: 1892
2025-05-18 22:12:04,736 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 22:12:04,736 - testcases_websocket - INFO - JSON字符串长度: 1657
2025-05-18 22:12:04,838 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-18 22:12:04,838 - testcases_websocket - INFO - JSON字符串长度: 216
2025-05-18 22:13:03,817 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-18 22:13:03,817 - testcases_websocket - INFO - JSON字符串长度: 157
2025-05-18 22:13:03,818 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-18 22:13:03,818 - testcases_websocket - INFO - JSON字符串长度: 1550
2025-05-18 22:13:03,918 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-18 22:13:03,919 - testcases_websocket - INFO - JSON字符串长度: 1657
2025-05-18 22:13:04,021 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-18 22:13:04,022 - testcases_websocket - INFO - JSON字符串长度: 1095
2025-05-18 22:13:04,123 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-18 22:13:04,123 - testcases_websocket - INFO - JSON字符串长度: 1338
2025-05-18 22:13:04,225 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-18 22:13:04,225 - testcases_websocket - INFO - JSON字符串长度: 1703
2025-05-18 22:13:04,327 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-18 22:13:04,328 - testcases_websocket - INFO - JSON字符串长度: 537
2025-05-18 22:13:04,431 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-18 22:13:04,432 - testcases_websocket - INFO - JSON字符串长度: 222
2025-05-18 22:13:04,446 - testcases_websocket - INFO - 发送消息到前端: 用例结构化智能体, 是否最终消息: False
2025-05-18 22:13:04,446 - testcases_websocket - INFO - JSON字符串长度: 162
2025-05-18 22:13:04,448 - testcases_websocket - INFO - 发送消息到前端: error, 是否最终消息: False
2025-05-18 22:13:04,448 - testcases_websocket - INFO - JSON字符串长度: 160
2025-05-18 22:13:04,449 - testcases_websocket - INFO - 测试用例生成运行时完成
2025-05-18 22:13:04,450 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-18 22:25:06,072 - testcases_websocket - INFO - 客户端断开连接
2025-05-24 13:55:22,843 - testcases_websocket - INFO - 收到WebSocket连接请求: /api/v1/testcases/generate, 客户端: 127.0.0.1:59146
2025-05-24 13:55:22,844 - testcases_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5174', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': '+A7ljiH0y5GUMxCz98Zdfg==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-05-24 13:55:22,845 - testcases_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:59146
2025-05-24 13:55:22,846 - testcases_websocket - INFO - 发送测试消息成功
2025-05-24 13:55:22,846 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-24 13:55:22,850 - testcases_websocket - INFO - 收到前端请求: {'id': 15, 'project_id': 2, 'title': '密码重置功能', 'description': '实现用户密码重置功能', 'reviewer': '王五', 'scenario': '实现用户密码重置功能', 'task': '根据需求生成测试用例'}
2025-05-24 13:55:22,851 - testcases_websocket - INFO - 开始生成测试用例，需求ID: 15
2025-05-24 13:55:22,851 - testcases_websocket - INFO - 开始启动测试用例生成运行时
2025-05-24 13:55:22,851 - testcases_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-05-24 13:55:22,851 - testcases_websocket - INFO - JSON字符串长度: 196
2025-05-24 13:55:22,851 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-24 13:55:22,852 - testcases_websocket - INFO - JSON字符串长度: 157
2025-05-24 13:56:32,537 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-24 13:56:32,538 - testcases_websocket - INFO - JSON字符串长度: 1889
2025-05-24 13:56:32,639 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-24 13:56:32,640 - testcases_websocket - INFO - JSON字符串长度: 1564
2025-05-24 13:56:32,742 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-24 13:56:32,743 - testcases_websocket - INFO - JSON字符串长度: 1587
2025-05-24 13:56:32,845 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-24 13:56:32,845 - testcases_websocket - INFO - JSON字符串长度: 1415
2025-05-24 13:56:32,947 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-24 13:56:32,948 - testcases_websocket - INFO - JSON字符串长度: 1320
2025-05-24 13:56:33,050 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-24 13:56:33,050 - testcases_websocket - INFO - JSON字符串长度: 1287
2025-05-24 13:56:33,152 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-24 13:56:33,153 - testcases_websocket - INFO - JSON字符串长度: 1202
2025-05-24 13:56:33,255 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-24 13:56:33,256 - testcases_websocket - INFO - JSON字符串长度: 1402
2025-05-24 13:56:33,358 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-05-24 13:56:33,358 - testcases_websocket - INFO - JSON字符串长度: 216
2025-05-24 13:56:33,359 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-24 13:56:33,359 - testcases_websocket - INFO - JSON字符串长度: 157
2025-05-24 13:57:23,895 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-24 13:57:23,895 - testcases_websocket - INFO - JSON字符串长度: 1598
2025-05-24 13:57:23,997 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-24 13:57:23,997 - testcases_websocket - INFO - JSON字符串长度: 1545
2025-05-24 13:57:24,098 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-24 13:57:24,098 - testcases_websocket - INFO - JSON字符串长度: 1829
2025-05-24 13:57:24,200 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-24 13:57:24,201 - testcases_websocket - INFO - JSON字符串长度: 1550
2025-05-24 13:57:24,302 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-24 13:57:24,303 - testcases_websocket - INFO - JSON字符串长度: 1276
2025-05-24 13:57:24,405 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-05-24 13:57:24,405 - testcases_websocket - INFO - JSON字符串长度: 222
2025-05-24 13:57:24,406 - testcases_websocket - INFO - 发送消息到前端: 用例结构化智能体, 是否最终消息: False
2025-05-24 13:57:24,406 - testcases_websocket - INFO - JSON字符串长度: 162
2025-05-24 13:58:35,887 - testcases_websocket - INFO - 发送消息到前端: 用例结构化智能体, 是否最终消息: False
2025-05-24 13:58:35,887 - testcases_websocket - INFO - JSON字符串长度: 150
2025-05-24 13:58:35,887 - testcases_websocket - INFO - 发送消息到前端: 数据库智能体, 是否最终消息: False
2025-05-24 13:58:35,887 - testcases_websocket - INFO - JSON字符串长度: 150
2025-05-24 13:58:35,888 - testcases_websocket - INFO - 发送消息到前端: generated_testcases, 是否最终消息: False
2025-05-24 13:58:35,888 - testcases_websocket - INFO - JSON字符串长度: 10221
2025-05-24 13:58:35,889 - testcases_websocket - INFO - 发送消息到前端: 数据库智能体, 是否最终消息: True
2025-05-24 13:58:35,889 - testcases_websocket - INFO - JSON字符串长度: 326
2025-05-24 13:58:35,889 - testcases_websocket - INFO - 测试用例生成运行时完成
2025-05-24 13:58:35,890 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-24 13:58:35,907 - testcases_websocket - INFO - 客户端断开连接
2025-05-24 20:48:47,470 - testcases_websocket - INFO - 收到WebSocket连接请求: /api/v1/testcases/generate, 客户端: 127.0.0.1:59183
2025-05-24 20:48:47,470 - testcases_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5174', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'RwjsS8g2fegGJKn7Mrp1kg==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-05-24 20:48:47,471 - testcases_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:59183
2025-05-24 20:48:47,473 - testcases_websocket - INFO - 发送测试消息成功
2025-05-24 20:48:47,473 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-24 20:48:47,479 - testcases_websocket - INFO - 收到前端请求: {'id': 14, 'project_id': 2, 'title': '文献数据补录', 'description': '支持文献数据补录，逐篇补录和规范化处理，整理完成一本刊的所有文献信息后，标记该刊数据补录完成', 'reviewer': None, 'scenario': '支持文献数据补录，逐篇补录和规范化处理，整理完成一本刊的所有文献信息后，标记该刊数据补录完成', 'task': '根据需求生成测试用例'}
2025-05-24 20:48:47,479 - testcases_websocket - INFO - 设置默认reviewer值为'admin'
2025-05-24 20:48:47,479 - testcases_websocket - INFO - 开始生成测试用例，需求ID: 14
2025-05-24 20:48:47,480 - testcases_websocket - INFO - 开始启动测试用例生成运行时
2025-05-24 20:48:47,490 - testcases_websocket - INFO - 测试用例生成运行时完成
2025-05-24 20:48:47,490 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-24 21:09:27,717 - testcases_websocket - INFO - 客户端断开连接
2025-05-24 21:18:52,941 - testcases_websocket - INFO - 收到WebSocket连接请求: /api/v1/testcases/generate, 客户端: 127.0.0.1:51086
2025-05-24 21:18:52,944 - testcases_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5174', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'jO+gcbvMc/hI6dGRcqhGdw==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-05-24 21:18:52,945 - testcases_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:51086
2025-05-24 21:18:52,946 - testcases_websocket - INFO - 发送测试消息成功
2025-05-24 21:18:52,946 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-24 21:18:52,951 - testcases_websocket - INFO - 收到前端请求: {'id': 14, 'project_id': 2, 'title': '文献数据补录', 'description': '支持文献数据补录，逐篇补录和规范化处理，整理完成一本刊的所有文献信息后，标记该刊数据补录完成', 'reviewer': None, 'scenario': '支持文献数据补录，逐篇补录和规范化处理，整理完成一本刊的所有文献信息后，标记该刊数据补录完成', 'task': '根据需求生成测试用例'}
2025-05-24 21:18:52,952 - testcases_websocket - INFO - 设置默认reviewer值为'admin'
2025-05-24 21:18:52,952 - testcases_websocket - INFO - 开始生成测试用例，需求ID: 14
2025-05-24 21:18:52,952 - testcases_websocket - INFO - 开始启动测试用例生成运行时
2025-05-24 21:18:52,959 - testcases_websocket - INFO - 测试用例生成运行时完成
2025-05-24 21:18:52,959 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-24 21:59:51,687 - testcases_websocket - INFO - 客户端断开连接
2025-05-31 15:30:43,842 - testcases_websocket - INFO - 收到WebSocket连接请求: /api/v1/testcases/generate, 客户端: 127.0.0.1:58295
2025-05-31 15:30:43,843 - testcases_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5174', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'CGsTSBg9G8miOmXIVISP+w==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-05-31 15:30:43,844 - testcases_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:58295
2025-05-31 15:30:43,846 - testcases_websocket - INFO - 发送测试消息成功
2025-05-31 15:30:43,846 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-31 15:30:43,852 - testcases_websocket - INFO - 收到前端请求: {'id': 15, 'project_id': 2, 'title': '密码重置功能', 'description': '实现用户密码重置功能', 'reviewer': '王五', 'scenario': '实现用户密码重置功能', 'task': '根据需求生成测试用例'}
2025-05-31 15:30:43,853 - testcases_websocket - INFO - 开始生成测试用例，需求ID: 15
2025-05-31 15:30:43,853 - testcases_websocket - INFO - 开始启动测试用例生成运行时
2025-05-31 15:30:43,865 - testcases_websocket - INFO - 测试用例生成运行时完成
2025-05-31 15:30:43,865 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-05-31 15:50:09,310 - testcases_websocket - INFO - 客户端断开连接
2025-06-02 22:34:12,543 - testcases_websocket - INFO - 收到WebSocket连接请求: /api/v1/testcases/generate, 客户端: 127.0.0.1:52625
2025-06-02 22:34:12,545 - testcases_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5174', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'vQ3tGdL5NtS8LrqOSEsszg==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-06-02 22:34:12,546 - testcases_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:52625
2025-06-02 22:34:12,547 - testcases_websocket - INFO - 发送测试消息成功
2025-06-02 22:34:12,547 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-06-02 22:34:12,551 - testcases_websocket - INFO - 收到前端请求: {'id': 19, 'project_id': 3, 'title': '智能系统员工管理功能', 'description': '智能系统新增、编辑、删除员工', 'reviewer': 'admin', 'scenario': '## 1. 需求项 1\n\n录入新员工基本信息并持久化存储。输入包括必填字段（姓名、工号、部门、入职日期）和可选字段（手机号、邮箱、职位）。输出成功时返回员工ID及完整信息，失败时返回错误码及原因。\n\n## 2. 需求项 2\n\n修改已存在员工的可编辑属性。输入包括员工ID和可修改字段（部门、职位、联系方式等）。输出成功时返回更新后的完整记录，失败时返回冲突提示。\n\n## 3. 需求项 3\n\n将员工标记为非活跃状态。输入包括员工ID和删除原因。输出成功时返回软删除成功状态，失败时返回权限不足提示。\n\n\n\n', 'task': '根据需求生成测试用例'}
2025-06-02 22:34:12,551 - testcases_websocket - INFO - 开始生成测试用例，需求ID: 19
2025-06-02 22:34:12,551 - testcases_websocket - INFO - 开始启动测试用例生成运行时
2025-06-02 22:34:12,570 - testcases_websocket - INFO - 测试用例生成运行时完成
2025-06-02 22:34:12,570 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-06-02 22:36:39,531 - testcases_websocket - INFO - 客户端断开连接
2025-06-21 15:55:14,505 - testcases_websocket - INFO - 收到WebSocket连接请求: /api/v1/testcases/generate, 客户端: 127.0.0.1:56688
2025-06-21 15:55:14,506 - testcases_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5174', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'tE8NUqJGQC1JYPT3dOYe2Q==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-06-21 15:55:14,507 - testcases_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:56688
2025-06-21 15:55:14,510 - testcases_websocket - INFO - 发送测试消息成功
2025-06-21 15:55:14,510 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-06-21 15:55:14,513 - testcases_websocket - INFO - 收到前端请求: {'id': 19, 'project_id': 3, 'title': '智能系统员工管理功能', 'description': '智能系统新增、编辑、删除员工', 'reviewer': 'admin', 'scenario': '## 1. 需求项 1\n\n录入新员工基本信息并持久化存储。输入包括必填字段（姓名、工号、部门、入职日期）和可选字段（手机号、邮箱、职位）。输出成功时返回员工ID及完整信息，失败时返回错误码及原因。\n\n## 2. 需求项 2\n\n修改已存在员工的可编辑属性。输入包括员工ID和可修改字段（部门、职位、联系方式等）。输出成功时返回更新后的完整记录，失败时返回冲突提示。\n\n## 3. 需求项 3\n\n将员工标记为非活跃状态。输入包括员工ID和删除原因。输出成功时返回软删除成功状态，失败时返回权限不足提示。\n\n\n\n', 'task': '根据需求生成测试用例'}
2025-06-21 15:55:14,514 - testcases_websocket - INFO - 开始生成测试用例，需求ID: 19
2025-06-21 15:55:14,514 - testcases_websocket - INFO - 开始启动测试用例生成运行时
2025-06-21 15:55:14,534 - testcases_websocket - INFO - 测试用例生成运行时完成
2025-06-21 15:55:14,534 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-06-21 16:02:39,830 - testcases_websocket - INFO - 客户端断开连接
2025-06-21 16:15:02,323 - testcases_websocket - INFO - 收到WebSocket连接请求: /api/v1/testcases/generate, 客户端: 127.0.0.1:59633
2025-06-21 16:15:02,323 - testcases_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5174', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'Al96/3NyKfM29oBZeXSFaA==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-06-21 16:15:02,324 - testcases_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:59633
2025-06-21 16:15:02,326 - testcases_websocket - INFO - 发送测试消息成功
2025-06-21 16:15:02,326 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-06-21 16:15:02,331 - testcases_websocket - INFO - 收到前端请求: {'id': 20, 'project_id': 3, 'title': '智慧系统员工工资管理', 'description': '员工管理，包括增删改查', 'reviewer': 'admin', 'scenario': '## 1. 需求项 1\n\n实现员工信息的增删改查全生命周期管理。输入：新增为姓名、工号、部门、职位等字段表单；修改为员工ID+更新字段；删除为员工ID；查询为姓名/部门筛选条件。输出：创建/更新为操作结果状态+最新员工数据；删除为成功标识或错误原因；查询为员工列表/详情视图。\n\n## 2. 需求项 2\n\n支持Excel格式的批量数据处理。输入：导入为符合模板的Excel文件；导出为当前筛选条件下的数据集。输出：导入为成功计数+错误明细报告；导出为标准Excel文件。\n\n\n\n', 'task': '根据需求生成测试用例'}
2025-06-21 16:15:02,331 - testcases_websocket - INFO - 开始生成测试用例，需求ID: 20
2025-06-21 16:15:02,331 - testcases_websocket - INFO - 开始启动测试用例生成运行时
2025-06-21 16:15:02,331 - testcases_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-06-21 16:15:02,331 - testcases_websocket - INFO - JSON字符串长度: 196
2025-06-21 16:15:02,331 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-06-21 16:15:02,332 - testcases_websocket - INFO - JSON字符串长度: 157
2025-06-21 16:16:01,313 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-06-21 16:16:01,313 - testcases_websocket - INFO - JSON字符串长度: 1929
2025-06-21 16:16:01,415 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-06-21 16:16:01,415 - testcases_websocket - INFO - JSON字符串长度: 1573
2025-06-21 16:16:01,517 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-06-21 16:16:01,517 - testcases_websocket - INFO - JSON字符串长度: 1471
2025-06-21 16:16:01,619 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-06-21 16:16:01,620 - testcases_websocket - INFO - JSON字符串长度: 1679
2025-06-21 16:16:01,721 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-06-21 16:16:01,722 - testcases_websocket - INFO - JSON字符串长度: 1614
2025-06-21 16:16:01,824 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-06-21 16:16:01,824 - testcases_websocket - INFO - JSON字符串长度: 1313
2025-06-21 16:16:01,926 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-06-21 16:16:01,926 - testcases_websocket - INFO - JSON字符串长度: 1550
2025-06-21 16:16:02,028 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-06-21 16:16:02,028 - testcases_websocket - INFO - JSON字符串长度: 216
2025-06-21 16:16:02,028 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-06-21 16:16:02,029 - testcases_websocket - INFO - JSON字符串长度: 157
2025-06-21 16:16:52,565 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-06-21 16:16:52,566 - testcases_websocket - INFO - JSON字符串长度: 653
2025-06-21 16:16:52,668 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-06-21 16:16:52,668 - testcases_websocket - INFO - JSON字符串长度: 310
2025-06-21 16:16:52,770 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-06-21 16:16:52,770 - testcases_websocket - INFO - JSON字符串长度: 1731
2025-06-21 16:16:52,872 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-06-21 16:16:52,872 - testcases_websocket - INFO - JSON字符串长度: 1608
2025-06-21 16:16:52,974 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-06-21 16:16:52,974 - testcases_websocket - INFO - JSON字符串长度: 1602
2025-06-21 16:16:53,076 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-06-21 16:16:53,076 - testcases_websocket - INFO - JSON字符串长度: 1670
2025-06-21 16:16:53,178 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-06-21 16:16:53,178 - testcases_websocket - INFO - JSON字符串长度: 1679
2025-06-21 16:16:53,280 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-06-21 16:16:53,281 - testcases_websocket - INFO - JSON字符串长度: 418
2025-06-21 16:16:53,382 - testcases_websocket - INFO - 发送消息到前端: 测试用例评审智能体, 是否最终消息: False
2025-06-21 16:16:53,383 - testcases_websocket - INFO - JSON字符串长度: 222
2025-06-21 16:16:53,383 - testcases_websocket - INFO - 发送消息到前端: 用例结构化智能体, 是否最终消息: False
2025-06-21 16:16:53,383 - testcases_websocket - INFO - JSON字符串长度: 162
2025-06-21 16:17:50,025 - testcases_websocket - INFO - 发送消息到前端: 用例结构化智能体, 是否最终消息: False
2025-06-21 16:17:50,025 - testcases_websocket - INFO - JSON字符串长度: 150
2025-06-21 16:17:50,025 - testcases_websocket - INFO - 发送消息到前端: 数据库智能体, 是否最终消息: False
2025-06-21 16:17:50,027 - testcases_websocket - INFO - JSON字符串长度: 150
2025-06-21 16:17:50,028 - testcases_websocket - INFO - 发送消息到前端: generated_testcases, 是否最终消息: False
2025-06-21 16:17:50,028 - testcases_websocket - INFO - JSON字符串长度: 9710
2025-06-21 16:17:50,029 - testcases_websocket - INFO - 发送消息到前端: 数据库智能体, 是否最终消息: True
2025-06-21 16:17:50,029 - testcases_websocket - INFO - JSON字符串长度: 326
2025-06-21 16:17:50,029 - testcases_websocket - INFO - 测试用例生成运行时完成
2025-06-21 16:17:50,029 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-06-21 16:17:50,052 - testcases_websocket - INFO - 客户端断开连接
2025-06-28 10:06:20,281 - testcases_websocket - INFO - 收到WebSocket连接请求: /api/v1/testcases/generate, 客户端: 127.0.0.1:51383
2025-06-28 10:06:20,282 - testcases_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5174', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'tjwP6taKvwOhpqrRhNH7AA==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-06-28 10:06:20,283 - testcases_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:51383
2025-06-28 10:06:20,284 - testcases_websocket - INFO - 发送测试消息成功
2025-06-28 10:06:20,285 - testcases_websocket - INFO - 等待前端发送测试用例生成请求
2025-06-28 10:06:20,290 - testcases_websocket - INFO - 收到前端请求: {'id': 18, 'project_id': 2, 'title': '期刊登到信息管理', 'description': '支持期刊登到信息的编辑和查询', 'reviewer': None, 'scenario': '支持期刊登到信息的编辑和查询', 'task': '根据需求生成测试用例'}
2025-06-28 10:06:20,290 - testcases_websocket - INFO - 设置默认reviewer值为'admin'
2025-06-28 10:06:20,291 - testcases_websocket - INFO - 开始生成测试用例，需求ID: 18
2025-06-28 10:06:20,291 - testcases_websocket - INFO - 开始启动测试用例生成运行时
2025-06-28 10:06:20,292 - testcases_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-06-28 10:06:20,293 - testcases_websocket - INFO - JSON字符串长度: 196
2025-06-28 10:06:20,293 - testcases_websocket - INFO - 发送消息到前端: 测试用例生成智能体, 是否最终消息: False
2025-06-28 10:06:20,294 - testcases_websocket - INFO - JSON字符串长度: 157
