2025-06-02 11:23:57,397 - requirements_websocket - INFO - 收到WebSocket连接请求: /api/v1/requirements/analyze, 客户端: 127.0.0.1:53440
2025-06-02 11:23:57,397 - requirements_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5174', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'sjmayAgH1QaREFRenkx0vw==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-06-02 11:23:57,398 - requirements_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:53440
2025-06-02 11:23:57,398 - requirements_websocket - INFO - WebSocket连接已建立，建议客户端设置较长的超时时间
2025-06-02 11:23:57,398 - requirements_websocket - INFO - 建议的WebSocket设置: ping_interval=30秒, ping_timeout=600秒(10分钟)
2025-06-02 11:23:57,399 - requirements_websocket - INFO - 发送测试消息成功
2025-06-02 11:23:57,399 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 11:23:57,402 - requirements_websocket - INFO - 收到前端消息: {"userId":1,"projectId":3,"content":"智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面","files":[],"task":"分析需求文档"...
2025-06-02 11:23:57,402 - requirements_websocket - INFO - 成功解析JSON数据
2025-06-02 11:23:57,402 - requirements_websocket - INFO - 处理前端请求: {'userId': 1, 'projectId': 3, 'content': '智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面', 'files': [], 'task':...
2025-06-02 11:23:57,402 - requirements_websocket - INFO - 用户输入的需求内容长度: 35 字符
2025-06-02 11:23:57,402 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面
2025-06-02 11:23:57,403 - requirements_websocket - INFO - 创建需求文件消息: 用户ID=1, 文件数量=0
2025-06-02 11:23:57,403 - requirements_websocket - INFO - 开始启动需求分析运行时
2025-06-02 11:23:57,403 - requirements_websocket - INFO - 用户输入的需求内容长度: 35 字符
2025-06-02 11:23:57,403 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面
2025-06-02 11:23:57,404 - requirements_websocket - INFO - 发送消息到前端: system, 是否最终消息: False
2025-06-02 11:23:57,404 - requirements_websocket - INFO - JSON字符串长度: 115
2025-06-02 11:23:57,407 - requirements_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-06-02 11:23:57,407 - requirements_websocket - INFO - JSON字符串长度: 142
2025-06-02 11:23:57,408 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 11:23:57,408 - requirements_websocket - INFO - JSON字符串长度: 159
2025-06-02 11:23:57,416 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 11:23:57,416 - requirements_websocket - INFO - JSON字符串长度: 165
2025-06-02 11:25:42,406 - requirements_websocket - ERROR - 发送简单心跳包失败: 
2025-06-02 11:25:42,407 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:25:42,407 - requirements_websocket - WARNING - 心跳包发送失败次数: 1
2025-06-02 11:25:57,409 - requirements_websocket - ERROR - 发送简单心跳包失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:25:57,409 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:25:57,409 - requirements_websocket - WARNING - 心跳包发送失败次数: 2
2025-06-02 11:26:12,411 - requirements_websocket - ERROR - 发送简单心跳包失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:26:12,411 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:26:12,412 - requirements_websocket - WARNING - 心跳包发送失败次数: 3
2025-06-02 11:26:12,412 - requirements_websocket - ERROR - 连续3次心跳包发送失败，停止心跳任务
2025-06-02 11:30:27,515 - requirements_websocket - INFO - 收到WebSocket连接请求: /api/v1/requirements/analyze, 客户端: 127.0.0.1:55723
2025-06-02 11:30:27,516 - requirements_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5174', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'gnFlecXFAQExta42eFKl3Q==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-06-02 11:30:27,516 - requirements_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:55723
2025-06-02 11:30:27,516 - requirements_websocket - INFO - WebSocket连接已建立，建议客户端设置较长的超时时间
2025-06-02 11:30:27,516 - requirements_websocket - INFO - 建议的WebSocket设置: ping_interval=30秒, ping_timeout=600秒(10分钟)
2025-06-02 11:30:27,517 - requirements_websocket - INFO - 发送测试消息成功
2025-06-02 11:30:27,518 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 11:30:27,519 - requirements_websocket - INFO - 收到前端消息: {"userId":1,"projectId":3,"content":"智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面","files":[],"task":"分析需求文档"...
2025-06-02 11:30:27,520 - requirements_websocket - INFO - 成功解析JSON数据
2025-06-02 11:30:27,520 - requirements_websocket - INFO - 处理前端请求: {'userId': 1, 'projectId': 3, 'content': '智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面', 'files': [], 'task':...
2025-06-02 11:30:27,521 - requirements_websocket - INFO - 用户输入的需求内容长度: 35 字符
2025-06-02 11:30:27,521 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面
2025-06-02 11:30:27,521 - requirements_websocket - INFO - 创建需求文件消息: 用户ID=1, 文件数量=0
2025-06-02 11:30:27,521 - requirements_websocket - INFO - 开始启动需求分析运行时
2025-06-02 11:30:27,521 - requirements_websocket - INFO - 用户输入的需求内容长度: 35 字符
2025-06-02 11:30:27,521 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面
2025-06-02 11:30:27,524 - requirements_websocket - INFO - 发送消息到前端: system, 是否最终消息: False
2025-06-02 11:30:27,524 - requirements_websocket - INFO - JSON字符串长度: 115
2025-06-02 11:30:27,524 - requirements_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-06-02 11:30:27,524 - requirements_websocket - INFO - JSON字符串长度: 142
2025-06-02 11:30:27,525 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 11:30:27,525 - requirements_websocket - INFO - JSON字符串长度: 159
2025-06-02 11:30:27,533 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 11:30:27,534 - requirements_websocket - INFO - JSON字符串长度: 199
2025-06-02 11:30:42,519 - requirements_websocket - ERROR - 发送简单心跳包失败: 
2025-06-02 11:30:42,521 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:30:42,521 - requirements_websocket - WARNING - 心跳包发送失败次数: 1
2025-06-02 11:30:47,694 - requirements_websocket - INFO - 收到WebSocket连接请求: /api/v1/requirements/analyze, 客户端: 127.0.0.1:55861
2025-06-02 11:30:47,694 - requirements_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5174', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'Ti/ZY2Cka7btpQ09VH2q1g==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-06-02 11:30:47,694 - requirements_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:55861
2025-06-02 11:30:47,694 - requirements_websocket - INFO - WebSocket连接已建立，建议客户端设置较长的超时时间
2025-06-02 11:30:47,694 - requirements_websocket - INFO - 建议的WebSocket设置: ping_interval=30秒, ping_timeout=600秒(10分钟)
2025-06-02 11:30:47,695 - requirements_websocket - INFO - 发送测试消息成功
2025-06-02 11:30:47,695 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 11:30:47,699 - requirements_websocket - INFO - 收到前端消息: {"userId":1,"projectId":3,"content":"智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面","files":[],"task":"分析需求文档"...
2025-06-02 11:30:47,699 - requirements_websocket - INFO - 成功解析JSON数据
2025-06-02 11:30:47,699 - requirements_websocket - INFO - 处理前端请求: {'userId': 1, 'projectId': 3, 'content': '智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面', 'files': [], 'task':...
2025-06-02 11:30:47,699 - requirements_websocket - INFO - 用户输入的需求内容长度: 35 字符
2025-06-02 11:30:47,699 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面
2025-06-02 11:30:47,700 - requirements_websocket - INFO - 创建需求文件消息: 用户ID=1, 文件数量=0
2025-06-02 11:30:47,700 - requirements_websocket - INFO - 开始启动需求分析运行时
2025-06-02 11:30:47,700 - requirements_websocket - INFO - 用户输入的需求内容长度: 35 字符
2025-06-02 11:30:47,700 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面
2025-06-02 11:30:47,701 - requirements_websocket - INFO - 发送消息到前端: system, 是否最终消息: False
2025-06-02 11:30:47,701 - requirements_websocket - INFO - JSON字符串长度: 115
2025-06-02 11:30:47,701 - requirements_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-06-02 11:30:47,701 - requirements_websocket - INFO - JSON字符串长度: 142
2025-06-02 11:30:47,702 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 11:30:47,702 - requirements_websocket - INFO - JSON字符串长度: 159
2025-06-02 11:30:47,706 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 11:30:47,706 - requirements_websocket - INFO - JSON字符串长度: 199
2025-06-02 11:30:57,522 - requirements_websocket - ERROR - 发送简单心跳包失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:30:57,523 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:30:57,523 - requirements_websocket - WARNING - 心跳包发送失败次数: 2
2025-06-02 11:31:12,524 - requirements_websocket - ERROR - 发送简单心跳包失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:31:12,526 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:31:12,526 - requirements_websocket - WARNING - 心跳包发送失败次数: 3
2025-06-02 11:31:12,526 - requirements_websocket - ERROR - 连续3次心跳包发送失败，停止心跳任务
2025-06-02 11:31:32,699 - requirements_websocket - ERROR - 发送简单心跳包失败: 
2025-06-02 11:31:32,699 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:31:32,699 - requirements_websocket - WARNING - 心跳包发送失败次数: 1
2025-06-02 11:31:47,700 - requirements_websocket - ERROR - 发送简单心跳包失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:31:47,702 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:31:47,702 - requirements_websocket - WARNING - 心跳包发送失败次数: 2
2025-06-02 11:33:44,174 - requirements_websocket - INFO - 收到WebSocket连接请求: /api/v1/requirements/analyze, 客户端: 127.0.0.1:56987
2025-06-02 11:33:44,174 - requirements_websocket - INFO - 请求头: {'host': 'localhost:8000', 'pragma': 'no-cache', 'accept': '*/*', 'sec-websocket-key': '5jrXIXicQr9BSZQPhsXanA==', 'sec-fetch-site': 'same-site', 'sec-websocket-version': '13', 'sec-websocket-extensions': 'permessage-deflate', 'cache-control': 'no-cache', 'sec-fetch-mode': 'websocket', 'accept-language': 'zh-CN,zh-Hans;q=0.9', 'origin': 'http://localhost:5174', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.6 Safari/605.1.15', 'connection': 'Upgrade', 'accept-encoding': 'gzip, deflate', 'upgrade': 'websocket', 'sec-fetch-dest': 'websocket'}
2025-06-02 11:33:44,175 - requirements_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:56987
2025-06-02 11:33:44,175 - requirements_websocket - INFO - WebSocket连接已建立，建议客户端设置较长的超时时间
2025-06-02 11:33:44,175 - requirements_websocket - INFO - 建议的WebSocket设置: ping_interval=30秒, ping_timeout=600秒(10分钟)
2025-06-02 11:33:44,177 - requirements_websocket - INFO - 发送测试消息成功
2025-06-02 11:33:44,177 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 11:33:44,177 - requirements_websocket - INFO - 收到前端消息: {"userId":1,"projectId":3,"content":"智能系统使用正确的用户名和密码登录","files":[],"task":"分析需求文档"}...
2025-06-02 11:33:44,178 - requirements_websocket - INFO - 成功解析JSON数据
2025-06-02 11:33:44,178 - requirements_websocket - INFO - 处理前端请求: {'userId': 1, 'projectId': 3, 'content': '智能系统使用正确的用户名和密码登录', 'files': [], 'task': '分析需求文档'}...
2025-06-02 11:33:44,179 - requirements_websocket - INFO - 用户输入的需求内容长度: 17 字符
2025-06-02 11:33:44,179 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统使用正确的用户名和密码登录
2025-06-02 11:33:44,179 - requirements_websocket - INFO - 创建需求文件消息: 用户ID=1, 文件数量=0
2025-06-02 11:33:44,179 - requirements_websocket - INFO - 开始启动需求分析运行时
2025-06-02 11:33:44,179 - requirements_websocket - INFO - 用户输入的需求内容长度: 17 字符
2025-06-02 11:33:44,179 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统使用正确的用户名和密码登录
2025-06-02 11:33:44,180 - requirements_websocket - INFO - 发送消息到前端: system, 是否最终消息: False
2025-06-02 11:33:44,180 - requirements_websocket - INFO - JSON字符串长度: 115
2025-06-02 11:33:44,181 - requirements_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-06-02 11:33:44,181 - requirements_websocket - INFO - JSON字符串长度: 142
2025-06-02 11:33:44,181 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 11:33:44,181 - requirements_websocket - INFO - JSON字符串长度: 159
2025-06-02 11:33:44,188 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 11:33:44,188 - requirements_websocket - INFO - JSON字符串长度: 420
2025-06-02 11:38:46,078 - requirements_websocket - INFO - 收到WebSocket连接请求: /api/v1/requirements/analyze, 客户端: 127.0.0.1:58800
2025-06-02 11:38:46,078 - requirements_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5174', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': '+uGUrJgQnAlrxhFoICrl0w==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-06-02 11:38:46,079 - requirements_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:58800
2025-06-02 11:38:46,079 - requirements_websocket - INFO - WebSocket连接已建立，建议客户端设置较长的超时时间
2025-06-02 11:38:46,079 - requirements_websocket - INFO - 建议的WebSocket设置: ping_interval=30秒, ping_timeout=600秒(10分钟)
2025-06-02 11:38:46,080 - requirements_websocket - INFO - 发送测试消息成功
2025-06-02 11:38:46,080 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 11:38:46,081 - requirements_websocket - INFO - 收到前端消息: {"userId":1,"projectId":3,"content":"智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面","files":[],"task":"分析需求文档"...
2025-06-02 11:38:46,083 - requirements_websocket - INFO - 成功解析JSON数据
2025-06-02 11:38:46,083 - requirements_websocket - INFO - 处理前端请求: {'userId': 1, 'projectId': 3, 'content': '智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面', 'files': [], 'task':...
2025-06-02 11:38:46,083 - requirements_websocket - INFO - 用户输入的需求内容长度: 35 字符
2025-06-02 11:38:46,083 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面
2025-06-02 11:38:46,084 - requirements_websocket - INFO - 创建需求文件消息: 用户ID=1, 文件数量=0
2025-06-02 11:38:46,084 - requirements_websocket - INFO - 开始启动需求分析运行时
2025-06-02 11:38:46,084 - requirements_websocket - INFO - 用户输入的需求内容长度: 35 字符
2025-06-02 11:38:46,084 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面
2025-06-02 11:38:46,085 - requirements_websocket - INFO - 发送消息到前端: system, 是否最终消息: False
2025-06-02 11:38:46,085 - requirements_websocket - INFO - JSON字符串长度: 115
2025-06-02 11:38:46,086 - requirements_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-06-02 11:38:46,086 - requirements_websocket - INFO - JSON字符串长度: 142
2025-06-02 11:38:46,087 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 11:38:46,087 - requirements_websocket - INFO - JSON字符串长度: 159
2025-06-02 11:39:15,755 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 11:39:15,755 - requirements_websocket - INFO - JSON字符串长度: 132
2025-06-02 11:39:15,755 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 11:39:15,755 - requirements_websocket - INFO - JSON字符串长度: 147
2025-06-02 11:39:15,756 - requirements_websocket - INFO - 发送消息到前端: 需求分析智能体, 是否最终消息: False
2025-06-02 11:39:15,756 - requirements_websocket - INFO - JSON字符串长度: 145
2025-06-02 11:39:55,830 - requirements_websocket - INFO - 发送消息到前端: Requirement Analysis Agent, 是否最终消息: False
2025-06-02 11:39:55,831 - requirements_websocket - INFO - JSON字符串长度: 158
2025-06-02 11:40:14,425 - requirements_websocket - INFO - 发送消息到前端: 需求结构化智能体, 是否最终消息: False
2025-06-02 11:40:14,425 - requirements_websocket - INFO - JSON字符串长度: 157
2025-06-02 11:40:48,741 - requirements_websocket - INFO - 发送消息到前端: 需求结构化智能体, 是否最终消息: False
2025-06-02 11:40:48,742 - requirements_websocket - INFO - JSON字符串长度: 192
2025-06-02 11:40:48,743 - requirements_websocket - INFO - 发送消息到前端: 需求数据库智能体, 是否最终消息: False
2025-06-02 11:40:48,743 - requirements_websocket - INFO - JSON字符串长度: 135
2025-06-02 11:40:48,744 - requirements_websocket - INFO - 发送消息到前端: database, 是否最终消息: False
2025-06-02 11:40:48,744 - requirements_websocket - INFO - JSON字符串长度: 1432
2025-06-02 11:40:48,744 - requirements_websocket - INFO - 发送消息到前端: 需求数据库智能体, 是否最终消息: True
2025-06-02 11:40:48,744 - requirements_websocket - INFO - JSON字符串长度: 280
2025-06-02 11:43:31,106 - requirements_websocket - ERROR - 发送简单心跳包失败: 
2025-06-02 11:43:31,107 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:43:31,107 - requirements_websocket - WARNING - 心跳包发送失败次数: 1
2025-06-02 11:43:46,109 - requirements_websocket - ERROR - 发送简单心跳包失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:43:46,110 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:43:46,110 - requirements_websocket - WARNING - 心跳包发送失败次数: 2
2025-06-02 11:44:01,111 - requirements_websocket - ERROR - 发送简单心跳包失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:44:01,112 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:44:01,112 - requirements_websocket - WARNING - 心跳包发送失败次数: 3
2025-06-02 11:44:01,112 - requirements_websocket - ERROR - 连续3次心跳包发送失败，停止心跳任务
