2025-06-02 11:23:57,397 - requirements_websocket - INFO - 收到WebSocket连接请求: /api/v1/requirements/analyze, 客户端: 127.0.0.1:53440
2025-06-02 11:23:57,397 - requirements_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5174', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'sjmayAgH1QaREFRenkx0vw==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-06-02 11:23:57,398 - requirements_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:53440
2025-06-02 11:23:57,398 - requirements_websocket - INFO - WebSocket连接已建立，建议客户端设置较长的超时时间
2025-06-02 11:23:57,398 - requirements_websocket - INFO - 建议的WebSocket设置: ping_interval=30秒, ping_timeout=600秒(10分钟)
2025-06-02 11:23:57,399 - requirements_websocket - INFO - 发送测试消息成功
2025-06-02 11:23:57,399 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 11:23:57,402 - requirements_websocket - INFO - 收到前端消息: {"userId":1,"projectId":3,"content":"智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面","files":[],"task":"分析需求文档"...
2025-06-02 11:23:57,402 - requirements_websocket - INFO - 成功解析JSON数据
2025-06-02 11:23:57,402 - requirements_websocket - INFO - 处理前端请求: {'userId': 1, 'projectId': 3, 'content': '智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面', 'files': [], 'task':...
2025-06-02 11:23:57,402 - requirements_websocket - INFO - 用户输入的需求内容长度: 35 字符
2025-06-02 11:23:57,402 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面
2025-06-02 11:23:57,403 - requirements_websocket - INFO - 创建需求文件消息: 用户ID=1, 文件数量=0
2025-06-02 11:23:57,403 - requirements_websocket - INFO - 开始启动需求分析运行时
2025-06-02 11:23:57,403 - requirements_websocket - INFO - 用户输入的需求内容长度: 35 字符
2025-06-02 11:23:57,403 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面
2025-06-02 11:23:57,404 - requirements_websocket - INFO - 发送消息到前端: system, 是否最终消息: False
2025-06-02 11:23:57,404 - requirements_websocket - INFO - JSON字符串长度: 115
2025-06-02 11:23:57,407 - requirements_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-06-02 11:23:57,407 - requirements_websocket - INFO - JSON字符串长度: 142
2025-06-02 11:23:57,408 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 11:23:57,408 - requirements_websocket - INFO - JSON字符串长度: 159
2025-06-02 11:23:57,416 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 11:23:57,416 - requirements_websocket - INFO - JSON字符串长度: 165
2025-06-02 11:25:42,406 - requirements_websocket - ERROR - 发送简单心跳包失败: 
2025-06-02 11:25:42,407 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:25:42,407 - requirements_websocket - WARNING - 心跳包发送失败次数: 1
2025-06-02 11:25:57,409 - requirements_websocket - ERROR - 发送简单心跳包失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:25:57,409 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:25:57,409 - requirements_websocket - WARNING - 心跳包发送失败次数: 2
2025-06-02 11:26:12,411 - requirements_websocket - ERROR - 发送简单心跳包失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:26:12,411 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:26:12,412 - requirements_websocket - WARNING - 心跳包发送失败次数: 3
2025-06-02 11:26:12,412 - requirements_websocket - ERROR - 连续3次心跳包发送失败，停止心跳任务
2025-06-02 11:30:27,515 - requirements_websocket - INFO - 收到WebSocket连接请求: /api/v1/requirements/analyze, 客户端: 127.0.0.1:55723
2025-06-02 11:30:27,516 - requirements_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5174', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'gnFlecXFAQExta42eFKl3Q==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-06-02 11:30:27,516 - requirements_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:55723
2025-06-02 11:30:27,516 - requirements_websocket - INFO - WebSocket连接已建立，建议客户端设置较长的超时时间
2025-06-02 11:30:27,516 - requirements_websocket - INFO - 建议的WebSocket设置: ping_interval=30秒, ping_timeout=600秒(10分钟)
2025-06-02 11:30:27,517 - requirements_websocket - INFO - 发送测试消息成功
2025-06-02 11:30:27,518 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 11:30:27,519 - requirements_websocket - INFO - 收到前端消息: {"userId":1,"projectId":3,"content":"智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面","files":[],"task":"分析需求文档"...
2025-06-02 11:30:27,520 - requirements_websocket - INFO - 成功解析JSON数据
2025-06-02 11:30:27,520 - requirements_websocket - INFO - 处理前端请求: {'userId': 1, 'projectId': 3, 'content': '智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面', 'files': [], 'task':...
2025-06-02 11:30:27,521 - requirements_websocket - INFO - 用户输入的需求内容长度: 35 字符
2025-06-02 11:30:27,521 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面
2025-06-02 11:30:27,521 - requirements_websocket - INFO - 创建需求文件消息: 用户ID=1, 文件数量=0
2025-06-02 11:30:27,521 - requirements_websocket - INFO - 开始启动需求分析运行时
2025-06-02 11:30:27,521 - requirements_websocket - INFO - 用户输入的需求内容长度: 35 字符
2025-06-02 11:30:27,521 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面
2025-06-02 11:30:27,524 - requirements_websocket - INFO - 发送消息到前端: system, 是否最终消息: False
2025-06-02 11:30:27,524 - requirements_websocket - INFO - JSON字符串长度: 115
2025-06-02 11:30:27,524 - requirements_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-06-02 11:30:27,524 - requirements_websocket - INFO - JSON字符串长度: 142
2025-06-02 11:30:27,525 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 11:30:27,525 - requirements_websocket - INFO - JSON字符串长度: 159
2025-06-02 11:30:27,533 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 11:30:27,534 - requirements_websocket - INFO - JSON字符串长度: 199
2025-06-02 11:30:42,519 - requirements_websocket - ERROR - 发送简单心跳包失败: 
2025-06-02 11:30:42,521 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:30:42,521 - requirements_websocket - WARNING - 心跳包发送失败次数: 1
2025-06-02 11:30:47,694 - requirements_websocket - INFO - 收到WebSocket连接请求: /api/v1/requirements/analyze, 客户端: 127.0.0.1:55861
2025-06-02 11:30:47,694 - requirements_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5174', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'Ti/ZY2Cka7btpQ09VH2q1g==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-06-02 11:30:47,694 - requirements_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:55861
2025-06-02 11:30:47,694 - requirements_websocket - INFO - WebSocket连接已建立，建议客户端设置较长的超时时间
2025-06-02 11:30:47,694 - requirements_websocket - INFO - 建议的WebSocket设置: ping_interval=30秒, ping_timeout=600秒(10分钟)
2025-06-02 11:30:47,695 - requirements_websocket - INFO - 发送测试消息成功
2025-06-02 11:30:47,695 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 11:30:47,699 - requirements_websocket - INFO - 收到前端消息: {"userId":1,"projectId":3,"content":"智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面","files":[],"task":"分析需求文档"...
2025-06-02 11:30:47,699 - requirements_websocket - INFO - 成功解析JSON数据
2025-06-02 11:30:47,699 - requirements_websocket - INFO - 处理前端请求: {'userId': 1, 'projectId': 3, 'content': '智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面', 'files': [], 'task':...
2025-06-02 11:30:47,699 - requirements_websocket - INFO - 用户输入的需求内容长度: 35 字符
2025-06-02 11:30:47,699 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面
2025-06-02 11:30:47,700 - requirements_websocket - INFO - 创建需求文件消息: 用户ID=1, 文件数量=0
2025-06-02 11:30:47,700 - requirements_websocket - INFO - 开始启动需求分析运行时
2025-06-02 11:30:47,700 - requirements_websocket - INFO - 用户输入的需求内容长度: 35 字符
2025-06-02 11:30:47,700 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面
2025-06-02 11:30:47,701 - requirements_websocket - INFO - 发送消息到前端: system, 是否最终消息: False
2025-06-02 11:30:47,701 - requirements_websocket - INFO - JSON字符串长度: 115
2025-06-02 11:30:47,701 - requirements_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-06-02 11:30:47,701 - requirements_websocket - INFO - JSON字符串长度: 142
2025-06-02 11:30:47,702 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 11:30:47,702 - requirements_websocket - INFO - JSON字符串长度: 159
2025-06-02 11:30:47,706 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 11:30:47,706 - requirements_websocket - INFO - JSON字符串长度: 199
2025-06-02 11:30:57,522 - requirements_websocket - ERROR - 发送简单心跳包失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:30:57,523 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:30:57,523 - requirements_websocket - WARNING - 心跳包发送失败次数: 2
2025-06-02 11:31:12,524 - requirements_websocket - ERROR - 发送简单心跳包失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:31:12,526 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:31:12,526 - requirements_websocket - WARNING - 心跳包发送失败次数: 3
2025-06-02 11:31:12,526 - requirements_websocket - ERROR - 连续3次心跳包发送失败，停止心跳任务
2025-06-02 11:31:32,699 - requirements_websocket - ERROR - 发送简单心跳包失败: 
2025-06-02 11:31:32,699 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:31:32,699 - requirements_websocket - WARNING - 心跳包发送失败次数: 1
2025-06-02 11:31:47,700 - requirements_websocket - ERROR - 发送简单心跳包失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:31:47,702 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:31:47,702 - requirements_websocket - WARNING - 心跳包发送失败次数: 2
2025-06-02 11:33:44,174 - requirements_websocket - INFO - 收到WebSocket连接请求: /api/v1/requirements/analyze, 客户端: 127.0.0.1:56987
2025-06-02 11:33:44,174 - requirements_websocket - INFO - 请求头: {'host': 'localhost:8000', 'pragma': 'no-cache', 'accept': '*/*', 'sec-websocket-key': '5jrXIXicQr9BSZQPhsXanA==', 'sec-fetch-site': 'same-site', 'sec-websocket-version': '13', 'sec-websocket-extensions': 'permessage-deflate', 'cache-control': 'no-cache', 'sec-fetch-mode': 'websocket', 'accept-language': 'zh-CN,zh-Hans;q=0.9', 'origin': 'http://localhost:5174', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.6 Safari/605.1.15', 'connection': 'Upgrade', 'accept-encoding': 'gzip, deflate', 'upgrade': 'websocket', 'sec-fetch-dest': 'websocket'}
2025-06-02 11:33:44,175 - requirements_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:56987
2025-06-02 11:33:44,175 - requirements_websocket - INFO - WebSocket连接已建立，建议客户端设置较长的超时时间
2025-06-02 11:33:44,175 - requirements_websocket - INFO - 建议的WebSocket设置: ping_interval=30秒, ping_timeout=600秒(10分钟)
2025-06-02 11:33:44,177 - requirements_websocket - INFO - 发送测试消息成功
2025-06-02 11:33:44,177 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 11:33:44,177 - requirements_websocket - INFO - 收到前端消息: {"userId":1,"projectId":3,"content":"智能系统使用正确的用户名和密码登录","files":[],"task":"分析需求文档"}...
2025-06-02 11:33:44,178 - requirements_websocket - INFO - 成功解析JSON数据
2025-06-02 11:33:44,178 - requirements_websocket - INFO - 处理前端请求: {'userId': 1, 'projectId': 3, 'content': '智能系统使用正确的用户名和密码登录', 'files': [], 'task': '分析需求文档'}...
2025-06-02 11:33:44,179 - requirements_websocket - INFO - 用户输入的需求内容长度: 17 字符
2025-06-02 11:33:44,179 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统使用正确的用户名和密码登录
2025-06-02 11:33:44,179 - requirements_websocket - INFO - 创建需求文件消息: 用户ID=1, 文件数量=0
2025-06-02 11:33:44,179 - requirements_websocket - INFO - 开始启动需求分析运行时
2025-06-02 11:33:44,179 - requirements_websocket - INFO - 用户输入的需求内容长度: 17 字符
2025-06-02 11:33:44,179 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统使用正确的用户名和密码登录
2025-06-02 11:33:44,180 - requirements_websocket - INFO - 发送消息到前端: system, 是否最终消息: False
2025-06-02 11:33:44,180 - requirements_websocket - INFO - JSON字符串长度: 115
2025-06-02 11:33:44,181 - requirements_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-06-02 11:33:44,181 - requirements_websocket - INFO - JSON字符串长度: 142
2025-06-02 11:33:44,181 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 11:33:44,181 - requirements_websocket - INFO - JSON字符串长度: 159
2025-06-02 11:33:44,188 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 11:33:44,188 - requirements_websocket - INFO - JSON字符串长度: 420
2025-06-02 11:38:46,078 - requirements_websocket - INFO - 收到WebSocket连接请求: /api/v1/requirements/analyze, 客户端: 127.0.0.1:58800
2025-06-02 11:38:46,078 - requirements_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5174', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': '+uGUrJgQnAlrxhFoICrl0w==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-06-02 11:38:46,079 - requirements_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:58800
2025-06-02 11:38:46,079 - requirements_websocket - INFO - WebSocket连接已建立，建议客户端设置较长的超时时间
2025-06-02 11:38:46,079 - requirements_websocket - INFO - 建议的WebSocket设置: ping_interval=30秒, ping_timeout=600秒(10分钟)
2025-06-02 11:38:46,080 - requirements_websocket - INFO - 发送测试消息成功
2025-06-02 11:38:46,080 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 11:38:46,081 - requirements_websocket - INFO - 收到前端消息: {"userId":1,"projectId":3,"content":"智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面","files":[],"task":"分析需求文档"...
2025-06-02 11:38:46,083 - requirements_websocket - INFO - 成功解析JSON数据
2025-06-02 11:38:46,083 - requirements_websocket - INFO - 处理前端请求: {'userId': 1, 'projectId': 3, 'content': '智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面', 'files': [], 'task':...
2025-06-02 11:38:46,083 - requirements_websocket - INFO - 用户输入的需求内容长度: 35 字符
2025-06-02 11:38:46,083 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面
2025-06-02 11:38:46,084 - requirements_websocket - INFO - 创建需求文件消息: 用户ID=1, 文件数量=0
2025-06-02 11:38:46,084 - requirements_websocket - INFO - 开始启动需求分析运行时
2025-06-02 11:38:46,084 - requirements_websocket - INFO - 用户输入的需求内容长度: 35 字符
2025-06-02 11:38:46,084 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面
2025-06-02 11:38:46,085 - requirements_websocket - INFO - 发送消息到前端: system, 是否最终消息: False
2025-06-02 11:38:46,085 - requirements_websocket - INFO - JSON字符串长度: 115
2025-06-02 11:38:46,086 - requirements_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-06-02 11:38:46,086 - requirements_websocket - INFO - JSON字符串长度: 142
2025-06-02 11:38:46,087 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 11:38:46,087 - requirements_websocket - INFO - JSON字符串长度: 159
2025-06-02 11:39:15,755 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 11:39:15,755 - requirements_websocket - INFO - JSON字符串长度: 132
2025-06-02 11:39:15,755 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 11:39:15,755 - requirements_websocket - INFO - JSON字符串长度: 147
2025-06-02 11:39:15,756 - requirements_websocket - INFO - 发送消息到前端: 需求分析智能体, 是否最终消息: False
2025-06-02 11:39:15,756 - requirements_websocket - INFO - JSON字符串长度: 145
2025-06-02 11:39:55,830 - requirements_websocket - INFO - 发送消息到前端: Requirement Analysis Agent, 是否最终消息: False
2025-06-02 11:39:55,831 - requirements_websocket - INFO - JSON字符串长度: 158
2025-06-02 11:40:14,425 - requirements_websocket - INFO - 发送消息到前端: 需求结构化智能体, 是否最终消息: False
2025-06-02 11:40:14,425 - requirements_websocket - INFO - JSON字符串长度: 157
2025-06-02 11:40:48,741 - requirements_websocket - INFO - 发送消息到前端: 需求结构化智能体, 是否最终消息: False
2025-06-02 11:40:48,742 - requirements_websocket - INFO - JSON字符串长度: 192
2025-06-02 11:40:48,743 - requirements_websocket - INFO - 发送消息到前端: 需求数据库智能体, 是否最终消息: False
2025-06-02 11:40:48,743 - requirements_websocket - INFO - JSON字符串长度: 135
2025-06-02 11:40:48,744 - requirements_websocket - INFO - 发送消息到前端: database, 是否最终消息: False
2025-06-02 11:40:48,744 - requirements_websocket - INFO - JSON字符串长度: 1432
2025-06-02 11:40:48,744 - requirements_websocket - INFO - 发送消息到前端: 需求数据库智能体, 是否最终消息: True
2025-06-02 11:40:48,744 - requirements_websocket - INFO - JSON字符串长度: 280
2025-06-02 11:43:31,106 - requirements_websocket - ERROR - 发送简单心跳包失败: 
2025-06-02 11:43:31,107 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:43:31,107 - requirements_websocket - WARNING - 心跳包发送失败次数: 1
2025-06-02 11:43:46,109 - requirements_websocket - ERROR - 发送简单心跳包失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:43:46,110 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:43:46,110 - requirements_websocket - WARNING - 心跳包发送失败次数: 2
2025-06-02 11:44:01,111 - requirements_websocket - ERROR - 发送简单心跳包失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:44:01,112 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 11:44:01,112 - requirements_websocket - WARNING - 心跳包发送失败次数: 3
2025-06-02 11:44:01,112 - requirements_websocket - ERROR - 连续3次心跳包发送失败，停止心跳任务
2025-06-02 11:50:15,482 - requirements_websocket - INFO - 收到WebSocket连接请求: /api/v1/requirements/analyze, 客户端: 127.0.0.1:62923
2025-06-02 11:50:15,483 - requirements_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5174', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'K5gc8l95qCHlKgcAJeGG8A==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-06-02 11:50:15,484 - requirements_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:62923
2025-06-02 11:50:15,484 - requirements_websocket - INFO - WebSocket连接已建立，建议客户端设置较长的超时时间
2025-06-02 11:50:15,484 - requirements_websocket - INFO - 建议的WebSocket设置: ping_interval=30秒, ping_timeout=600秒(10分钟)
2025-06-02 11:50:15,486 - requirements_websocket - INFO - 发送测试消息成功
2025-06-02 11:50:15,486 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 11:50:15,487 - requirements_websocket - INFO - 收到前端消息: {"userId":1,"projectId":3,"content":"智能系统使用指定的用户名和密码登录","files":[],"task":"分析需求文档"}...
2025-06-02 11:50:15,487 - requirements_websocket - INFO - 成功解析JSON数据
2025-06-02 11:50:15,487 - requirements_websocket - INFO - 处理前端请求: {'userId': 1, 'projectId': 3, 'content': '智能系统使用指定的用户名和密码登录', 'files': [], 'task': '分析需求文档'}...
2025-06-02 11:50:15,487 - requirements_websocket - INFO - 用户输入的需求内容长度: 17 字符
2025-06-02 11:50:15,487 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统使用指定的用户名和密码登录
2025-06-02 11:50:15,487 - requirements_websocket - INFO - 创建需求文件消息: 用户ID=1, 文件数量=0
2025-06-02 11:50:15,487 - requirements_websocket - INFO - 开始启动需求分析运行时
2025-06-02 11:50:15,487 - requirements_websocket - INFO - 用户输入的需求内容长度: 17 字符
2025-06-02 11:50:15,488 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统使用指定的用户名和密码登录
2025-06-02 11:50:15,489 - requirements_websocket - INFO - 发送消息到前端: system, 是否最终消息: False
2025-06-02 11:50:15,489 - requirements_websocket - INFO - JSON字符串长度: 115
2025-06-02 11:50:15,490 - requirements_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-06-02 11:50:15,490 - requirements_websocket - INFO - JSON字符串长度: 142
2025-06-02 11:50:15,490 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 11:50:15,490 - requirements_websocket - INFO - JSON字符串长度: 159
2025-06-02 11:50:47,574 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 11:50:47,575 - requirements_websocket - INFO - JSON字符串长度: 132
2025-06-02 11:50:47,575 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 11:50:47,575 - requirements_websocket - INFO - JSON字符串长度: 147
2025-06-02 11:50:47,576 - requirements_websocket - INFO - 发送消息到前端: 需求分析智能体, 是否最终消息: False
2025-06-02 11:50:47,576 - requirements_websocket - INFO - JSON字符串长度: 145
2025-06-02 11:51:21,669 - requirements_websocket - INFO - 发送消息到前端: Requirement Analysis Agent, 是否最终消息: False
2025-06-02 11:51:21,669 - requirements_websocket - INFO - JSON字符串长度: 158
2025-06-02 11:51:41,025 - requirements_websocket - INFO - 发送消息到前端: 需求结构化智能体, 是否最终消息: False
2025-06-02 11:51:41,025 - requirements_websocket - INFO - JSON字符串长度: 157
2025-06-02 11:52:16,689 - requirements_websocket - INFO - 发送消息到前端: 需求结构化智能体, 是否最终消息: False
2025-06-02 11:52:16,689 - requirements_websocket - INFO - JSON字符串长度: 192
2025-06-02 11:52:16,690 - requirements_websocket - INFO - 发送消息到前端: 需求数据库智能体, 是否最终消息: False
2025-06-02 11:52:16,690 - requirements_websocket - INFO - JSON字符串长度: 135
2025-06-02 11:52:16,692 - requirements_websocket - INFO - 发送消息到前端: database, 是否最终消息: False
2025-06-02 11:52:16,692 - requirements_websocket - INFO - JSON字符串长度: 1682
2025-06-02 11:52:16,693 - requirements_websocket - INFO - 发送消息到前端: 需求数据库智能体, 是否最终消息: True
2025-06-02 11:52:16,693 - requirements_websocket - INFO - JSON字符串长度: 280
2025-06-02 12:06:33,104 - requirements_websocket - ERROR - 发送简单心跳包失败: 
2025-06-02 12:06:33,106 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 12:06:33,106 - requirements_websocket - WARNING - 心跳包发送失败次数: 1
2025-06-02 12:06:48,107 - requirements_websocket - ERROR - 发送简单心跳包失败: Cannot call "send" once a close message has been sent.
2025-06-02 12:06:48,108 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 12:06:48,108 - requirements_websocket - WARNING - 心跳包发送失败次数: 2
2025-06-02 12:09:10,714 - requirements_websocket - ERROR - 发送简单心跳包失败: Cannot call "send" once a close message has been sent.
2025-06-02 12:09:10,715 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 12:09:10,715 - requirements_websocket - WARNING - 心跳包发送失败次数: 3
2025-06-02 12:09:10,715 - requirements_websocket - ERROR - 连续3次心跳包发送失败，停止心跳任务
2025-06-02 15:11:03,472 - requirements_websocket - INFO - 发送消息到前端: system, 是否最终消息: True
2025-06-02 15:11:03,473 - requirements_websocket - INFO - JSON字符串长度: 149
2025-06-02 15:11:03,474 - requirements_websocket - WARNING - WebSocket连接已关闭，无法发送消息
2025-06-02 15:11:03,474 - requirements_websocket - INFO - 需求分析运行时完成
2025-06-02 15:11:03,474 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 15:11:03,475 - requirements_websocket - ERROR - 处理WebSocket消息时发生错误: WebSocket is not connected. Need to call "accept" first.
Traceback (most recent call last):
  File "/Users/<USER>/Documents/augment-projects/aiTesting/backend/app/api/endpoints/requirements.py", line 853, in analyze_requirements
    raw_data = await asyncio.wait_for(websocket.receive_text(), timeout=1800)  # 30分钟超时
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/tasks.py", line 489, in wait_for
    return fut.result()
           ^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/aiTesting/.venv/lib/python3.11/site-packages/starlette/websockets.py", line 117, in receive_text
    raise RuntimeError('WebSocket is not connected. Need to call "accept" first.')
RuntimeError: WebSocket is not connected. Need to call "accept" first.
2025-06-02 15:11:03,481 - requirements_websocket - ERROR - 无法发送错误消息
2025-06-02 15:53:59,031 - requirements_websocket - INFO - 收到WebSocket连接请求: /api/v1/requirements/analyze, 客户端: 127.0.0.1:51798
2025-06-02 15:53:59,032 - requirements_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5174', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'k8B76CaR1LQg3+5iC5WZng==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-06-02 15:53:59,032 - requirements_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:51798
2025-06-02 15:53:59,032 - requirements_websocket - INFO - WebSocket连接已建立，建议客户端设置较长的超时时间
2025-06-02 15:53:59,032 - requirements_websocket - INFO - 建议的WebSocket设置: ping_interval=30秒, ping_timeout=600秒(10分钟)
2025-06-02 15:53:59,033 - requirements_websocket - INFO - 发送测试消息成功
2025-06-02 15:53:59,033 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 15:53:59,037 - requirements_websocket - INFO - 收到前端消息: {"userId":1,"projectId":3,"content":"智能系统登录，使用正确的用户名和密码成功登录","files":[],"task":"分析需求文档"}...
2025-06-02 15:53:59,037 - requirements_websocket - INFO - 成功解析JSON数据
2025-06-02 15:53:59,038 - requirements_websocket - INFO - 处理前端请求: {'userId': 1, 'projectId': 3, 'content': '智能系统登录，使用正确的用户名和密码成功登录', 'files': [], 'task': '分析需求文档'}...
2025-06-02 15:53:59,038 - requirements_websocket - INFO - 用户输入的需求内容长度: 22 字符
2025-06-02 15:53:59,038 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统登录，使用正确的用户名和密码成功登录
2025-06-02 15:53:59,038 - requirements_websocket - INFO - 创建需求文件消息: 用户ID=1, 文件数量=0
2025-06-02 15:53:59,039 - requirements_websocket - INFO - 开始启动需求分析运行时
2025-06-02 15:53:59,039 - requirements_websocket - INFO - 用户输入的需求内容长度: 22 字符
2025-06-02 15:53:59,039 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统登录，使用正确的用户名和密码成功登录
2025-06-02 15:53:59,040 - requirements_websocket - INFO - 发送消息到前端: system, 是否最终消息: False
2025-06-02 15:53:59,040 - requirements_websocket - INFO - JSON字符串长度: 115
2025-06-02 15:53:59,041 - requirements_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-06-02 15:53:59,041 - requirements_websocket - INFO - JSON字符串长度: 142
2025-06-02 15:53:59,041 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 15:53:59,041 - requirements_websocket - INFO - JSON字符串长度: 159
2025-06-02 15:54:26,677 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 15:54:26,678 - requirements_websocket - INFO - JSON字符串长度: 132
2025-06-02 15:54:26,678 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 15:54:26,678 - requirements_websocket - INFO - JSON字符串长度: 147
2025-06-02 15:54:26,683 - requirements_websocket - INFO - 发送消息到前端: 需求分析智能体, 是否最终消息: False
2025-06-02 15:54:26,684 - requirements_websocket - INFO - JSON字符串长度: 145
2025-06-02 15:54:58,348 - requirements_websocket - INFO - 发送消息到前端: Requirement Analysis Agent, 是否最终消息: False
2025-06-02 15:54:58,349 - requirements_websocket - INFO - JSON字符串长度: 158
2025-06-02 15:55:14,546 - requirements_websocket - INFO - 发送消息到前端: 需求结构化智能体, 是否最终消息: False
2025-06-02 15:55:14,547 - requirements_websocket - INFO - JSON字符串长度: 157
2025-06-02 15:55:38,426 - requirements_websocket - INFO - 发送消息到前端: 需求结构化智能体, 是否最终消息: False
2025-06-02 15:55:38,427 - requirements_websocket - INFO - JSON字符串长度: 192
2025-06-02 15:55:38,429 - requirements_websocket - INFO - 发送消息到前端: 需求数据库智能体, 是否最终消息: False
2025-06-02 15:55:38,430 - requirements_websocket - INFO - JSON字符串长度: 135
2025-06-02 15:55:38,438 - requirements_websocket - INFO - 发送消息到前端: database, 是否最终消息: False
2025-06-02 15:55:38,438 - requirements_websocket - INFO - JSON字符串长度: 1084
2025-06-02 15:55:38,439 - requirements_websocket - INFO - 发送消息到前端: 需求数据库智能体, 是否最终消息: True
2025-06-02 15:55:38,439 - requirements_websocket - INFO - JSON字符串长度: 280
2025-06-02 16:00:44,066 - requirements_websocket - ERROR - 发送简单心跳包失败: 
2025-06-02 16:00:44,068 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 16:00:44,068 - requirements_websocket - WARNING - 心跳包发送失败次数: 1
2025-06-02 16:00:59,069 - requirements_websocket - ERROR - 发送简单心跳包失败: Cannot call "send" once a close message has been sent.
2025-06-02 16:00:59,070 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 16:00:59,070 - requirements_websocket - WARNING - 心跳包发送失败次数: 2
2025-06-02 16:01:14,071 - requirements_websocket - ERROR - 发送简单心跳包失败: Cannot call "send" once a close message has been sent.
2025-06-02 16:01:14,071 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 16:01:14,071 - requirements_websocket - WARNING - 心跳包发送失败次数: 3
2025-06-02 16:01:14,071 - requirements_websocket - ERROR - 连续3次心跳包发送失败，停止心跳任务
2025-06-02 16:04:27,218 - requirements_websocket - INFO - 发送消息到前端: system, 是否最终消息: True
2025-06-02 16:04:27,220 - requirements_websocket - INFO - JSON字符串长度: 149
2025-06-02 16:04:27,220 - requirements_websocket - WARNING - WebSocket连接已关闭，无法发送消息
2025-06-02 16:04:27,221 - requirements_websocket - INFO - 需求分析运行时完成
2025-06-02 16:04:27,221 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:04:27,221 - requirements_websocket - ERROR - 处理WebSocket消息时发生错误: WebSocket is not connected. Need to call "accept" first.
Traceback (most recent call last):
  File "/Users/<USER>/Documents/augment-projects/aiTesting/backend/app/api/endpoints/requirements.py", line 853, in analyze_requirements
    raw_data = await asyncio.wait_for(websocket.receive_text(), timeout=1800)  # 30分钟超时
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/tasks.py", line 489, in wait_for
    return fut.result()
           ^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/aiTesting/.venv/lib/python3.11/site-packages/starlette/websockets.py", line 117, in receive_text
    raise RuntimeError('WebSocket is not connected. Need to call "accept" first.')
RuntimeError: WebSocket is not connected. Need to call "accept" first.
2025-06-02 16:04:27,223 - requirements_websocket - ERROR - 无法发送错误消息
2025-06-02 16:17:26,472 - requirements_websocket - INFO - 收到WebSocket连接请求: /api/v1/requirements/analyze, 客户端: 127.0.0.1:59959
2025-06-02 16:17:26,473 - requirements_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5174', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'UpMWxXiwYQSr8MMRFMW+Wg==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-06-02 16:17:26,473 - requirements_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:59959
2025-06-02 16:17:26,473 - requirements_websocket - INFO - WebSocket连接已建立，建议客户端设置较长的超时时间
2025-06-02 16:17:26,473 - requirements_websocket - INFO - 建议的WebSocket设置: ping_interval=30秒, ping_timeout=600秒(10分钟)
2025-06-02 16:17:26,474 - requirements_websocket - INFO - 发送测试消息成功
2025-06-02 16:17:26,475 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:17:26,476 - requirements_websocket - INFO - 收到前端消息: {"userId":1,"projectId":3,"content":"智能系统登录功能","files":[],"task":"分析需求文档"}...
2025-06-02 16:17:26,478 - requirements_websocket - INFO - 成功解析JSON数据
2025-06-02 16:17:26,478 - requirements_websocket - INFO - 处理前端请求: {'userId': 1, 'projectId': 3, 'content': '智能系统登录功能', 'files': [], 'task': '分析需求文档'}...
2025-06-02 16:17:26,478 - requirements_websocket - INFO - 用户输入的需求内容长度: 8 字符
2025-06-02 16:17:26,479 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统登录功能
2025-06-02 16:17:26,479 - requirements_websocket - INFO - 创建需求文件消息: 用户ID=1, 文件数量=0
2025-06-02 16:17:26,479 - requirements_websocket - INFO - 开始启动需求分析运行时
2025-06-02 16:17:26,479 - requirements_websocket - INFO - 用户输入的需求内容长度: 8 字符
2025-06-02 16:17:26,479 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统登录功能
2025-06-02 16:17:26,480 - requirements_websocket - INFO - 发送消息到前端: system, 是否最终消息: False
2025-06-02 16:17:26,480 - requirements_websocket - INFO - JSON字符串长度: 115
2025-06-02 16:17:26,481 - requirements_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-06-02 16:17:26,481 - requirements_websocket - INFO - JSON字符串长度: 142
2025-06-02 16:17:26,481 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 16:17:26,482 - requirements_websocket - INFO - JSON字符串长度: 159
2025-06-02 16:18:08,501 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 16:18:08,501 - requirements_websocket - INFO - JSON字符串长度: 132
2025-06-02 16:18:08,502 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 16:18:08,502 - requirements_websocket - INFO - JSON字符串长度: 147
2025-06-02 16:18:08,503 - requirements_websocket - INFO - 发送消息到前端: 需求分析智能体, 是否最终消息: False
2025-06-02 16:18:08,504 - requirements_websocket - INFO - JSON字符串长度: 145
2025-06-02 16:18:56,090 - requirements_websocket - INFO - 发送消息到前端: Requirement Analysis Agent, 是否最终消息: False
2025-06-02 16:18:56,090 - requirements_websocket - INFO - JSON字符串长度: 158
2025-06-02 16:19:21,922 - requirements_websocket - INFO - 发送消息到前端: 需求结构化智能体, 是否最终消息: False
2025-06-02 16:19:21,923 - requirements_websocket - INFO - JSON字符串长度: 157
2025-06-02 16:20:00,791 - requirements_websocket - INFO - 发送消息到前端: 需求结构化智能体, 是否最终消息: False
2025-06-02 16:20:00,792 - requirements_websocket - INFO - JSON字符串长度: 192
2025-06-02 16:20:00,793 - requirements_websocket - INFO - 发送消息到前端: 需求数据库智能体, 是否最终消息: False
2025-06-02 16:20:00,793 - requirements_websocket - INFO - JSON字符串长度: 135
2025-06-02 16:20:00,794 - requirements_websocket - INFO - 发送消息到前端: database, 是否最终消息: False
2025-06-02 16:20:00,795 - requirements_websocket - INFO - JSON字符串长度: 1640
2025-06-02 16:20:00,795 - requirements_websocket - INFO - 发送消息到前端: 需求数据库智能体, 是否最终消息: True
2025-06-02 16:20:00,796 - requirements_websocket - INFO - JSON字符串长度: 280
2025-06-02 16:28:08,542 - requirements_websocket - INFO - 发送消息到前端: system, 是否最终消息: True
2025-06-02 16:28:08,544 - requirements_websocket - INFO - JSON字符串长度: 149
2025-06-02 16:28:08,545 - requirements_websocket - INFO - 需求分析运行时完成
2025-06-02 16:28:08,545 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:28:08,545 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:28:08,547 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:28:08,548 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:28:08,558 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:28:08,558 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:28:08,558 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:28:08,559 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:28:08,559 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:28:08,560 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:28:08,560 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:28:08,560 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:28:08,561 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:28:08,561 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:28:08,561 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:28:08,561 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:28:08,561 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:28:08,561 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:28:08,561 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:28:08,561 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:28:08,561 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:28:26,470 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:28:26,471 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:29:26,470 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:29:26,473 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:30:26,469 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:30:26,471 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:31:26,469 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:31:26,471 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:32:26,468 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:32:26,469 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:33:26,467 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:33:26,468 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:34:26,466 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:34:26,468 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:35:26,465 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:35:26,465 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:36:26,467 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:36:26,468 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:37:26,465 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:37:26,467 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:38:26,463 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:38:26,464 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:39:26,464 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:39:26,466 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:40:26,461 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:40:26,462 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:41:26,461 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:41:26,461 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:42:26,461 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:42:26,463 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:43:26,460 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:43:26,461 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:44:26,460 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:44:26,462 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:45:26,459 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:45:26,461 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:46:26,458 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:46:26,461 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:47:26,458 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:47:26,459 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:48:26,458 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:48:26,459 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:49:26,457 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:49:26,459 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:50:26,456 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:50:26,458 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:51:26,454 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:51:26,456 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:52:26,453 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:52:26,455 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:53:26,454 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:53:26,456 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:54:26,451 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:54:26,454 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:55:26,452 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:55:26,454 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:56:26,453 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:56:26,454 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:57:26,450 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:57:26,452 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:58:26,449 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:58:26,450 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 16:59:26,449 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 16:59:26,450 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 17:00:26,448 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 17:00:26,450 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 17:01:26,446 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 17:01:26,447 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 17:02:26,448 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 17:02:26,449 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 17:03:26,446 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 17:03:26,447 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 17:04:26,467 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 17:04:26,469 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 17:05:26,492 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 17:05:26,494 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 17:06:26,492 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 17:06:26,493 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 17:07:26,491 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 17:07:26,494 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 17:08:26,492 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 17:08:26,493 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 17:09:26,491 - requirements_websocket - INFO - 收到前端消息: ping...
2025-06-02 17:09:26,493 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 17:09:48,173 - requirements_websocket - INFO - 收到WebSocket连接请求: /api/v1/requirements/analyze, 客户端: 127.0.0.1:61479
2025-06-02 17:09:48,173 - requirements_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5174', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': '4n/Ofh+h9DHH13hW/lE1qw==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-06-02 17:09:48,174 - requirements_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:61479
2025-06-02 17:09:48,174 - requirements_websocket - INFO - WebSocket连接已建立，建议客户端设置较长的超时时间
2025-06-02 17:09:48,174 - requirements_websocket - INFO - 建议的WebSocket设置: ping_interval=30秒, ping_timeout=600秒(10分钟)
2025-06-02 17:09:48,175 - requirements_websocket - INFO - 发送测试消息成功
2025-06-02 17:09:48,176 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 17:09:48,177 - requirements_websocket - INFO - 收到前端消息: {"userId":1,"projectId":3,"content":"智能系统员工新增、编辑、删除功能","files":[],"task":"分析需求文档"}...
2025-06-02 17:09:48,178 - requirements_websocket - INFO - 成功解析JSON数据
2025-06-02 17:09:48,178 - requirements_websocket - INFO - 处理前端请求: {'userId': 1, 'projectId': 3, 'content': '智能系统员工新增、编辑、删除功能', 'files': [], 'task': '分析需求文档'}...
2025-06-02 17:09:48,178 - requirements_websocket - INFO - 用户输入的需求内容长度: 16 字符
2025-06-02 17:09:48,178 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统员工新增、编辑、删除功能
2025-06-02 17:09:48,178 - requirements_websocket - INFO - 创建需求文件消息: 用户ID=1, 文件数量=0
2025-06-02 17:09:48,179 - requirements_websocket - INFO - 开始启动需求分析运行时
2025-06-02 17:09:48,179 - requirements_websocket - INFO - 用户输入的需求内容长度: 16 字符
2025-06-02 17:09:48,179 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统员工新增、编辑、删除功能
2025-06-02 17:09:48,180 - requirements_websocket - INFO - 发送消息到前端: system, 是否最终消息: False
2025-06-02 17:09:48,180 - requirements_websocket - INFO - JSON字符串长度: 115
2025-06-02 17:09:48,181 - requirements_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-06-02 17:09:48,181 - requirements_websocket - INFO - JSON字符串长度: 142
2025-06-02 17:09:48,181 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 17:09:48,182 - requirements_websocket - INFO - JSON字符串长度: 159
2025-06-02 17:10:16,097 - requirements_websocket - INFO - 客户端断开连接
2025-06-02 17:10:18,179 - requirements_websocket - ERROR - 发送简单心跳包失败: 
2025-06-02 17:10:18,179 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 17:10:18,180 - requirements_websocket - WARNING - 心跳包发送失败次数: 1
2025-06-02 17:10:21,295 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 17:10:21,295 - requirements_websocket - INFO - JSON字符串长度: 132
2025-06-02 17:10:21,295 - requirements_websocket - WARNING - WebSocket连接已关闭，无法发送消息
2025-06-02 17:10:21,296 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 17:10:21,296 - requirements_websocket - INFO - JSON字符串长度: 147
2025-06-02 17:10:21,296 - requirements_websocket - WARNING - WebSocket连接已关闭，无法发送消息
2025-06-02 17:10:21,296 - requirements_websocket - INFO - 发送消息到前端: 需求分析智能体, 是否最终消息: False
2025-06-02 17:10:21,296 - requirements_websocket - INFO - JSON字符串长度: 145
2025-06-02 17:10:21,296 - requirements_websocket - WARNING - WebSocket连接已关闭，无法发送消息
2025-06-02 17:34:00,206 - requirements_websocket - ERROR - 发送简单心跳包失败: Cannot call "send" once a close message has been sent.
2025-06-02 17:34:00,206 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 17:34:00,207 - requirements_websocket - WARNING - 心跳包发送失败次数: 2
2025-06-02 17:40:53,643 - requirements_websocket - ERROR - 发送简单心跳包失败: Cannot call "send" once a close message has been sent.
2025-06-02 17:40:53,645 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 17:40:53,645 - requirements_websocket - WARNING - 心跳包发送失败次数: 3
2025-06-02 17:40:53,645 - requirements_websocket - ERROR - 连续3次心跳包发送失败，停止心跳任务
2025-06-02 18:34:04,653 - requirements_websocket - INFO - 发送消息到前端: Requirement Analysis Agent, 是否最终消息: False
2025-06-02 18:34:04,654 - requirements_websocket - INFO - JSON字符串长度: 158
2025-06-02 18:34:04,654 - requirements_websocket - WARNING - WebSocket连接已关闭，无法发送消息
2025-06-02 18:34:28,534 - requirements_websocket - INFO - 发送消息到前端: 需求结构化智能体, 是否最终消息: False
2025-06-02 18:34:28,535 - requirements_websocket - INFO - JSON字符串长度: 157
2025-06-02 18:34:28,536 - requirements_websocket - WARNING - WebSocket连接已关闭，无法发送消息
2025-06-02 18:35:11,264 - requirements_websocket - INFO - 发送消息到前端: 需求结构化智能体, 是否最终消息: False
2025-06-02 18:35:11,266 - requirements_websocket - INFO - JSON字符串长度: 192
2025-06-02 18:35:11,266 - requirements_websocket - WARNING - WebSocket连接已关闭，无法发送消息
2025-06-02 18:35:11,266 - requirements_websocket - INFO - 发送消息到前端: 需求数据库智能体, 是否最终消息: False
2025-06-02 18:35:11,266 - requirements_websocket - INFO - JSON字符串长度: 135
2025-06-02 18:35:11,267 - requirements_websocket - WARNING - WebSocket连接已关闭，无法发送消息
2025-06-02 18:35:11,269 - requirements_websocket - INFO - 发送消息到前端: database, 是否最终消息: False
2025-06-02 18:35:11,269 - requirements_websocket - INFO - JSON字符串长度: 2854
2025-06-02 18:35:11,269 - requirements_websocket - WARNING - WebSocket连接已关闭，无法发送消息
2025-06-02 18:35:11,269 - requirements_websocket - INFO - 发送消息到前端: 需求数据库智能体, 是否最终消息: True
2025-06-02 18:35:11,270 - requirements_websocket - INFO - JSON字符串长度: 280
2025-06-02 18:35:11,270 - requirements_websocket - WARNING - WebSocket连接已关闭，无法发送消息
2025-06-02 18:38:48,729 - requirements_websocket - INFO - 发送消息到前端: system, 是否最终消息: True
2025-06-02 18:38:48,730 - requirements_websocket - INFO - JSON字符串长度: 149
2025-06-02 18:38:48,731 - requirements_websocket - WARNING - WebSocket连接已关闭，无法发送消息
2025-06-02 18:38:48,731 - requirements_websocket - INFO - 需求分析运行时完成
2025-06-02 18:38:48,731 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 18:38:48,732 - requirements_websocket - ERROR - 处理WebSocket消息时发生错误: WebSocket is not connected. Need to call "accept" first.
Traceback (most recent call last):
  File "/Users/<USER>/Documents/augment-projects/aiTesting/backend/app/api/endpoints/requirements.py", line 853, in analyze_requirements
    raw_data = await asyncio.wait_for(websocket.receive_text(), timeout=1800)  # 30分钟超时
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/tasks.py", line 489, in wait_for
    return fut.result()
           ^^^^^^^^^^^^
  File "/Users/<USER>/Documents/augment-projects/aiTesting/.venv/lib/python3.11/site-packages/starlette/websockets.py", line 117, in receive_text
    raise RuntimeError('WebSocket is not connected. Need to call "accept" first.')
RuntimeError: WebSocket is not connected. Need to call "accept" first.
2025-06-02 18:38:48,734 - requirements_websocket - ERROR - 无法发送错误消息
2025-06-02 18:48:02,024 - requirements_websocket - INFO - 收到WebSocket连接请求: /api/v1/requirements/analyze, 客户端: 127.0.0.1:51865
2025-06-02 18:48:02,025 - requirements_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5174', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'YR6j4OPmKcAeqK5tBu7ccw==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-06-02 18:48:02,026 - requirements_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:51865
2025-06-02 18:48:02,026 - requirements_websocket - INFO - WebSocket连接已建立，建议客户端设置较长的超时时间
2025-06-02 18:48:02,026 - requirements_websocket - INFO - 建议的WebSocket设置: ping_interval=30秒, ping_timeout=600秒(10分钟)
2025-06-02 18:48:02,027 - requirements_websocket - INFO - 发送测试消息成功
2025-06-02 18:48:02,028 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 18:48:02,029 - requirements_websocket - INFO - 收到前端消息: {"userId":1,"projectId":3,"content":"智能系统员工新增、编辑、删除功能","files":[],"task":"分析需求文档"}...
2025-06-02 18:48:02,029 - requirements_websocket - INFO - 成功解析JSON数据
2025-06-02 18:48:02,029 - requirements_websocket - INFO - 处理前端请求: {'userId': 1, 'projectId': 3, 'content': '智能系统员工新增、编辑、删除功能', 'files': [], 'task': '分析需求文档'}...
2025-06-02 18:48:02,030 - requirements_websocket - INFO - 用户输入的需求内容长度: 16 字符
2025-06-02 18:48:02,030 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统员工新增、编辑、删除功能
2025-06-02 18:48:02,030 - requirements_websocket - INFO - 创建需求文件消息: 用户ID=1, 文件数量=0
2025-06-02 18:48:02,030 - requirements_websocket - INFO - 开始启动需求分析运行时
2025-06-02 18:48:02,030 - requirements_websocket - INFO - 用户输入的需求内容长度: 16 字符
2025-06-02 18:48:02,030 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统员工新增、编辑、删除功能
2025-06-02 18:48:02,032 - requirements_websocket - INFO - 发送消息到前端: system, 是否最终消息: False
2025-06-02 18:48:02,032 - requirements_websocket - INFO - JSON字符串长度: 115
2025-06-02 18:48:02,033 - requirements_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-06-02 18:48:02,033 - requirements_websocket - INFO - JSON字符串长度: 142
2025-06-02 18:48:02,034 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 18:48:02,034 - requirements_websocket - INFO - JSON字符串长度: 159
2025-06-02 18:48:31,051 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 18:48:31,052 - requirements_websocket - INFO - JSON字符串长度: 132
2025-06-02 18:48:31,053 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 18:48:31,053 - requirements_websocket - INFO - JSON字符串长度: 147
2025-06-02 18:48:31,055 - requirements_websocket - INFO - 发送消息到前端: 需求分析智能体, 是否最终消息: False
2025-06-02 18:48:31,060 - requirements_websocket - INFO - JSON字符串长度: 145
2025-06-02 18:49:27,782 - requirements_websocket - INFO - 发送消息到前端: Requirement Analysis Agent, 是否最终消息: False
2025-06-02 18:49:27,783 - requirements_websocket - INFO - JSON字符串长度: 158
2025-06-02 18:49:49,302 - requirements_websocket - INFO - 发送消息到前端: 需求结构化智能体, 是否最终消息: False
2025-06-02 18:49:49,302 - requirements_websocket - INFO - JSON字符串长度: 157
2025-06-02 18:50:26,869 - requirements_websocket - INFO - 发送消息到前端: 需求结构化智能体, 是否最终消息: False
2025-06-02 18:50:26,870 - requirements_websocket - INFO - JSON字符串长度: 192
2025-06-02 18:50:26,871 - requirements_websocket - INFO - 发送消息到前端: 需求数据库智能体, 是否最终消息: False
2025-06-02 18:50:26,871 - requirements_websocket - INFO - JSON字符串长度: 135
2025-06-02 18:50:26,873 - requirements_websocket - INFO - 发送消息到前端: database, 是否最终消息: False
2025-06-02 18:50:26,879 - requirements_websocket - INFO - JSON字符串长度: 1519
2025-06-02 18:50:26,880 - requirements_websocket - INFO - 发送消息到前端: 需求数据库智能体, 是否最终消息: True
2025-06-02 18:50:26,882 - requirements_websocket - INFO - JSON字符串长度: 280
2025-06-02 18:55:32,072 - requirements_websocket - ERROR - 发送简单心跳包失败: 
2025-06-02 18:55:32,074 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 18:55:32,074 - requirements_websocket - WARNING - 心跳包发送失败次数: 1
2025-06-02 18:55:47,076 - requirements_websocket - ERROR - 发送简单心跳包失败: Cannot call "send" once a close message has been sent.
2025-06-02 18:55:47,076 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 18:55:47,076 - requirements_websocket - WARNING - 心跳包发送失败次数: 2
2025-06-02 18:56:02,077 - requirements_websocket - ERROR - 发送简单心跳包失败: Cannot call "send" once a close message has been sent.
2025-06-02 18:56:02,077 - requirements_websocket - ERROR - 发送JSON心跳包也失败: Cannot call "send" once a close message has been sent.
2025-06-02 18:56:02,078 - requirements_websocket - WARNING - 心跳包发送失败次数: 3
2025-06-02 18:56:02,078 - requirements_websocket - ERROR - 连续3次心跳包发送失败，停止心跳任务
