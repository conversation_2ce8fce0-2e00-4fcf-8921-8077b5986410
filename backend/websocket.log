2025-06-02 11:23:57,397 - requirements_websocket - INFO - 收到WebSocket连接请求: /api/v1/requirements/analyze, 客户端: 127.0.0.1:53440
2025-06-02 11:23:57,397 - requirements_websocket - INFO - 请求头: {'host': 'localhost:8000', 'connection': 'Upgrade', 'pragma': 'no-cache', 'cache-control': 'no-cache', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'upgrade': 'websocket', 'origin': 'http://localhost:5174', 'sec-websocket-version': '13', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9', 'sec-websocket-key': 'sjmayAgH1QaREFRenkx0vw==', 'sec-websocket-extensions': 'permessage-deflate; client_max_window_bits'}
2025-06-02 11:23:57,398 - requirements_websocket - INFO - WebSocket连接已接受, 客户端: 127.0.0.1:53440
2025-06-02 11:23:57,398 - requirements_websocket - INFO - WebSocket连接已建立，建议客户端设置较长的超时时间
2025-06-02 11:23:57,398 - requirements_websocket - INFO - 建议的WebSocket设置: ping_interval=30秒, ping_timeout=600秒(10分钟)
2025-06-02 11:23:57,399 - requirements_websocket - INFO - 发送测试消息成功
2025-06-02 11:23:57,399 - requirements_websocket - INFO - 等待前端发送需求分析请求
2025-06-02 11:23:57,402 - requirements_websocket - INFO - 收到前端消息: {"userId":1,"projectId":3,"content":"智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面","files":[],"task":"分析需求文档"...
2025-06-02 11:23:57,402 - requirements_websocket - INFO - 成功解析JSON数据
2025-06-02 11:23:57,402 - requirements_websocket - INFO - 处理前端请求: {'userId': 1, 'projectId': 3, 'content': '智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面', 'files': [], 'task':...
2025-06-02 11:23:57,402 - requirements_websocket - INFO - 用户输入的需求内容长度: 35 字符
2025-06-02 11:23:57,402 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面
2025-06-02 11:23:57,403 - requirements_websocket - INFO - 创建需求文件消息: 用户ID=1, 文件数量=0
2025-06-02 11:23:57,403 - requirements_websocket - INFO - 开始启动需求分析运行时
2025-06-02 11:23:57,403 - requirements_websocket - INFO - 用户输入的需求内容长度: 35 字符
2025-06-02 11:23:57,403 - requirements_websocket - INFO - 用户输入的需求内容前100个字符: 智能系统登录功能，使用指定的用户名和密码进行登录，能够正确登录到主页面
2025-06-02 11:23:57,404 - requirements_websocket - INFO - 发送消息到前端: system, 是否最终消息: False
2025-06-02 11:23:57,404 - requirements_websocket - INFO - JSON字符串长度: 115
2025-06-02 11:23:57,407 - requirements_websocket - INFO - 发送消息到前端: user, 是否最终消息: False
2025-06-02 11:23:57,407 - requirements_websocket - INFO - JSON字符串长度: 142
2025-06-02 11:23:57,408 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 11:23:57,408 - requirements_websocket - INFO - JSON字符串长度: 159
2025-06-02 11:23:57,416 - requirements_websocket - INFO - 发送消息到前端: Document Parser Agent, 是否最终消息: False
2025-06-02 11:23:57,416 - requirements_websocket - INFO - JSON字符串长度: 165
