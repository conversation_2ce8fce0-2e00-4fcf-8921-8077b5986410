/**
 * Axios配置模块
 *
 * 这个文件配置了Axios实例，包括基础URL、超时时间、请求头、拦截器等。
 * 提供了统一的错误处理和认证令牌管理。
 */

import axios from 'axios';  // 导入axios库
import { ElMessage } from 'element-plus';  // 导入Element Plus的消息组件
import router from '../router';  // 导入路由实例，用于在认证失败时重定向

/**
 * 创建自定义的axios实例
 *
 * 配置了基础URL、超时时间和默认请求头
 */
const instance = axios.create({
  // 从环境变量获取API基础URL，如果不存在则使用相对路径
  baseURL: import.meta.env.VITE_API_BASE_URL || '',
  timeout: 120000,  // 设置请求超时时间为120秒，适用于长时间运行的AI操作
  headers: {
    'Content-Type': 'application/json'  // 默认请求头，指定内容类型为JSON
  }
  // 注释掉withCredentials，因为我们不需要发送凭证
  // withCredentials: true
});

/**
 * 请求拦截器
 *
 * 在发送请求前执行，用于添加认证令牌等操作
 */
instance.interceptors.request.use(
  // 成功处理函数
  config => {
    // 从localStorage获取认证令牌
    const token = localStorage.getItem('token');
    // 如果令牌存在，添加到请求头中
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;  // 返回修改后的配置
  },
  // 错误处理函数
  error => {
    console.error('请求错误:', error);  // 记录错误信息
    return Promise.reject(error);  // 返回被拒绝的Promise
  }
);

/**
 * 响应拦截器
 *
 * 在接收响应后执行，用于处理响应数据和错误
 */
instance.interceptors.response.use(
  // 成功处理函数
  response => {
    return response;  // 直接返回响应
  },
  // 错误处理函数
  async error => {
    // 获取原始请求配置
    const originalRequest = error.config;

    // 处理超时错误
    // 如果是超时错误且未尝试过重试，则进行一次重试
    if (error.code === 'ECONNABORTED' && !originalRequest._retry) {
      originalRequest._retry = true;  // 标记为已尝试重试

      try {
        // 重试请求
        return await instance(originalRequest);
      } catch (retryError) {
        // 重试失败
        console.error('重试请求失败:', retryError);
        ElMessage.error('请求超时，请检查网络连接或稍后重试');
        return Promise.reject(retryError);
      }
    }

    // 处理响应错误
    if (error.response) {
      // 获取错误状态码和消息
      const status = error.response.status;
      const message = error.response.data?.detail || error.message;

      // 根据不同的错误状态码显示不同的错误消息
      switch (status) {
        case 400:  // 错误请求
          ElMessage.error(`请求参数错误: ${message}`);
          break;
        case 401:  // 未授权
          // 清除认证信息
          localStorage.removeItem('token');
          localStorage.removeItem('username');
          localStorage.removeItem('userId');
          // 重定向到登录页
          router.push('/login');
          ElMessage.error('登录已过期，请重新登录');
          break;
        case 403:  // 禁止访问
          ElMessage.error('没有权限执行此操作');
          break;
        case 404:  // 资源不存在
          ElMessage.error('请求的资源不存在');
          break;
        case 500:  // 服务器错误
          ElMessage.error(`服务器错误: ${message}`);
          break;
        default:  // 其他错误
          ElMessage.error(`请求失败: ${message}`);
      }
    } else if (error.request) {
      // 请求已发送但没有收到响应
      ElMessage.error('无法连接到服务器，请检查网络连接');
    } else {
      // 请求配置出错
      ElMessage.error(`请求错误: ${error.message}`);
    }

    // 返回被拒绝的Promise
    return Promise.reject(error);
  }
);

// 导出配置好的axios实例
export default instance;
