<template>
  <div class="api-document-list-container">
    <div class="page-header">
      <div class="header-buttons">
        <el-button type="success" @click="handleUploadDocument">上传文档</el-button>
        <el-button type="primary" @click="handleCreateDocument">新增接口文档</el-button>
      </div>
    </div>

    <!-- 搜索表单 -->
    <el-card class="search-form">
      <el-form :model="searchForm" label-width="100px" inline>
        <el-form-item label="所属项目">
          <el-select v-model="searchForm.project_id" placeholder="请选择项目" clearable style="width: 220px"
            @change="handleProjectChange">
            <el-option v-for="item in projectList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="文档标题">
          <el-input v-model="searchForm.title" placeholder="请输入文档标题" clearable style="width: 220px" />
        </el-form-item>

        <el-form-item label="文档格式">
          <el-select v-model="searchForm.format_type" placeholder="请选择文档格式" clearable style="width: 220px">
            <el-option label="Swagger/OpenAPI" value="swagger" />
            <el-option label="Postman" value="postman" />
          </el-select>
        </el-form-item>

        <el-form-item label="创建时间">
          <el-date-picker v-model="searchForm.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" value-format="YYYY-MM-DD" style="width: 260px" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card>
      <!-- 文档列表 -->
      <el-table v-loading="loading" :data="documentList" border style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="title" label="文档标题" min-width="150" show-overflow-tooltip />
        <el-table-column prop="format_type" label="文档格式" width="120" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.format_type.toLowerCase() === 'swagger' ? 'success' : 'primary'">
              {{ scope.row.format_type }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="project.name" label="所属项目" min-width="120" show-overflow-tooltip />
        <el-table-column prop="creator" label="创建者" width="120" align="center" />
        <el-table-column prop="created_at" label="创建时间" width="180" align="center">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="350" fixed="right" align="center">
          <template #default="scope">
            <el-button size="small" @click="handleViewDocument(scope.row)">
              查看详情
            </el-button>
            <el-button size="small" type="warning" @click="handleParseDocument(scope.row)">
              AI解析
            </el-button>
            <el-button size="small" type="primary" @click="handleGenerateTestCases(scope.row)">
              生成测试用例
            </el-button>
            <el-button size="small" type="danger" @click="handleDeleteDocument(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </el-card>
    <!-- 上传接口文档对话框 -->
    <el-dialog v-model="uploadDialogVisible" title="上传接口文档" width="600px">
      <el-form :model="uploadForm" label-width="100px" :rules="uploadRules" ref="uploadFormRef">
        <el-form-item label="文档标题" prop="title">
          <el-input v-model="uploadForm.title" placeholder="请输入文档标题" />
        </el-form-item>
        <el-form-item label="文档描述">
          <el-input v-model="uploadForm.description" type="textarea" placeholder="请输入文档描述" />
        </el-form-item>
        <el-form-item label="所属项目" prop="project_id">
          <el-select v-model="uploadForm.project_id" placeholder="请选择项目" style="width: 100%">
            <el-option v-for="item in projectList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="选择文件" prop="file">
          <el-upload ref="uploadRef" :auto-upload="false" :limit="1" :on-change="handleFileChange"
            :on-remove="handleFileRemove" accept=".json,.yaml,.yml" drag>
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                只能上传 JSON/YAML 格式的接口文档文件
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitUploadForm" :loading="uploading">
            上传
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导入接口文档对话框 -->
    <el-dialog v-model="importDialogVisible" title="新增接口文档" width="600px">
      <el-form :model="importForm" label-width="100px" :rules="importRules" ref="importFormRef">
        <el-form-item label="文档标题" prop="title">
          <el-input v-model="importForm.title" placeholder="请输入文档标题" />
        </el-form-item>
        <el-form-item label="文档描述">
          <el-input v-model="importForm.description" type="textarea" placeholder="请输入文档描述" />
        </el-form-item>
        <el-form-item label="文档格式" prop="format_type">
          <el-select v-model="importForm.format_type" placeholder="请选择文档格式" style="width: 100%">
            <el-option label="Swagger/OpenAPI" value="swagger" />
            <el-option label="Postman" value="postman" />
          </el-select>
        </el-form-item>
        <el-form-item label="所属项目" prop="project_id">
          <el-select v-model="importForm.project_id" placeholder="请选择项目" style="width: 100%">
            <el-option v-for="item in projectList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="文档内容" prop="content">
          <el-input v-model="importForm.content" type="textarea" :rows="10" placeholder="请粘贴JSON或YAML格式的接口文档内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitImportForm" :loading="importing">
            导入
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 文档详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="接口文档详情" width="800px">
      <div v-if="currentDocument">
        <h3>{{ currentDocument.title }}</h3>
        <p v-if="currentDocument.description">{{ currentDocument.description }}</p>
        <div class="document-info">
          <p><strong>文档格式：</strong>{{ currentDocument.format_type }}</p>
          <p><strong>所属项目：</strong>{{ currentDocument.project?.name }}</p>
          <p><strong>创建者：</strong>{{ currentDocument.creator }}</p>
          <p><strong>创建时间：</strong>{{ formatDate(currentDocument.created_at) }}</p>
        </div>
        <div class="document-content">
          <h4>文档内容：</h4>
          <pre>{{ formatJSON(currentDocument.content) }}</pre>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { useRouter } from 'vue-router'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'

const router = useRouter()

// 项目和文档列表
const projectList = ref([])
const documentList = ref([])
const loading = ref(false)

// 分页参数
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 搜索表单
const searchForm = reactive({
  project_id: '',
  title: '',
  format_type: '',
  dateRange: []
})

// 上传表单
const uploadDialogVisible = ref(false)
const uploadForm = reactive({
  title: '',
  description: '',
  project_id: '',
  file: null
})
const uploadRules = {
  title: [{ required: true, message: '请输入文档标题', trigger: 'blur' }],
  project_id: [{ required: true, message: '请选择所属项目', trigger: 'change' }],
  file: [{ required: true, message: '请选择文件', trigger: 'change' }]
}
const uploadFormRef = ref(null)
const uploadRef = ref(null)
const uploading = ref(false)

// 导入表单
const importDialogVisible = ref(false)
const importForm = reactive({
  title: '',
  description: '',
  format_type: 'swagger',
  project_id: '',
  content: ''
})
const importRules = {
  title: [{ required: true, message: '请输入文档标题', trigger: 'blur' }],
  format_type: [{ required: true, message: '请选择文档格式', trigger: 'change' }],
  project_id: [{ required: true, message: '请选择所属项目', trigger: 'change' }],
  content: [{ required: true, message: '请输入文档内容', trigger: 'blur' }]
}
const importFormRef = ref(null)
const importing = ref(false)

// 文档详情
const detailDialogVisible = ref(false)
const currentDocument = ref(null)

// 初始化
onMounted(() => {
  fetchProjects()
  fetchDocuments()
})

// 获取项目列表
const fetchProjects = async () => {
  try {
    const response = await axios.get('/api/projects')
    projectList.value = response.data
  } catch (error) {
    console.error('获取项目列表失败:', error)
    ElMessage.error('获取项目列表失败')
  }
}

// 获取接口文档列表
const fetchDocuments = async () => {
  loading.value = true
  try {
    const params = {
      skip: (currentPage.value - 1) * pageSize.value,
      limit: pageSize.value,
      project_id: searchForm.project_id || undefined,
      title: searchForm.title || undefined,
      format_type: searchForm.format_type || undefined
    }

    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.start_date = searchForm.dateRange[0]
      params.end_date = searchForm.dateRange[1]
    }

    console.log('发送请求参数:', params)

    const response = await axios.get('/api/api-documents', { params })
    console.log('获取到的接口文档数据:', response.data)

    // 从响应头中获取总数
    const totalCount = response.headers['x-total-count']
    console.log('从响应头获取的总数:', totalCount)

    documentList.value = response.data

    // 设置总数
    total.value = totalCount ? parseInt(totalCount) : response.data.length

    console.log(`获取到 ${response.data.length} 条接口文档，总数: ${total.value}`)

    // 如果没有数据，显示提示消息
    if (response.data.length === 0) {
      ElMessage.info('没有找到符合条件的接口文档')
    }
  } catch (error) {
    console.error('获取接口文档列表失败:', error)
    ElMessage.error('获取接口文档列表失败')
    documentList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1 // 重置为第一页
  fetchDocuments()
}

// 重置搜索
const resetSearch = () => {
  searchForm.project_id = ''
  searchForm.title = ''
  searchForm.format_type = ''
  searchForm.dateRange = []
  currentPage.value = 1 // 重置为第一页
  fetchDocuments()
}

// 项目变更
const handleProjectChange = () => {
  currentPage.value = 1 // 重置为第一页
  fetchDocuments()
}

// 分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchDocuments()
}

// 当前页变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchDocuments()
}

// 上传接口文档
const handleUploadDocument = () => {
  // 重置表单
  uploadForm.title = ''
  uploadForm.description = ''
  uploadForm.project_id = ''
  uploadForm.file = null

  // 清空上传组件
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }

  // 显示上传对话框
  uploadDialogVisible.value = true
}

// 文件选择变化
const handleFileChange = (file) => {
  uploadForm.file = file.raw
  // 自动设置标题为文件名（去掉扩展名）
  if (!uploadForm.title && file.name) {
    uploadForm.title = file.name.replace(/\.[^/.]+$/, "")
  }
}

// 文件移除
const handleFileRemove = () => {
  uploadForm.file = null
}

// 提交上传表单
const submitUploadForm = async () => {
  if (!uploadFormRef.value) return

  await uploadFormRef.value.validate(async (valid) => {
    if (valid) {
      uploading.value = true
      try {
        const formData = new FormData()
        formData.append('file', uploadForm.file)
        formData.append('title', uploadForm.title)
        formData.append('description', uploadForm.description || '')
        formData.append('project_id', uploadForm.project_id)
        formData.append('creator', 'admin')

        const response = await axios.post('/api/api-documents/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        ElMessage.success('接口文档上传成功')
        uploadDialogVisible.value = false
        fetchDocuments() // 刷新列表
      } catch (error) {
        console.error('上传接口文档失败:', error)
        ElMessage.error('上传接口文档失败: ' + (error.response?.data?.detail || error.message))
      } finally {
        uploading.value = false
      }
    }
  })
}

// 创建接口文档
const handleCreateDocument = () => {
  // 重置表单
  importForm.title = ''
  importForm.description = ''
  importForm.format_type = 'swagger'
  importForm.project_id = ''
  importForm.content = ''

  // 显示导入对话框
  importDialogVisible.value = true
}

// 提交导入表单
const submitImportForm = async () => {
  if (!importFormRef.value) return

  await importFormRef.value.validate(async (valid) => {
    if (valid) {
      importing.value = true
      try {
        const response = await axios.post('/api/api-documents/import', importForm)
        ElMessage.success('接口文档导入成功')
        importDialogVisible.value = false
        fetchDocuments() // 刷新列表
      } catch (error) {
        console.error('导入接口文档失败:', error)
        ElMessage.error('导入接口文档失败: ' + (error.response?.data?.detail || error.message))
      } finally {
        importing.value = false
      }
    }
  })
}

// 查看文档详情
const handleViewDocument = (document) => {
  currentDocument.value = document
  detailDialogVisible.value = true
}

// AI解析文档
const handleParseDocument = async (document) => {
  try {
    ElMessageBox.confirm(
      `确定要使用AI解析接口文档 "${document.title}" 吗？`,
      'AI解析文档',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(async () => {
      try {
        const response = await axios.post(`/api/api-documents/${document.id}/parse`)
        ElMessage.success('AI解析任务已启动，请稍候查看结果')
      } catch (error) {
        console.error('AI解析文档失败:', error)
        ElMessage.error('AI解析文档失败: ' + (error.response?.data?.detail || error.message))
      }
    })
  } catch (error) {
    // 用户取消操作
  }
}

// 生成测试用例
const handleGenerateTestCases = async (document) => {
  try {
    ElMessageBox.confirm(
      `确定要根据接口文档 "${document.title}" 生成测试用例吗？`,
      '生成测试用例',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(async () => {
      try {
        const response = await axios.post(`/api/api-documents/${document.id}/generate-testcases`)
        ElMessage.success('测试用例生成任务已启动，请稍候查看结果')
        // 跳转到测试用例列表页面
        router.push({
          path: '/apitest/testcase-list',
          query: { api_document_id: document.id }
        })
      } catch (error) {
        console.error('生成测试用例失败:', error)
        ElMessage.error('生成测试用例失败: ' + (error.response?.data?.detail || error.message))
      }
    })
  } catch (error) {
    // 用户取消操作
  }
}

// 删除文档
const handleDeleteDocument = (document) => {
  ElMessageBox.confirm(
    `确定要删除接口文档 "${document.title}" 吗？`,
    '删除接口文档',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await axios.delete(`/api/api-documents/${document.id}`)
      ElMessage.success('接口文档删除成功')
      fetchDocuments() // 刷新列表
    } catch (error) {
      console.error('删除接口文档失败:', error)
      ElMessage.error('删除接口文档失败: ' + (error.response?.data?.detail || error.message))
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString()
}

// 格式化JSON
const formatJSON = (jsonString) => {
  if (!jsonString) return ''
  try {
    const obj = typeof jsonString === 'string' ? JSON.parse(jsonString) : jsonString
    return JSON.stringify(obj, null, 2)
  } catch (e) {
    return jsonString
  }
}
</script>

<style scoped>
.api-document-list-container {
  padding: 20px 15px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-buttons {
  display: flex;
  gap: 10px;
}
.el-card {
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.el-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.document-info {
  margin: 15px 0;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.document-content {
  margin-top: 15px;
}

.document-content pre {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  overflow: auto;
  max-height: 400px;
}
</style>
