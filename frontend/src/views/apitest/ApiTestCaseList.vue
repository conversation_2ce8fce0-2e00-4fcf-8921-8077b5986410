<template>
  <div class="api-testcase-list-container">
    <div class="page-header">
      <h2>接口测试用例管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleCreateTestCase">新增测试用例</el-button>
        <el-button type="success" @click="handleExecuteSelected" :disabled="selectedTestCases.length === 0">
          执行选中测试用例
        </el-button>
        <el-dropdown @command="handleExportCommand" :disabled="selectedTestCases.length === 0">
          <el-button type="warning">
            导出选中用例<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="excel">导出为Excel</el-dropdown-item>
              <el-dropdown-item command="csv">导出为CSV</el-dropdown-item>
              <el-dropdown-item command="json">导出为JSON</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-dropdown @command="handleScriptCommand" :disabled="selectedTestCases.length === 0">
          <el-button type="info">
            生成测试脚本<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="pytest">生成Pytest脚本</el-dropdown-item>
              <el-dropdown-item command="unittest">生成Unittest脚本</el-dropdown-item>
              <el-dropdown-item command="requests">生成Requests脚本</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 搜索表单 -->
    <el-card class="search-form">
      <el-form :model="searchForm" label-width="100px" inline>
        <el-form-item label="所属项目">
          <el-select v-model="searchForm.project_id" placeholder="请选择项目" clearable style="width: 220px"
            @change="handleProjectChange">
            <el-option v-for="item in projectList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="接口文档">
          <el-select v-model="searchForm.api_document_id" placeholder="请选择接口文档" clearable style="width: 220px">
            <el-option v-for="item in documentList" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>

        <el-form-item label="用例标题">
          <el-input v-model="searchForm.title" placeholder="请输入用例标题" clearable style="width: 220px" />
        </el-form-item>

        <el-form-item label="执行状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 220px">
            <el-option label="未执行" value="未执行" />
            <el-option label="通过" value="通过" />
            <el-option label="失败" value="失败" />
          </el-select>
        </el-form-item>

        <el-form-item label="创建时间">
          <el-date-picker v-model="searchForm.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" value-format="YYYY-MM-DD" style="width: 260px" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card>
      <!-- 测试用例列表 -->
      <el-table v-loading="loading" :data="testCaseList" border style="width: 100%"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="title" label="用例标题" min-width="150" show-overflow-tooltip />
        <el-table-column prop="api_path" label="接口路径" min-width="150" show-overflow-tooltip />
        <el-table-column prop="method" label="HTTP方法" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getMethodTagType(scope.row.method)">
              {{ scope.row.method }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="执行状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="api_document.title" label="所属文档" min-width="120" show-overflow-tooltip />
        <el-table-column prop="project.name" label="所属项目" min-width="120" show-overflow-tooltip />
        <el-table-column prop="created_at" label="创建时间" width="180" align="center">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="280" fixed="right" align="center">
          <template #default="scope">
            <el-button size="small" @click="handleViewTestCase(scope.row)">
              查看详情
            </el-button>
            <el-button size="small" type="success" @click="handleExecuteTestCase(scope.row)">
              执行
            </el-button>
            <el-button size="small" type="danger" @click="handleDeleteTestCase(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </el-card>
    <!-- 测试用例详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="接口测试用例详情" width="800px">
      <div v-if="currentTestCase">
        <h3>{{ currentTestCase.title }}</h3>
        <p v-if="currentTestCase.description">{{ currentTestCase.description }}</p>
        <div class="testcase-info">
          <p><strong>接口路径：</strong>{{ currentTestCase.api_path }}</p>
          <p><strong>HTTP方法：</strong>{{ currentTestCase.method }}</p>
          <p><strong>执行状态：</strong>{{ currentTestCase.status }}</p>
          <p><strong>所属文档：</strong>{{ currentTestCase.api_document?.title }}</p>
          <p><strong>所属项目：</strong>{{ currentTestCase.project?.name }}</p>
          <p><strong>创建者：</strong>{{ currentTestCase.creator }}</p>
          <p><strong>创建时间：</strong>{{ formatDate(currentTestCase.created_at) }}</p>
        </div>
        <div class="testcase-details">
          <el-tabs>
            <el-tab-pane label="请求参数">
              <div v-if="currentTestCase.headers">
                <h4>请求头：</h4>
                <pre>{{ formatJSON(currentTestCase.headers) }}</pre>
              </div>
              <div v-if="currentTestCase.params">
                <h4>查询参数：</h4>
                <pre>{{ formatJSON(currentTestCase.params) }}</pre>
              </div>
              <div v-if="currentTestCase.body">
                <h4>请求体：</h4>
                <pre>{{ formatJSON(currentTestCase.body) }}</pre>
              </div>
            </el-tab-pane>
            <el-tab-pane label="预期结果">
              <div>
                <h4>预期状态码：</h4>
                <p>{{ currentTestCase.expected_status }}</p>
              </div>
              <div v-if="currentTestCase.expected_response">
                <h4>预期响应：</h4>
                <pre>{{ formatJSON(currentTestCase.expected_response) }}</pre>
              </div>
              <div v-if="currentTestCase.validation_rules">
                <h4>验证规则：</h4>
                <pre>{{ formatJSON(currentTestCase.validation_rules) }}</pre>
              </div>
            </el-tab-pane>
            <el-tab-pane label="执行结果" v-if="executionResults.length > 0">
              <div v-for="(result, index) in executionResults" :key="index" class="execution-result">
                <h4>执行时间：{{ formatDateTime(result.executed_at) }}</h4>
                <p><strong>执行状态：</strong>
                  <el-tag :type="getStatusTagType(result.status)">{{ result.status }}</el-tag>
                </p>
                <p><strong>实际状态码：</strong>{{ result.actual_status }}</p>
                <p><strong>执行时间：</strong>{{ result.execution_time }}ms</p>
                <p v-if="result.error_message"><strong>错误信息：</strong>{{ result.error_message }}</p>
                <div v-if="result.actual_response">
                  <h4>实际响应：</h4>
                  <pre>{{ formatJSON(result.actual_response) }}</pre>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-dialog>

    <!-- 执行测试对话框 -->
    <el-dialog v-model="executeDialogVisible" title="执行接口测试" width="500px">
      <el-form :model="executeForm" label-width="100px">
        <el-form-item label="测试环境">
          <el-select v-model="executeForm.environment" placeholder="请选择测试环境" style="width: 100%">
            <el-option label="开发环境" value="开发环境" />
            <el-option label="测试环境" value="测试环境" />
            <el-option label="预发布环境" value="预发布环境" />
            <el-option label="生产环境" value="生产环境" />
          </el-select>
        </el-form-item>
        <el-form-item label="基础URL">
          <el-input v-model="executeForm.base_url" placeholder="请输入基础URL，例如：http://api.example.com" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="executeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitExecuteForm" :loading="executing">
            开始执行
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, watch } from 'vue'
import { useRoute } from 'vue-router'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'

const route = useRoute()

// 项目、文档和测试用例列表
const projectList = ref([])
const documentList = ref([])
const testCaseList = ref([])
const loading = ref(false)

// 分页参数
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 搜索表单
const searchForm = reactive({
  project_id: '',
  api_document_id: '',
  title: '',
  status: '',
  dateRange: []
})

// 选中的测试用例
const selectedTestCases = ref([])

// 测试用例详情
const detailDialogVisible = ref(false)
const currentTestCase = ref(null)
const executionResults = ref([])

// 执行测试
const executeDialogVisible = ref(false)
const executeForm = reactive({
  environment: '测试环境',
  base_url: '',
  testcase_ids: []
})
const executing = ref(false)

// 初始化
onMounted(() => {
  fetchProjects()

  // 如果URL中有api_document_id参数，自动设置搜索条件
  if (route.query.api_document_id) {
    searchForm.api_document_id = parseInt(route.query.api_document_id)
  }

  fetchTestCases()
})

// 监听项目变化，更新文档列表
watch(() => searchForm.project_id, (newVal) => {
  if (newVal) {
    fetchDocuments(newVal)
  } else {
    documentList.value = []
  }
})

// 获取项目列表
const fetchProjects = async () => {
  try {
    const response = await axios.get('/api/projects')
    projectList.value = response.data

    // 如果URL中有api_document_id参数，需要获取对应的文档列表
    if (searchForm.api_document_id && !documentList.value.length) {
      fetchDocuments()
    }
  } catch (error) {
    console.error('获取项目列表失败:', error)
    ElMessage.error('获取项目列表失败')
  }
}

// 获取接口文档列表
const fetchDocuments = async (projectId) => {
  try {
    const params = {}
    if (projectId) {
      params.project_id = projectId
    }

    const response = await axios.get('/api/api-documents', { params })
    documentList.value = response.data
  } catch (error) {
    console.error('获取接口文档列表失败:', error)
    ElMessage.error('获取接口文档列表失败')
  }
}

// 获取接口测试用例列表
const fetchTestCases = async () => {
  loading.value = true
  try {
    const params = {
      skip: (currentPage.value - 1) * pageSize.value,
      limit: pageSize.value,
      project_id: searchForm.project_id || undefined,
      api_document_id: searchForm.api_document_id || undefined,
      title: searchForm.title || undefined,
      status: searchForm.status || undefined
    }

    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.start_date = searchForm.dateRange[0]
      params.end_date = searchForm.dateRange[1]
    }

    console.log('发送请求参数:', params)

    const response = await axios.get('/api/api-testcases', { params })
    console.log('获取到的接口测试用例数据:', response.data)

    // 从响应头中获取总数
    const totalCount = response.headers['x-total-count']
    console.log('从响应头获取的总数:', totalCount)

    testCaseList.value = response.data

    // 设置总数
    total.value = totalCount ? parseInt(totalCount) : response.data.length

    console.log(`获取到 ${response.data.length} 条接口测试用例，总数: ${total.value}`)

    // 如果没有数据，显示提示消息
    if (response.data.length === 0) {
      ElMessage.info('没有找到符合条件的接口测试用例')
    }
  } catch (error) {
    console.error('获取接口测试用例列表失败:', error)
    ElMessage.error('获取接口测试用例列表失败')
    testCaseList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1 // 重置为第一页
  fetchTestCases()
}

// 重置搜索
const resetSearch = () => {
  searchForm.project_id = ''
  searchForm.api_document_id = ''
  searchForm.title = ''
  searchForm.status = ''
  searchForm.dateRange = []
  currentPage.value = 1 // 重置为第一页
  fetchTestCases()
}

// 项目变更
const handleProjectChange = () => {
  searchForm.api_document_id = '' // 清空接口文档选择
  currentPage.value = 1 // 重置为第一页
  fetchTestCases()
}

// 分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchTestCases()
}

// 当前页变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchTestCases()
}

// 选择变化
const handleSelectionChange = (selection) => {
  selectedTestCases.value = selection
}

// 创建测试用例
const handleCreateTestCase = () => {
  ElMessage.info('暂未实现，请通过导入接口文档生成测试用例')
}

// 查看测试用例详情
const handleViewTestCase = async (testCase) => {
  currentTestCase.value = testCase
  detailDialogVisible.value = true

  // 获取执行结果
  try {
    const response = await axios.get('/api/api-test-executions', {
      params: { api_testcase_id: testCase.id }
    })
    executionResults.value = response.data
  } catch (error) {
    console.error('获取测试执行结果失败:', error)
    executionResults.value = []
  }
}

// 执行单个测试用例
const handleExecuteTestCase = (testCase) => {
  executeForm.testcase_ids = [testCase.id]
  executeDialogVisible.value = true
}

// 执行选中的测试用例
const handleExecuteSelected = () => {
  if (selectedTestCases.value.length === 0) {
    ElMessage.warning('请先选择要执行的测试用例')
    return
  }

  executeForm.testcase_ids = selectedTestCases.value.map(item => item.id)
  executeDialogVisible.value = true
}

// 提交执行表单
const submitExecuteForm = async () => {
  executing.value = true
  try {
    const response = await axios.post('/api/api-testcases/execute', {
      testcase_ids: executeForm.testcase_ids,
      environment: executeForm.environment,
      base_url: executeForm.base_url || undefined,
      executed_by: 'admin'
    })

    ElMessage.success(response.data.message)
    executeDialogVisible.value = false

    // 延迟刷新列表，等待后台执行完成
    setTimeout(() => {
      fetchTestCases()
    }, 2000)
  } catch (error) {
    console.error('执行测试用例失败:', error)
    ElMessage.error('执行测试用例失败: ' + (error.response?.data?.detail || error.message))
  } finally {
    executing.value = false
  }
}

// 删除测试用例
const handleDeleteTestCase = (testCase) => {
  ElMessageBox.confirm(
    `确定要删除测试用例 "${testCase.title}" 吗？`,
    '删除测试用例',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await axios.delete(`/api/api-testcases/${testCase.id}`)
      ElMessage.success('测试用例删除成功')
      fetchTestCases() // 刷新列表
    } catch (error) {
      console.error('删除测试用例失败:', error)
      ElMessage.error('删除测试用例失败: ' + (error.response?.data?.detail || error.message))
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 获取HTTP方法对应的标签类型
const getMethodTagType = (method) => {
  const methodMap = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'DELETE': 'danger',
    'PATCH': 'info'
  }
  return methodMap[method] || 'info'
}

// 获取状态对应的标签类型
const getStatusTagType = (status) => {
  const statusMap = {
    '未执行': 'info',
    '通过': 'success',
    '失败': 'danger'
  }
  return statusMap[status] || 'info'
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString()
}

// 格式化日期时间
const formatDateTime = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString()
}

// 导出命令处理
const handleExportCommand = async (command) => {
  if (selectedTestCases.value.length === 0) {
    ElMessage.warning('请先选择要导出的测试用例')
    return
  }

  try {
    const testcaseIds = selectedTestCases.value.map(item => item.id)
    const response = await axios.post('/api/api-testcases/export', {
      testcase_ids: testcaseIds,
      format_type: command
    }, {
      responseType: 'blob'
    })

    // 创建下载链接
    const blob = new Blob([response.data])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url

    // 设置文件名
    const extension = command === 'excel' ? 'xlsx' : command
    link.download = `api_testcases.${extension}`

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success(`成功导出${selectedTestCases.value.length}个测试用例`)
  } catch (error) {
    console.error('导出测试用例失败:', error)
    ElMessage.error('导出测试用例失败: ' + (error.response?.data?.detail || error.message))
  }
}

// 脚本生成命令处理
const handleScriptCommand = async (command) => {
  if (selectedTestCases.value.length === 0) {
    ElMessage.warning('请先选择要生成脚本的测试用例')
    return
  }

  try {
    const testcaseIds = selectedTestCases.value.map(item => item.id)
    const response = await axios.post('/api/generate-python-script', {
      testcase_ids: testcaseIds,
      script_type: command,
      project_id: searchForm.project_id || selectedTestCases.value[0].project_id,
      title: `${command}_test_script`,
      description: `基于${selectedTestCases.value.length}个测试用例生成的${command}测试脚本`
    })

    ElMessage.success(`${command}测试脚本生成任务已启动，请稍候查看结果`)
  } catch (error) {
    console.error('生成测试脚本失败:', error)
    ElMessage.error('生成测试脚本失败: ' + (error.response?.data?.detail || error.message))
  }
}

// 格式化JSON
const formatJSON = (jsonString) => {
  if (!jsonString) return ''
  try {
    const obj = typeof jsonString === 'string' ? JSON.parse(jsonString) : jsonString
    return JSON.stringify(obj, null, 2)
  } catch (e) {
    return jsonString
  }
}
</script>

<style scoped>
.api-testcase-list-container {
  padding: 20px 15px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.el-card {
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.el-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.testcase-info {
  margin: 15px 0;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.testcase-details pre {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  overflow: auto;
  max-height: 300px;
}

.execution-result {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.execution-result:last-child {
  border-bottom: none;
}
</style>
