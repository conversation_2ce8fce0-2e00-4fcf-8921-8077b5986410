<template>
  <div class="project-list-container">
    <div class="page-header">
      <div class="page-title">项目管理</div>
      <div class="page-actions">
        <el-button type="primary" @click="$router.push('/project/create')">新建项目</el-button>
      </div>
    </div>

    <el-card class="search-form">
      <el-form :model="searchForm" inline>
        <el-form-item label="项目名称">
          <el-input v-model="searchForm.name" placeholder="请输入项目名称" clearable style="width: 220px;" />
        </el-form-item>

        <el-form-item label="创建时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card>
      <el-table
        v-loading="loading"
        :data="projectList"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="项目ID" width="80" />
        <el-table-column prop="project_code" label="项目编号" width="120" />
        <el-table-column prop="name" label="项目名称" />
        <el-table-column prop="description" label="项目描述" show-overflow-tooltip />
        <el-table-column label="创建时间" width="120">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="160" fixed="right">
          <template #default="scope">
            <div class="table-actions">
              <el-button
                size="small"
                type="primary"
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'

// 日期格式化函数
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toISOString().split('T')[0]; // 返回 YYYY-MM-DD 格式
}

const router = useRouter()

// 搜索表单
const searchForm = reactive({
  name: '',
  dateRange: []
})

// 分页参数
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 项目列表
const projectList = ref([])
const loading = ref(false)

// 获取项目列表
const fetchProjects = async () => {
  loading.value = true
  try {
    const params = {
      skip: (currentPage.value - 1) * pageSize.value,
      limit: pageSize.value,
      name: searchForm.name || undefined
    }

    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.start_date = searchForm.dateRange[0]
      params.end_date = searchForm.dateRange[1]
    }

    const response = await axios.get('/api/projects', { params })
    projectList.value = response.data
    total.value = response.data.length // 实际应该从响应头或响应体中获取总数
  } catch (error) {
    console.error('获取项目列表失败:', error)
    ElMessage.error('获取项目列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchProjects()
}

// 重置搜索
const resetSearch = () => {
  searchForm.name = ''
  searchForm.dateRange = []
  handleSearch()
}

// 编辑项目
const handleEdit = (row) => {
  router.push(`/project/edit/${row.id}`)
}

// 删除项目
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除项目 "${row.name}" 吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
    .then(async () => {
      try {
        await axios.delete(`/api/projects/${row.id}`)
        ElMessage.success('删除成功')
        fetchProjects()
      } catch (error) {
        console.error('删除项目失败:', error)
        ElMessage.error('删除项目失败')
      }
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchProjects()
}

// 当前页变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchProjects()
}

onMounted(() => {
  fetchProjects()
})
</script>

<style scoped>
.project-list-container {
  padding: 20px 15px; /* 上下保持20px，左右减小到2px */
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.page-actions {
  display: flex;
  gap: 10px;
}

.page-title {
  font-size: 20px;
  font-weight: bold;
}

.el-card {
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.el-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

:deep(.el-card__body) {
  padding: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

:deep(.table-actions) {
  display: flex;
  flex-wrap: nowrap;
  gap: 5px;
}

/* 表格列标题加粗居中 */
:deep(.el-table__header th) {
  font-weight: bold;
  text-align: center !important;
}

:deep(.el-table__header th .cell) {
  text-align: center;
}
</style>
