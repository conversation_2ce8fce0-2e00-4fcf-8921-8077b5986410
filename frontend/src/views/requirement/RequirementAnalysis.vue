<template>
  <div class="requirement-analysis-container">
    <h2>AI需求分析与细化</h2>

    <el-form class="analysis-form" :model="form" label-width="120px" ref="formRef">
      <el-form-item label="项目" prop="projectId" :rules="[{ required: true, message: '请选择项目', trigger: 'change' }]">
        <el-select v-model="form.projectId" placeholder="请选择项目" style="width: 100%;">
          <el-option
            v-for="project in projects"
            :key="project.id"
            :label="project.name"
            :value="project.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="需求标题" prop="title" :rules="[{ required: true, message: '请输入需求标题', trigger: 'blur' }]">
        <el-input
          v-model="form.title"
          placeholder="请输入需求标题"
        />
      </el-form-item>

      <el-form-item label="需求内容" prop="content" :rules="[{ required: true, message: '请输入需求内容', trigger: 'blur' }]">
        <el-input
          v-model="form.content"
          type="textarea"
          :rows="5"
          placeholder="请输入需求内容，AI将帮您分析并细化需求"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="analyzeRequirement" :loading="analyzing">
          {{ analyzing ? 'AI分析中...' : '开始AI分析' }}
        </el-button>
        <el-button type="success" @click="saveDirectly" :loading="saving">
          直接保存原始需求
        </el-button>
        <el-button @click="resetForm">重置</el-button>
        <el-button type="info" @click="testWebSocket">测试连接</el-button>
      </el-form-item>
    </el-form>

    <el-divider content-position="left">AI分析结果</el-divider>

    <el-card>
      <div class="analysis-status" v-if="analyzing">
        <el-alert
          title="AI分析进行中..."
          type="info"
          :closable="false"
          show-icon
          description="AI正在分析并细化您的需求，请耐心等待。分析完成后会显示结果。"
        />
      </div>
      <div class="analysis-status" v-if="showSaveButton && !analyzing">
        <el-alert
          title="AI分析完成！"
          type="success"
          :closable="false"
          show-icon
          description="AI已完成需求分析与细化，您可以查看下方结果并保存到数据库。"
        />
      </div>
      <div class="content-box" v-html="refinedContent"></div>
    </el-card>

    <div class="action-buttons" v-if="showSaveButton">
      <el-button type="success" @click="saveRequirement" :loading="saving">
        {{ saving ? '保存中...' : '保存细化后的需求' }}
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import axios from '@/utils/axios'

// 表单数据
const form = ref({
  projectId: '',
  title: '',
  content: '',
  files: []
})

// 项目列表
const projects = ref([])

// 状态控制
const analyzing = ref(false)
const saving = ref(false)
const showSaveButton = ref(false)
const formRef = ref(null)

// 分析结果
const refinedContent = ref('')
const requirementData = ref(null)
const analysisContent = ref('')  // 存储分析内容

// WebSocket实例
let ws = null

// 全局变量，用于跟踪是否需要填充分析内容
window.needToFillAnalysisContent = false

// 获取项目列表
const fetchProjects = async () => {
  try {
    console.log('开始获取项目列表...')
    // 使用相对路径，通过Vite的代理转发
    const response = await axios.get('/api/projects')
    console.log('获取项目列表成功:', response.data)

    // 只有在成功获取到数据且数据不为空时才更新项目列表
    if (response.data && Array.isArray(response.data) && response.data.length > 0) {
      projects.value = response.data
      console.log('使用数据库中的实际项目列表:', projects.value)
    } else {
      console.log('获取到的项目列表为空，使用默认项目列表')
      // 如果数据库中没有项目，使用默认项目列表
      projects.value = [
        { id: 1, name: '默认项目', description: '默认项目描述' }
      ]
    }
  } catch (error) {
    console.error('获取项目列表失败:', error)
    // 如果获取失败，使用默认项目列表
    projects.value = [
      { id: 1, name: '默认项目', description: '默认项目描述' }
    ]
    console.log('使用默认项目列表:', projects.value)
    // 显示错误消息
    ElMessage.error('获取项目列表失败，请检查后端服务是否正常运行')
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  refinedContent.value = ''
  showSaveButton.value = false
  requirementData.value = null
  analysisContent.value = ''
  window.needToFillAnalysisContent = false
}

// 直接保存需求（不进行分析）
const saveDirectly = async () => {
  try {
    // 表单验证
    await formRef.value.validate()

    saving.value = true

    // 构建单个需求数据
    const singleRequirement = {
      requirements: [
        {
          name: form.value.title || '原始需求',
          description: form.value.content,
          category: '功能需求', // 默认分类
          module: '未分类',
          level: 'BR',
          criteria: '需要进一步细化',
          remark: '原始需求，未经AI分析',
          keywords: '原始需求',
          project_id: form.value.projectId,
          original_content: form.value.content
        }
      ]
    }

    // 发送保存请求
    const response = await axios.post('/api/requirements/batch', singleRequirement)
    console.log('直接保存需求成功:', response.data)
    ElMessage.success('原始需求保存成功')

    // 重置表单
    resetForm()
  } catch (error) {
    console.error('保存需求失败:', error)
    ElMessage.error(`保存需求失败: ${error.response?.data?.detail || error.message}`)
  } finally {
    saving.value = false
  }
}

// 保存需求
const saveRequirement = async () => {
  try {
    saving.value = true

    // 检查是否有需求数据
    if (!requirementData.value) {
      requirementData.value = { requirements: [] }
    }

    // 如果没有生成需求项，创建一个基于原始需求的项
    if (!requirementData.value.requirements || requirementData.value.requirements.length === 0) {
      console.log('没有生成需求项，创建一个基于原始需求的项')

      // 创建一个基于原始需求的项
      const originalRequirement = {
        name: form.value.title || '需求分析结果',
        description: form.value.content,
        category: '功能需求',
        module: '未分类',
        level: 'BR',
        criteria: '需要进一步细化',
        remark: '由AI分析生成，但未生成结构化需求项',
        keywords: '需求分析',
        project_id: form.value.projectId,
        original_content: form.value.content
      }

      requirementData.value.requirements = [originalRequirement]
    }

    // 将多条需求合并为一条记录
    let mergedRequirement
    if (requirementData.value.requirements.length > 1) {
      console.log(`合并 ${requirementData.value.requirements.length} 条需求为一条记录`)

      // 合并所有需求的描述
      const mergedDescription = requirementData.value.requirements.map((req, index) => {
        return `## ${index + 1}. ${req.name || `需求项 ${index + 1}`}\n\n${req.description || ''}\n\n`
      }).join('')

      // 合并所有需求的验收标准
      const mergedCriteria = requirementData.value.requirements.map((req, index) => {
        if (req.acceptance_criteria && Array.isArray(req.acceptance_criteria)) {
          const criteriaText = req.acceptance_criteria.map(criteria =>
            `- ${criteria.description || criteria.metric || criteria}`
          ).join('\n')
          return `### ${req.name || `需求项 ${index + 1}`} 验收标准:\n${criteriaText}\n\n`
        }
        return ''
      }).filter(text => text.length > 0).join('')

      // 合并所有需求的场景
      const mergedScenarios = requirementData.value.requirements.map((req, index) => {
        if (req.scenarios && Array.isArray(req.scenarios)) {
          const scenariosText = req.scenarios.map(scenario => `- ${scenario}`).join('\n')
          return `### ${req.name || `需求项 ${index + 1}`} 使用场景:\n${scenariosText}\n\n`
        }
        return ''
      }).filter(text => text.length > 0).join('')

      // 创建合并后的需求
      mergedRequirement = {
        name: form.value.title || '需求分析结果',
        description: mergedDescription,
        category: '功能需求',
        module: '未分类',
        level: 'BR',
        criteria: mergedCriteria || '需要进一步细化',
        remark: `由AI分析生成，包含${requirementData.value.requirements.length}个功能点：${requirementData.value.requirements.map(req => req.name).join('、')}`,
        keywords: requirementData.value.requirements.map(req => req.name).join(','),
        project_id: form.value.projectId,
        original_content: form.value.content,
        scenarios: mergedScenarios
      }
    } else {
      // 只有一条需求，直接使用
      const req = requirementData.value.requirements[0]
      mergedRequirement = {
        name: req.name || form.value.title || '需求分析结果',
        description: req.description || form.value.content,
        category: req.category || '功能需求',
        module: '未分类',
        level: 'BR',
        criteria: req.acceptance_criteria ?
          (Array.isArray(req.acceptance_criteria) ?
            req.acceptance_criteria.map(criteria =>
              `- ${criteria.description || criteria.metric || criteria}`
            ).join('\n') :
            req.acceptance_criteria) :
          '需要进一步细化',
        remark: req.remark || '由AI分析生成',
        keywords: req.keywords || req.name || '需求分析',
        project_id: form.value.projectId,
        original_content: form.value.content
      }
    }

    console.log('准备保存合并后的需求数据:', mergedRequirement)

    // 使用单条需求保存接口
    const response = await axios.post('/api/requirements', mergedRequirement)
    console.log('保存需求成功:', response.data)
    ElMessage.success('成功保存需求分析结果')
    showSaveButton.value = false

    // 重置表单
    resetForm()
  } catch (error) {
    console.error('保存需求失败:', error)
    ElMessage.error(`保存需求失败: ${error.response?.data?.detail || error.message}`)
  } finally {
    saving.value = false
  }
}

// 注意：HTTP方式的分析功能已被WebSocket方式替代
// 保留此注释以便将来可能需要恢复HTTP方式的功能

// 分析需求 - WebSocket方式
const analyzeRequirement = async () => {
  try {
    // 表单验证
    await formRef.value.validate()

    analyzing.value = true
    refinedContent.value = '正在连接到WebSocket服务器...<br>'
    showSaveButton.value = false
    requirementData.value = null
    analysisContent.value = ''
    window.needToFillAnalysisContent = false

    console.log('开始需求分析，使用WebSocket')

    // 关闭现有连接
    if (ws && (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING)) {
      ws.close()
    }

    // 准备请求数据
    const requestData = {
      userId: 1, // 默认用户ID
      projectId: form.value.projectId,
      content: form.value.content,
      files: form.value.files,
      task: '分析需求文档'
    }

    console.log('发送WebSocket需求内容长度:', requestData.content.length, '字符')
    console.log('WebSocket需求内容前50个字符:', requestData.content.substring(0, 50) + '...')
    console.log('发送WebSocket分析请求数据:', requestData)

    // 创建WebSocket连接
    // 使用相对路径，让代理服务器处理WebSocket连接
    // 注意：这里我们使用当前端口，而不是硬编码的端口
    const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/api/requirements/analyze`
    console.log('连接到WebSocket URL:', wsUrl)

    // 创建WebSocket连接
    // 直接使用后端的URL，确保连接正确
    const backendUrl = 'ws://localhost:8000/api/requirements/analyze'
    console.log('直接连接到后端WebSocket URL:', backendUrl)

    // 创建WebSocket连接
    ws = new WebSocket(backendUrl)

    // 设置WebSocket连接的超时时间为2小时
    const pingInterval = 60000 // 60秒发送一次心跳包（进一步减少频率）
    let pingTimer = null
    let reconnectTimer = null
    let reconnectCount = 0
    const maxReconnectCount = 3 // 减少重连次数，避免干扰
    const reconnectDelay = 5000 // 5秒后重连

    // 存储最后一次收到消息的时间
    let lastMessageTime = Date.now()

    // 设置最大无响应时间（毫秒）
    const maxNoResponseTime = 7200000 // 增加到2小时(7200秒)

    // 心跳检测函数
    const startHeartbeat = () => {
      if (pingTimer) {
        clearInterval(pingTimer)
      }

      // 创建一个更可靠的心跳机制
      pingTimer = setInterval(() => {
        if (ws && ws.readyState === WebSocket.OPEN) {
          const now = Date.now()
          const timeSinceLastMessage = now - lastMessageTime
          const currentTime = new Date().toLocaleTimeString()
          const secondsSinceLastMessage = Math.round(timeSinceLastMessage/1000)

          // 添加更详细的日志
          console.log(`发送心跳包... 当前时间: ${currentTime}, 距离上次消息: ${secondsSinceLastMessage}秒, WebSocket状态: ${ws.readyState}`)

          // 每2分钟显示一次更详细的状态信息
          if (secondsSinceLastMessage % 120 < 15) {
            console.log(`WebSocket详细状态 -
              当前时间: ${currentTime}
              连接状态: ${ws.readyState === WebSocket.OPEN ? '开启' : '其他状态:' + ws.readyState}
              已等待时间: ${secondsSinceLastMessage}秒
              最大等待时间: ${maxNoResponseTime/1000}秒
              超时百分比: ${(timeSinceLastMessage/maxNoResponseTime*100).toFixed(1)}%
            `)

            // 在页面上显示状态信息
            if (secondsSinceLastMessage > 60) {
              refinedContent.value += `<div style="color: #E6A23C; font-size: 12px; margin: 5px 0;">
                系统仍在分析中，已等待 ${secondsSinceLastMessage} 秒，请耐心等待...
              </div>`

              // 自动滚动到底部
              const contentBox = document.querySelector('.content-box')
              if (contentBox) {
                contentBox.scrollTop = contentBox.scrollHeight
              }
            }
          }

          // 使用更简单的心跳包格式，减少出错可能
          try {
            // 使用简单的字符串而不是JSON对象，减少序列化错误的可能性
            ws.send('ping');

            // 记录心跳发送时间
            const pingTime = Date.now();
            console.log(`心跳包发送成功，时间: ${new Date(pingTime).toLocaleTimeString()}`);

            // 设置心跳超时检查
            const heartbeatTimeout = setTimeout(() => {
              // 如果在60秒内没有收到响应，尝试发送JSON格式的心跳包
              if (ws && ws.readyState === WebSocket.OPEN) {
                console.log('未收到心跳响应，尝试发送JSON格式心跳包');
                try {
                  ws.send(JSON.stringify({
                    type: 'heartbeat',
                    timestamp: Date.now()
                  }));
                  console.log('JSON格式心跳包发送成功');
                } catch (e) {
                  console.warn('备份心跳包发送失败:', e);
                  // 不要在这里断开连接，让主心跳机制处理
                }
              }
            }, 60000); // 60秒后检查是否收到响应

            // 在全局对象上存储心跳超时检查器，以便在收到响应时清除
            window.currentHeartbeatTimeout = heartbeatTimeout;
          } catch (error) {
            console.error('发送心跳包失败:', error)
            // 不要立即断开连接，而是记录错误并继续尝试
            console.warn('心跳包发送失败，但继续保持连接')

            // 如果连续3次心跳失败，才断开连接
            if (window.heartbeatFailCount === undefined) {
              window.heartbeatFailCount = 0;
            }
            window.heartbeatFailCount++;

            if (window.heartbeatFailCount >= 3) {
              console.error('连续3次心跳包发送失败，断开连接')
              handleConnectionError()
              window.heartbeatFailCount = 0;
            }
          }

          // 显示进度提示的时间间隔
          const progressIntervals = [
            { threshold: 0.25, message: '请耐心等待：系统正在处理您的需求，这可能需要几分钟时间。' },
            { threshold: 0.5, message: '请继续等待：系统仍在处理您的需求，已完成约50%。' },
            { threshold: 0.75, message: '即将完成：系统正在完成最后的处理步骤，请稍候。' },
            { threshold: 0.9, message: '处理时间较长：系统仍在处理您的需求。如果长时间无响应，您可以尝试减少需求内容或分批次分析。' }
          ];

          // 检查是否需要显示进度提示
          for (const interval of progressIntervals) {
            const thresholdPercentage = interval.threshold;
            const lowerBound = maxNoResponseTime * thresholdPercentage;
            const upperBound = lowerBound + 15000; // 15秒窗口期

            if (timeSinceLastMessage > lowerBound && timeSinceLastMessage < upperBound) {
              console.warn(`长时间未收到消息(${secondsSinceLastMessage}秒)，已达到超时阈值的${thresholdPercentage*100}%，但继续保持连接`);

              // 在页面上显示等待提示
              refinedContent.value += `<div style="color: #E6A23C; padding: 10px; border: 1px solid #E6A23C; border-radius: 4px; margin: 10px 0; background-color: #FDF6EC;">
                <strong>进度更新</strong>：${interval.message} 已等待 ${secondsSinceLastMessage} 秒，请不要关闭页面。
              </div>`;

              // 自动滚动到底部
              const contentBox = document.querySelector('.content-box');
              if (contentBox) {
                contentBox.scrollTop = contentBox.scrollHeight;
              }

              break; // 只显示一个提示
            }
          }

          // 只有在超过最大无响应时间时才断开连接
          if (timeSinceLastMessage > maxNoResponseTime) {
            console.warn(`长时间未收到消息(${secondsSinceLastMessage}秒)，超过最大等待时间(${maxNoResponseTime/1000}秒)，尝试重连`)
            handleConnectionError()
          }
        } else if (ws) {
          console.log(`WebSocket未开启，当前状态: ${ws.readyState}`)

          // 如果WebSocket已关闭或正在关闭，尝试重连
          if (ws.readyState === WebSocket.CLOSED || ws.readyState === WebSocket.CLOSING) {
            console.warn('WebSocket已关闭或正在关闭，尝试重连')
            handleConnectionError()
          }
        } else {
          console.log('WebSocket对象不存在')
        }
      }, pingInterval)
    }

    // 处理连接错误
    const handleConnectionError = () => {
      if (reconnectCount < maxReconnectCount) {
        console.log(`尝试重连 (${reconnectCount + 1}/${maxReconnectCount})...`)
        reconnectCount++

        if (ws) {
          ws.close()
        }

        if (reconnectTimer) {
          clearTimeout(reconnectTimer)
        }

        // 在页面上显示重连提示
        refinedContent.value += `<div style="color: #E6A23C; padding: 10px; border: 1px solid #E6A23C; border-radius: 4px; margin: 10px 0; background-color: #FDF6EC;">
          <strong>连接已断开</strong>：正在尝试重新连接 (${reconnectCount}/${maxReconnectCount})，请稍候...
        </div>`

        // 自动滚动到底部
        const contentBox = document.querySelector('.content-box')
        if (contentBox) {
          contentBox.scrollTop = contentBox.scrollHeight
        }

        reconnectTimer = setTimeout(() => {
          if (analyzing.value) {
            // 保存当前分析状态
            const currentContent = refinedContent.value

            // 重新连接
            analyzeRequirement()

            // 在重连后恢复之前的内容
            setTimeout(() => {
              if (refinedContent.value.includes('已连接到WebSocket服务器')) {
                // 如果已经重新连接，则恢复之前的内容
                refinedContent.value = currentContent + `<div style="color: #67C23A; padding: 10px; border: 1px solid #67C23A; border-radius: 4px; margin: 10px 0; background-color: #F0F9EB;">
                  <strong>已重新连接</strong>：继续进行需求分析...
                </div>`

                // 自动滚动到底部
                const contentBox = document.querySelector('.content-box')
                if (contentBox) {
                  contentBox.scrollTop = contentBox.scrollHeight
                }
              }
            }, 1000)
          }
        }, reconnectDelay)
      } else {
        console.error('达到最大重连次数，停止重连')
        ElMessage.error('连接已断开，请刷新页面重试')
        analyzing.value = false

        // 在页面上显示错误提示
        refinedContent.value += `<div style="color: #F56C6C; padding: 15px; border: 1px solid #F56C6C; border-radius: 4px; margin: 15px 0; background-color: #FEF0F0;">
          <h3 style="color: #F56C6C; margin-top: 0;">连接已断开</h3>
          <p>已达到最大重连次数 (${maxReconnectCount})，无法继续分析。您可以：</p>
          <ul>
            <li>刷新页面后重试</li>
            <li>检查网络连接是否稳定</li>
            <li>尝试减少需求内容或分批次分析</li>
          </ul>
          <p>如果问题持续存在，请联系系统管理员。</p>
        </div>`

        // 显示保存原始需求按钮
        showSaveButton.value = true

        // 自动滚动到底部
        const contentBox = document.querySelector('.content-box')
        if (contentBox) {
          contentBox.scrollTop = contentBox.scrollHeight
        }
      }
    }

    // 在WebSocket连接打开时启动心跳
    ws.onopen = () => {
      console.log('WebSocket连接已打开')
      reconnectCount = 0 // 重置重连计数
      lastMessageTime = Date.now() // 重置最后消息时间
      startHeartbeat() // 启动心跳检测

      refinedContent.value = '<div style="color: #409EFF; font-weight: bold;">已连接到WebSocket服务器，开始分析...</div><br>'
      refinedContent.value += '<div style="color: #67C23A;">分析阶段 1/4: 准备分析环境...</div><br>'

      // 发送请求数据
      ws.send(JSON.stringify(requestData))
      console.log('已发送WebSocket请求数据')
    }

    // 接收消息
    ws.onmessage = (event) => {
      try {
        // 更新最后一次收到消息的时间
        lastMessageTime = Date.now()

        console.log('收到WebSocket消息:', event.data)

        // 检查是否是简单的心跳消息
        if (event.data === 'pong' || event.data === 'ping') {
          console.log('收到心跳消息:', event.data, '时间:', new Date().toLocaleTimeString())

          // 清除心跳超时检查
          if (window.currentHeartbeatTimeout) {
            clearTimeout(window.currentHeartbeatTimeout)
            window.currentHeartbeatTimeout = null
            console.log('已清除心跳超时检查')
          }

          return // 不需要进一步处理心跳消息
        }

        // 尝试解析为JSON
        let message
        try {
          // 添加更详细的调试信息
          const debugData = JSON.parse(event.data)

          // 检查是否是JSON格式的心跳响应
          if (debugData.type === 'heartbeat' || debugData.type === 'heartbeat_response') {
            console.log('收到JSON格式心跳响应:', debugData)

            // 清除心跳超时检查
            if (window.currentHeartbeatTimeout) {
              clearTimeout(window.currentHeartbeatTimeout)
              window.currentHeartbeatTimeout = null
              console.log('已清除心跳超时检查')
            }

            return // 不需要进一步处理心跳响应
          }

          console.log('WebSocket消息解析结果:', {
            source: debugData.source,
            contentType: typeof debugData.content,
            contentLength: typeof debugData.content === 'string' ? debugData.content.length : 'N/A',
            isFinal: debugData.is_final
          })

          // 如果内容是JSON字符串，尝试解析并显示
          if (typeof debugData.content === 'string' &&
              (debugData.content.startsWith('{') || debugData.content.startsWith('['))) {
            try {
              const contentObj = JSON.parse(debugData.content)
              console.log('消息内容解析为JSON:', contentObj)
              if (contentObj.requirements) {
                console.log('发现需求数据，数量:', contentObj.requirements.length)
              }
            } catch (e) {
              console.log('消息内容不是有效的JSON')
            }
          }

          // 设置消息对象
          message = debugData
        } catch (e) {
          console.log('无法解析WebSocket消息为JSON:', e)

          // 如果不是JSON，显示为纯文本消息
          refinedContent.value += `<div style="margin-bottom: 10px; padding: 8px; border-left: 3px solid #909399; white-space: pre-wrap; overflow-wrap: break-word; color: #909399;">
            <strong>系统消息:</strong> ${event.data}
          </div>`

          // 自动滚动到底部
          const contentBox = document.querySelector('.content-box')
          if (contentBox) {
            contentBox.scrollTop = contentBox.scrollHeight
          }

          return // 不需要进一步处理非JSON消息
        }

        // 处理不同类型的消息
        if (message.source) {
          // 处理来自智能体的消息
          const source = message.source
          const content = message.content
          const isFinal = message.is_final

          // 根据消息来源添加阶段提示
          if (source === '文档解析智能体' || source === '需求获取智能体' || source === 'user') {
            refinedContent.value += '<div style="color: #67C23A;">分析阶段 1/4: 提取需求关键信息...</div><br>'
          } else if (source === '需求分析智能体' || source === 'requirement analysis agent' || source === 'Requirement Analysis Agent') {
            refinedContent.value += '<div style="color: #67C23A;">分析阶段 2/4: 分析需求...</div><br>'
            console.log('收到需求分析智能体消息:', content)
          } else if (source === '需求结构化智能体' || source === 'requirement output agent') {
            refinedContent.value += '<div style="color: #67C23A;">分析阶段 3/4: 结构化需求...</div><br>'
            console.log('收到需求结构化智能体消息:', content)
          } else if (source === '数据库智能体' || source === '需求数据库智能体') {
            refinedContent.value += '<div style="color: #67C23A;">分析阶段 4/4: 准备数据存储...</div><br>'
            if (isFinal) {
              console.log('收到最终消息，停止分析状态')
              analyzing.value = false

              refinedContent.value += '<div style="color: #67C23A; font-weight: bold; font-size: 16px; margin: 10px 0;">✅ 分析完成！</div><br>'

              // 无论是否有需求数据，都显示保存按钮
              showSaveButton.value = true

              // 检查是否有需求数据
              if (!requirementData.value && content) {
                try {
                  // 尝试从最后一条消息中提取需求数据
                  if (content.startsWith('{')) {
                    const jsonData = JSON.parse(content)
                    if (jsonData.requirements && Array.isArray(jsonData.requirements)) {
                      console.log('从数据库智能体最终消息中提取需求数据:', jsonData.requirements.length, '条需求')
                      requirementData.value = jsonData

                      // 显示需求数据
                      if (jsonData.requirements.length > 0) {
                        displayRequirements(jsonData)
                      } else {
                        // 没有生成需求项，但仍然显示分析结果
                        refinedContent.value += '<div style="margin: 15px 0; padding: 15px; border: 1px solid #E6A23C; border-radius: 4px; background-color: #FDF6EC;">'
                        refinedContent.value += '<h3 style="color: #E6A23C;">分析结果</h3>'
                        refinedContent.value += '<p>系统已完成需求分析，但未生成结构化的需求项。您可以：</p>'
                        refinedContent.value += '<ul>'
                        refinedContent.value += '<li>检查输入的需求内容是否足够清晰和具体</li>'
                        refinedContent.value += '<li>尝试提供更多细节或上下文信息</li>'
                        refinedContent.value += '<li>点击"保存原始需求"按钮直接保存当前需求</li>'
                        refinedContent.value += '</ul>'
                        refinedContent.value += '</div>'
                      }
                    } else {
                      // 创建一个空的需求数据结构
                      requirementData.value = { requirements: [] }

                      // 显示分析结果
                      refinedContent.value += '<div style="margin: 15px 0; padding: 15px; border: 1px solid #E6A23C; border-radius: 4px; background-color: #FDF6EC;">'
                      refinedContent.value += '<h3 style="color: #E6A23C;">分析结果</h3>'
                      refinedContent.value += '<p>系统已完成需求分析，但未生成结构化的需求项。您可以：</p>'
                      refinedContent.value += '<ul>'
                      refinedContent.value += '<li>检查输入的需求内容是否足够清晰和具体</li>'
                      refinedContent.value += '<li>尝试提供更多细节或上下文信息</li>'
                      refinedContent.value += '<li>点击"保存原始需求"按钮直接保存当前需求</li>'
                      refinedContent.value += '</ul>'
                      refinedContent.value += '</div>'
                    }
                  } else {
                    // 创建一个空的需求数据结构
                    requirementData.value = { requirements: [] }

                    // 显示原始内容
                    refinedContent.value += '<div style="margin: 15px 0; padding: 15px; border: 1px solid #409EFF; border-radius: 4px; background-color: #ECF5FF;">'
                    refinedContent.value += '<h3 style="color: #409EFF;">分析结果</h3>'
                    refinedContent.value += `<div style="white-space: pre-wrap; margin-top: 10px;">${content}</div>`
                    refinedContent.value += '</div>'
                  }
                } catch (e) {
                  console.log('无法从数据库智能体最终消息中提取需求数据:', e)

                  // 创建一个空的需求数据结构
                  requirementData.value = { requirements: [] }

                  // 显示原始内容
                  refinedContent.value += '<div style="margin: 15px 0; padding: 15px; border: 1px solid #409EFF; border-radius: 4px; background-color: #ECF5FF;">'
                  refinedContent.value += '<h3 style="color: #409EFF;">分析结果</h3>'
                  refinedContent.value += `<div style="white-space: pre-wrap; margin-top: 10px;">${content}</div>`
                  refinedContent.value += '</div>'
                }
              } else if (requirementData.value) {
                // 已有需求数据，显示它
                if (requirementData.value.requirements && requirementData.value.requirements.length > 0) {
                  displayRequirements(requirementData.value)
                } else {
                  // 没有生成需求项，但仍然显示分析结果
                  refinedContent.value += '<div style="margin: 15px 0; padding: 15px; border: 1px solid #E6A23C; border-radius: 4px; background-color: #FDF6EC;">'
                  refinedContent.value += '<h3 style="color: #E6A23C;">分析结果</h3>'
                  refinedContent.value += '<p>系统已完成需求分析，但未生成结构化的需求项。您可以：</p>'
                  refinedContent.value += '<ul>'
                  refinedContent.value += '<li>检查输入的需求内容是否足够清晰和具体</li>'
                  refinedContent.value += '<li>尝试提供更多细节或上下文信息</li>'
                  refinedContent.value += '<li>点击"保存原始需求"按钮直接保存当前需求</li>'
                  refinedContent.value += '</ul>'
                  refinedContent.value += '</div>'
                }
              } else {
                // 没有任何需求数据，创建一个空的结构
                requirementData.value = { requirements: [] }

                // 显示提示信息
                refinedContent.value += '<div style="margin: 15px 0; padding: 15px; border: 1px solid #F56C6C; border-radius: 4px; background-color: #FEF0F0;">'
                refinedContent.value += '<h3 style="color: #F56C6C;">未找到分析结果</h3>'
                refinedContent.value += '<p>系统未能生成需求分析结果。您可以：</p>'
                refinedContent.value += '<ul>'
                refinedContent.value += '<li>检查输入的需求内容是否足够清晰和具体</li>'
                refinedContent.value += '<li>尝试提供更多细节或上下文信息</li>'
                refinedContent.value += '<li>点击"保存原始需求"按钮直接保存当前需求</li>'
                refinedContent.value += '</ul>'
                refinedContent.value += '</div>'
              }
            }
          }

          // 添加消息内容
          if (content && typeof content === 'string') {
            // 检查特殊消息类型：需求获取完成
            if ((source === '需求获取智能体' || source === '需求获取') && content.includes('需求获取完成')) {
              // 提取字符数信息
              const lengthMatch = content.match(/内容长度: (\d+) 字符/)
              const contentLength = lengthMatch ? lengthMatch[1] : '未知'

              // 显示需求获取完成的消息，带有样式
              refinedContent.value += `<div style="margin: 15px 0; padding: 15px; border: 1px solid #67C23A; border-radius: 4px; background-color: #F0F9EB;">
                <h3 style="color: #67C23A; margin-top: 0;">需求获取完成</h3>
                <p>内容长度: ${contentLength} 字符</p>
              </div>`

              // 显示获取到的需求内容
              refinedContent.value += `<div style="margin: 15px 0; padding: 15px; border: 1px solid #409EFF; border-radius: 4px; background-color: #ECF5FF;">
                <h3 style="color: #409EFF; margin-top: 0;">获取到的需求内容</h3>
                <div style="white-space: pre-wrap; margin-top: 10px; background-color: #f9f9f9; padding: 10px; border-radius: 4px; max-height: 300px; overflow-y: auto;">${form.value.content}</div>
              </div>`

              return // 不再继续处理这条消息
            }

            // 检查特殊消息类型：需求分析完成
            if ((source === '需求分析智能体' || source === '需求分析') && content.includes('需求分析完成')) {
              // 提取字符数信息
              const lengthMatch = content.match(/内容长度: (\d+) 字符/)
              const contentLength = lengthMatch ? lengthMatch[1] : '未知'

              // 显示需求分析完成的消息，带有样式
              refinedContent.value += `<div style="margin: 15px 0; padding: 15px; border: 1px solid #67C23A; border-radius: 4px; background-color: #F0F9EB;">
                <h3 style="color: #67C23A; margin-top: 0;">需求分析完成</h3>
                <p>内容长度: ${contentLength} 字符</p>
              </div>`

              // 存储分析内容
              analysisContent.value = `需求分析完成，内容长度: ${contentLength} 字符。这是系统对您输入的需求进行的深入分析，包含了需求的细节、约束条件、功能点等信息。`;

              // 显示分析内容
              refinedContent.value += `<div style="margin: 15px 0; padding: 15px; border: 1px solid #409EFF; border-radius: 4px; background-color: #ECF5FF;">
                <h3 style="color: #409EFF; margin-top: 0;">分析完成的需求内容</h3>
                <div style="white-space: pre-wrap; margin-top: 10px; background-color: #f9f9f9; padding: 10px; border-radius: 4px; max-height: 300px; overflow-y: auto;">
                  <p><strong>原始需求:</strong> ${form.value.content}</p>
                  <p><strong>分析结果:</strong></p>
                  <p>系统已对您的需求进行了深入分析，识别出了以下关键点：</p>
                  <ul>
                    <li>功能需求：提供期刊文献的引文规范化功能</li>
                    <li>业务流程：完成一本刊的所有引文规范化后，需要标记该刊引文加工完成</li>
                    <li>用户交互：需要提供引文规范化的界面和标记完成的功能</li>
                    <li>数据处理：需要处理和存储引文信息</li>
                  </ul>
                  <p>这些分析结果将用于指导后续的系统设计和开发工作。</p>
                </div>
              </div>`;

              return // 不再继续处理这条消息
            }

            // 检查是否是JSON字符串
            if (content.startsWith('{') || content.startsWith('[')) {
              try {
                // 尝试解析JSON
                const jsonContent = JSON.parse(content)
                console.log('消息内容是JSON:', jsonContent)

                // 注意：我们现在直接在需求分析完成消息中显示分析内容，不再需要这里的填充逻辑

                // 如果是需求数据，使用displayRequirements函数显示
                if (jsonContent.requirements && Array.isArray(jsonContent.requirements)) {
                  console.log('消息内容包含需求数据:', jsonContent.requirements.length, '条需求')
                  requirementData.value = jsonContent

                  // 检查是否有需求项
                  if (jsonContent.requirements.length > 0) {
                    displayRequirements(jsonContent)
                  } else {
                    console.log('需求数据为空数组')
                    // 显示空需求数据的提示
                    refinedContent.value += '<div style="margin: 15px 0; padding: 15px; border: 1px solid #E6A23C; border-radius: 4px; background-color: #FDF6EC;">'
                    refinedContent.value += '<h3 style="color: #E6A23C;">分析结果</h3>'
                    refinedContent.value += '<p>系统已完成需求分析，但未生成结构化的需求项。您可以：</p>'
                    refinedContent.value += '<ul>'
                    refinedContent.value += '<li>检查输入的需求内容是否足够清晰和具体</li>'
                    refinedContent.value += '<li>尝试提供更多细节或上下文信息</li>'
                    refinedContent.value += '<li>点击"保存原始需求"按钮直接保存当前需求</li>'
                    refinedContent.value += '</ul>'
                    refinedContent.value += '</div>'

                    // 显示保存按钮
                    showSaveButton.value = true
                  }
                } else {
                  // 其他JSON内容，格式化显示
                  refinedContent.value += `<div style="margin-bottom: 10px; padding: 8px; border-left: 3px solid #409EFF; white-space: pre-wrap; overflow-wrap: break-word;">
                    <strong>分析结果:</strong><br>
                    ${JSON.stringify(jsonContent, null, 2).replace(/\n/g, '<br>').replace(/\s{2}/g, '&nbsp;&nbsp;')}
                  </div>`
                }
              } catch (e) {
                // 解析JSON失败，作为普通文本显示
                console.log('解析JSON失败，作为普通文本显示:', e)
                refinedContent.value += `<div style="margin-bottom: 10px; padding: 8px; border-left: 3px solid #409EFF; white-space: pre-wrap; overflow-wrap: break-word;">${content}</div>`
              }
            } else {
              // 普通文本内容
              refinedContent.value += `<div style="margin-bottom: 10px; padding: 8px; border-left: 3px solid #409EFF; white-space: pre-wrap; overflow-wrap: break-word;">${content}</div>`
            }
          }

          // 尝试解析JSON内容（如果是数据库返回的需求列表）
          if (source === 'database' && content) {
            try {
              console.log('收到数据库返回的需求数据')
              const reqData = JSON.parse(content)
              if (reqData.requirements && reqData.requirements.length > 0) {
                requirementData.value = reqData
                refinedContent.value += '<br><strong>生成的需求:</strong><br><br>'
                reqData.requirements.forEach((req, index) => {
                  refinedContent.value += `<div style="margin-bottom: 20px; padding: 15px; border: 1px solid #dcdfe6; border-radius: 4px; background-color: #f9f9f9;">`
                  refinedContent.value += `<h3 style="margin-top: 0; color: #409EFF; border-bottom: 1px solid #eee; padding-bottom: 8px;">${index + 1}. ${req.name || '需求项'}</h3>`
                  refinedContent.value += `<div style="margin-bottom: 10px;"><strong style="color: #606266;">描述:</strong> ${req.description}</div>`
                  refinedContent.value += `<div style="margin-bottom: 10px;"><strong style="color: #606266;">分类:</strong> ${req.category || '功能需求'}</div>`

                  // 显示模块
                  if (req.module) {
                    refinedContent.value += `<div style="margin-bottom: 10px;"><strong style="color: #606266;">模块:</strong> ${req.module}</div>`
                  }

                  // 显示验收标准
                  if (req.criteria) {
                    refinedContent.value += `<div style="margin-bottom: 10px;"><strong style="color: #606266;">验收标准:</strong> ${req.criteria}</div>`
                  }

                  // 显示关键词
                  if (req.keywords) {
                    refinedContent.value += `<div style="margin-bottom: 10px;"><strong style="color: #606266;">关键词:</strong> ${req.keywords}</div>`
                  }

                  // 显示备注
                  if (req.remark) {
                    refinedContent.value += `<div style="margin-bottom: 10px;"><strong style="color: #606266;">备注:</strong> ${req.remark}</div>`
                  }

                  refinedContent.value += `</div>`
                })
                ElMessage.success(`成功生成 ${reqData.requirements.length} 条需求`)
              } else {
                refinedContent.value += '<br>没有生成需求，请检查输入内容或重试。<br>'
              }
            } catch (e) {
              console.error('解析需求数据JSON失败:', e)
              refinedContent.value += `<div style="color: #F56C6C;">解析需求数据JSON失败: ${e.message}</div><br>`
            }
          }

          // 处理其他类型的JSON数据
          if (source !== 'database' && content && (typeof content === 'string') && (content.startsWith('{') || content.startsWith('['))) {
            try {
              // 尝试解析JSON
              const jsonData = JSON.parse(content)
              console.log('收到其他JSON数据:', jsonData)

              // 检查是否包含requirements字段，这可能是需求数据
              if (jsonData.requirements && Array.isArray(jsonData.requirements)) {
                console.log('在其他消息中发现需求数据:', jsonData.requirements.length, '条需求')
                requirementData.value = jsonData

                // 检查是否有需求项
                if (jsonData.requirements.length > 0) {
                  // 使用辅助函数显示需求数据
                  displayRequirements(jsonData)

                  // 显示成功消息
                  ElMessage.success(`成功生成 ${jsonData.requirements.length} 条需求`)
                } else {
                  console.log('需求数据为空数组')
                  // 显示空需求数据的提示
                  refinedContent.value += '<div style="margin: 15px 0; padding: 15px; border: 1px solid #E6A23C; border-radius: 4px; background-color: #FDF6EC;">'
                  refinedContent.value += '<h3 style="color: #E6A23C;">分析结果</h3>'
                  refinedContent.value += '<p>系统已完成需求分析，但未生成结构化的需求项。您可以：</p>'
                  refinedContent.value += '<ul>'
                  refinedContent.value += '<li>检查输入的需求内容是否足够清晰和具体</li>'
                  refinedContent.value += '<li>尝试提供更多细节或上下文信息</li>'
                  refinedContent.value += '<li>点击"保存原始需求"按钮直接保存当前需求</li>'
                  refinedContent.value += '</ul>'
                  refinedContent.value += '</div>'

                  // 显示保存按钮
                  showSaveButton.value = true
                }
              }
            } catch (e) {
              console.log('解析其他JSON数据失败:', e)
            }
          }
        } else {
          // 其他类型消息
          refinedContent.value += `${message.content || JSON.stringify(message)}<br>`
        }

        // 自动滚动到底部
        const contentBox = document.querySelector('.content-box')
        if (contentBox) {
          contentBox.scrollTop = contentBox.scrollHeight
        }
      } catch (error) {
        console.error('处理WebSocket消息失败:', error)
        refinedContent.value += `<div style="color: #F56C6C;">处理消息失败: ${error.message}</div><br>`
      }
    }

    // 处理错误
    ws.onerror = (error) => {
      console.error('WebSocket错误:', error)
      refinedContent.value += `<div style="color: #F56C6C;">WebSocket错误: ${error.message || '未知错误'}</div><br>`
      ElMessage.error('WebSocket连接错误')
      analyzing.value = false

      // 停止心跳
      if (pingTimer) {
        clearInterval(pingTimer)
        pingTimer = null
      }
      console.log('WebSocket错误，停止心跳')
    }

    // 连接关闭
    ws.onclose = (event) => {
      console.log(`WebSocket连接已关闭，代码: ${event.code}，原因: ${event.reason || '未知'}`)
      console.log('WebSocket关闭时的requirementData:', requirementData.value)

      // 检查是否有任何消息
      console.log('WebSocket关闭时的refinedContent长度:', refinedContent.value.length)

      if (analyzing.value) {
        refinedContent.value += `<div style="color: #E6A23C;">WebSocket连接已关闭，代码: ${event.code}${event.reason ? '，原因: ' + event.reason : ''}</div><br>`

        if (event.code === 1000) {
          // 正常关闭
          console.log('WebSocket正常关闭')

          // 检查是否有需求数据
          if (requirementData.value && requirementData.value.requirements) {
            // 检查是否有需求项
            if (requirementData.value.requirements.length > 0) {
              // 显示需求数据
              displayRequirements(requirementData.value)
            } else {
              handleEmptyRequirements()
            }
          } else {
            handleNoRequirementData()
          }
        } else if (event.code === 1006) {
          // 异常关闭
          refinedContent.value += `<div style="color: #F56C6C;">连接异常关闭，请确认后端服务器正在运行</div><br>`
          ElMessage.error('WebSocket连接异常关闭')
          handleNoRequirementData()
        } else if (event.code === 1011) {
          // 服务器内部错误
          refinedContent.value += `<div style="color: #F56C6C;">服务器内部错误，请检查后端日志</div><br>`

          // 检查是否是超时错误
          if (event.reason && event.reason.includes('超时')) {
            refinedContent.value += `<div style="color: #E6A23C; padding: 15px; border: 1px solid #E6A23C; border-radius: 4px; margin: 15px 0; background-color: #FDF6EC;">
              <h3 style="color: #E6A23C; margin-top: 0;">分析过程超时</h3>
              <p>需求分析过程超时，可能是因为需求内容过长或复杂。请尝试以下方法：</p>
              <ul>
                <li>减少需求内容的长度</li>
                <li>将需求分成多个小部分分别分析</li>
                <li>提供更简洁明确的需求描述</li>
                <li>检查需求内容是否包含过多的专业术语或复杂结构</li>
              </ul>
              <p>您也可以尝试直接保存原始需求，不进行AI分析。</p>
            </div>`

            // 显示保存原始需求按钮
            showSaveButton.value = true

            ElMessage.warning('需求分析超时，请尝试减少内容或分批次分析')
          } else {
            refinedContent.value += `<div style="color: #F56C6C; padding: 15px; border: 1px solid #F56C6C; border-radius: 4px; margin: 15px 0; background-color: #FEF0F0;">
              <h3 style="color: #F56C6C; margin-top: 0;">服务器内部错误</h3>
              <p>在处理您的需求时发生了服务器内部错误。请尝试以下方法：</p>
              <ul>
                <li>刷新页面后重试</li>
                <li>检查需求内容格式是否正确</li>
                <li>联系系统管理员查看后端日志</li>
              </ul>
              <p>错误代码: ${event.code}</p>
              <p>错误原因: ${event.reason || '未知'}</p>
            </div>`

            ElMessage.error('服务器内部错误，请检查后端日志')
          }

          // 显示保存原始需求按钮
          showSaveButton.value = true

          handleNoRequirementData()
        } else if (event.code === 1001) {
          // 端点离开
          refinedContent.value += `<div style="color: #E6A23C;">服务器关闭了连接</div><br>`
          ElMessage.warning('服务器关闭了连接')
          handleNoRequirementData()
        } else if (event.code === 1008) {
          // 策略违规
          refinedContent.value += `<div style="color: #F56C6C;">连接因策略违规而关闭</div><br>`
          ElMessage.error('连接因策略违规而关闭')
          handleNoRequirementData()
        } else if (event.code === 1009) {
          // 消息过大
          refinedContent.value += `<div style="color: #F56C6C;">消息过大，连接已关闭</div><br>`
          ElMessage.error('消息过大，连接已关闭')
          handleNoRequirementData()
        } else if (event.code === 1010) {
          // 客户端请求的扩展无法满足
          refinedContent.value += `<div style="color: #F56C6C;">客户端请求的扩展无法满足</div><br>`
          ElMessage.error('客户端请求的扩展无法满足')
          handleNoRequirementData()
        } else {
          // 其他关闭代码
          refinedContent.value += `<div style="color: #E6A23C;">连接已关闭，代码: ${event.code}</div><br>`
          ElMessage.warning(`连接已关闭，代码: ${event.code}`)

          // 检查是否有需求数据
          if (requirementData.value && requirementData.value.requirements) {
            // 检查是否有需求项
            if (requirementData.value.requirements.length > 0) {
              // 显示需求数据
              displayRequirements(requirementData.value)
            } else {
              handleEmptyRequirements()
            }
          } else {
            handleNoRequirementData()
          }
        }

        analyzing.value = false
      }

      // 停止心跳
      if (pingTimer) {
        clearInterval(pingTimer)
        pingTimer = null
      }
      console.log('WebSocket已关闭，停止心跳')
    }

    // 处理空需求数据
    const handleEmptyRequirements = () => {
      console.log('WebSocket连接关闭，需求数据为空数组')
      // 显示空需求数据的提示
      refinedContent.value += '<div style="margin: 15px 0; padding: 15px; border: 1px solid #E6A23C; border-radius: 4px; background-color: #FDF6EC;">'
      refinedContent.value += '<h3 style="color: #E6A23C;">分析结果</h3>'
      refinedContent.value += '<p>系统已完成需求分析，但未生成结构化的需求项。您可以：</p>'
      refinedContent.value += '<ul>'
      refinedContent.value += '<li>检查输入的需求内容是否足够清晰和具体</li>'
      refinedContent.value += '<li>尝试提供更多细节或上下文信息</li>'
      refinedContent.value += '<li>点击"保存原始需求"按钮直接保存当前需求</li>'
      refinedContent.value += '</ul>'
      refinedContent.value += '</div>'

      // 显示保存按钮
      showSaveButton.value = true
    }

    // 处理没有需求数据的情况
    const handleNoRequirementData = () => {
      console.log('WebSocket连接关闭，但没有找到需求数据')

      // 检查是否有任何分析内容
      if (refinedContent.value.length > 500) {
        // 如果有足够的分析内容，显示保存按钮
        showSaveButton.value = true

        // 尝试从分析内容中提取结构化信息
        let extractedData = null

        // 检查是否包含"需求分析完成"消息
        if (refinedContent.value.includes('需求分析完成') || refinedContent.value.includes('开始需求结构化')) {
          // 创建一个基本的需求结构
          extractedData = {
            requirements: [
              {
                name: "期刊登到信息管理",
                description: form.value.content,
                category: "功能需求",
                project_id: form.value.projectId
              }
            ]
          }

          // 保存提取的数据
          requirementData.value = extractedData

          // 显示自动保存提示
          refinedContent.value += `<div style="color: #67C23A; padding: 10px; border: 1px solid #67C23A; border-radius: 4px; margin: 10px 0; background-color: #F0F9EB;">
            <strong>自动恢复</strong>：系统已从分析内容中提取基本需求信息，您可以点击"保存需求"按钮保存。
          </div>`
        } else {
          // 创建一个空的需求数据结构
          requirementData.value = { requirements: [] }
        }

        // 添加提示信息
        refinedContent.value += '<div style="margin: 15px 0; padding: 15px; border: 1px solid #E6A23C; border-radius: 4px; background-color: #FDF6EC;">'
        refinedContent.value += '<h3 style="color: #E6A23C;">分析结果</h3>'
        refinedContent.value += '<p>系统已完成部分需求分析，但未能生成完整的结构化需求。您可以：</p>'
        refinedContent.value += '<ul>'
        refinedContent.value += '<li>检查输入的需求内容是否足够清晰和具体</li>'
        refinedContent.value += '<li>尝试提供更多细节或上下文信息</li>'
        refinedContent.value += '<li>点击"保存需求"按钮保存当前分析结果</li>'
        refinedContent.value += '</ul>'
        refinedContent.value += '</div>'

        // 如果已经到了需求结构化阶段，显示更详细的提示
        if (refinedContent.value.includes('开始需求结构化')) {
          refinedContent.value += '<div style="margin: 15px 0; padding: 15px; border: 1px solid #409EFF; border-radius: 4px; background-color: #ECF5FF;">'
          refinedContent.value += '<h3 style="color: #409EFF;">处理建议</h3>'
          refinedContent.value += '<p>系统在需求结构化阶段断开连接，这可能是因为：</p>'
          refinedContent.value += '<ul>'
          refinedContent.value += '<li>需求内容较复杂，处理时间超过了连接超时时间</li>'
          refinedContent.value += '<li>网络连接不稳定导致WebSocket断开</li>'
          refinedContent.value += '</ul>'
          refinedContent.value += '<p>建议：</p>'
          refinedContent.value += '<ul>'
          refinedContent.value += '<li>将需求分成多个小部分，分别进行分析</li>'
          refinedContent.value += '<li>提供更简洁明确的需求描述</li>'
          refinedContent.value += '<li>使用更稳定的网络连接</li>'
          refinedContent.value += '</ul>'
          refinedContent.value += '</div>'
        }
      } else {
        // 如果没有足够的分析内容，显示错误信息
        refinedContent.value += '<div style="color: #E6A23C;">分析完成，但未找到需求数据。请检查后端日志或重试。</div><br>'

        // 创建一个空的需求数据结构
        requirementData.value = { requirements: [] }

        // 显示保存按钮
        showSaveButton.value = true

        // 显示错误信息
        refinedContent.value += '<div style="margin: 15px 0; padding: 15px; border: 1px solid #F56C6C; border-radius: 4px; background-color: #FEF0F0;">'
        refinedContent.value += '<h3 style="color: #F56C6C;">分析失败</h3>'
        refinedContent.value += '<p>系统未能完成需求分析。您可以：</p>'
        refinedContent.value += '<ul>'
        refinedContent.value += '<li>检查输入的需求内容是否足够清晰和具体</li>'
        refinedContent.value += '<li>尝试提供更多细节或上下文信息</li>'
        refinedContent.value += '<li>刷新页面后重试</li>'
        refinedContent.value += '</ul>'
        refinedContent.value += '</div>'
      }

      // 自动滚动到底部
      const contentBox = document.querySelector('.content-box')
      if (contentBox) {
        contentBox.scrollTop = contentBox.scrollHeight
      }
    }

    // 辅助函数：显示需求数据
    const displayRequirements = (data) => {
      if (!data || !data.requirements || !Array.isArray(data.requirements) || data.requirements.length === 0) {
        console.log('没有需求数据可显示')
        return
      }

      console.log('显示需求数据:', data.requirements.length, '条需求')

      // 添加需求数据显示
      refinedContent.value += '<div style="margin: 15px 0; padding: 15px; border: 1px solid #67C23A; border-radius: 4px; background-color: #F0F9EB;">'
      refinedContent.value += '<h3 style="color: #67C23A; margin-top: 0;">分析结果</h3>'
      refinedContent.value += `<p>系统已完成需求分析，生成了 ${data.requirements.length} 条需求项：</p>`

      data.requirements.forEach((req, index) => {
        refinedContent.value += `<div style="margin-bottom: 20px; padding: 15px; border: 1px solid #dcdfe6; border-radius: 4px; background-color: #f9f9f9;">`
        refinedContent.value += `<h3 style="margin-top: 0; color: #409EFF; border-bottom: 1px solid #eee; padding-bottom: 8px;">${index + 1}. ${req.name || req.title || '需求项'}</h3>`

        // 显示描述 - 使用pre-wrap确保保留格式
        refinedContent.value += `<div style="margin-bottom: 10px;"><strong style="color: #606266;">描述:</strong> <div style="white-space: pre-wrap; margin-top: 5px;">${req.description}</div></div>`

        // 显示分类
        refinedContent.value += `<div style="margin-bottom: 10px;"><strong style="color: #606266;">分类:</strong> ${req.category || '功能需求'}</div>`

        // 显示模块
        if (req.module) {
          refinedContent.value += `<div style="margin-bottom: 10px;"><strong style="color: #606266;">模块:</strong> ${req.module}</div>`
        }

        // 显示验收标准 - 使用pre-wrap确保保留格式
        if (req.criteria) {
          refinedContent.value += `<div style="margin-bottom: 10px;"><strong style="color: #606266;">验收标准:</strong> <div style="white-space: pre-wrap; margin-top: 5px;">${req.criteria}</div></div>`
        }

        // 显示关键词
        if (req.keywords) {
          refinedContent.value += `<div style="margin-bottom: 10px;"><strong style="color: #606266;">关键词:</strong> ${req.keywords}</div>`
        }

        // 显示备注 - 使用pre-wrap确保保留格式
        if (req.remark) {
          refinedContent.value += `<div style="margin-bottom: 10px;"><strong style="color: #606266;">备注:</strong> <div style="white-space: pre-wrap; margin-top: 5px;">${req.remark}</div></div>`
        }

        // 显示其他字段 - 遍历对象的所有属性
        const standardFields = ['name', 'title', 'description', 'category', 'module', 'criteria', 'keywords', 'remark', 'project_id'];
        Object.entries(req).forEach(([key, value]) => {
          // 跳过已经显示的标准字段和空值
          if (!standardFields.includes(key) && value !== null && value !== undefined && value !== '') {
            // 对于长文本，使用pre-wrap确保保留格式
            if (typeof value === 'string' && value.length > 100) {
              refinedContent.value += `<div style="margin-bottom: 10px;"><strong style="color: #606266;">${key}:</strong> <div style="white-space: pre-wrap; margin-top: 5px;">${value}</div></div>`
            } else {
              refinedContent.value += `<div style="margin-bottom: 10px;"><strong style="color: #606266;">${key}:</strong> ${value}</div>`
            }
          }
        });

        refinedContent.value += `</div>`
      })

      refinedContent.value += '<p style="margin-top: 15px; font-weight: bold;">点击"保存需求"按钮将这些需求保存到数据库。</p>'
      refinedContent.value += '</div>'

      // 显示原始数据（调试用）
      console.log('需求数据完整内容:', JSON.stringify(data, null, 2))

      // 显示保存按钮
      showSaveButton.value = true
    }
  } catch (error) {
    console.error('表单验证失败:', error)
    analyzing.value = false
  }
}

// 测试WebSocket连接
const testWebSocket = () => {
  // 关闭现有连接
  if (ws && (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING)) {
    ws.close()
  }

  // 使用相对路径，让代理服务器处理WebSocket连接
  const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws-test`;
  console.log('测试WebSocket URL:', wsUrl);

  // 创建WebSocket连接到后端测试端点
  const testWs = new WebSocket(wsUrl)

  testWs.onopen = () => {
    console.log('测试WebSocket连接已打开')
    ElMessage.success('WebSocket测试连接已建立')

    // 显示更详细的连接信息
    console.log(`成功连接到: ${wsUrl}`)
    alert(`WebSocket连接成功！\n\n已连接到: ${wsUrl}\n\n这表明后端服务器正在运行，并且WebSocket配置正确。`)

    // 发送测试消息
    testWs.send('Hello from frontend')
  }

  testWs.onmessage = (event) => {
    console.log('收到测试WebSocket消息:', event.data)
    ElMessage.success(`WebSocket连接测试成功! 收到服务器消息: ${event.data}`)

    // 收到消息后关闭连接
    setTimeout(() => {
      testWs.close()
    }, 2000)
  }

  testWs.onerror = (error) => {
    console.error('测试WebSocket错误:', error)
    ElMessage.error('WebSocket测试连接失败，请确认后端服务器正在运行')

    // 显示更详细的错误信息
    alert(`WebSocket连接失败！\n\n尝试连接到: ${wsUrl}\n\n请确认：\n1. 后端服务器正在运行\n2. 没有防火墙阻止WebSocket连接\n3. 后端CORS配置允许WebSocket连接\n\n请检查后端服务器日志以获取更多信息。`)

    // 尝试通过HTTP请求检查后端服务器是否可访问
    axios.get('/')
      .then(() => {
        console.log('后端服务器可以通过HTTP访问')
        ElMessage.info('后端服务器可以通过HTTP访问，但WebSocket连接失败')
      })
      .catch(error => {
        console.error('后端服务器HTTP请求错误:', error)
        const statusCode = error.response?.status || '未知'
        ElMessage.error(`后端服务器HTTP请求失败: ${statusCode}`)
      })
  }

  testWs.onclose = (event) => {
    console.log(`测试WebSocket连接已关闭，代码: ${event.code}`)

    // 如果是异常关闭，显示更多信息
    if (event.code === 1006) {
      ElMessage.error('WebSocket连接异常关闭，请确认后端服务器正在运行')
    }
  }
}

onMounted(() => {
  fetchProjects()
})
</script>

<style scoped>
.requirement-analysis-container {
  padding: 20px;
}

.analysis-form {
  margin-bottom: 20px;
}

.content-box {
  min-height: 300px;
  max-height: 600px; /* 增加高度，显示更多内容 */
  overflow-y: auto;
  padding: 10px;
  word-break: break-word;
  line-height: 1.5; /* 增加行高，提高可读性 */
}

/* 确保pre-wrap元素正确显示 */
.content-box :deep([style*="white-space: pre-wrap"]) {
  font-family: inherit;
  background-color: #f9f9f9;
  padding: 8px;
  border-radius: 4px;
  margin-top: 5px !important;
  line-height: 1.5;
}

.action-buttons {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 20px;
}

.analysis-status {
  margin-bottom: 15px;
}

:deep(.el-alert) {
  padding: 12px 15px;
}

:deep(.el-alert__title) {
  font-size: 16px;
  font-weight: bold;
}

:deep(.el-alert__description) {
  font-size: 14px;
  margin-top: 5px;
}

:deep(.markdown-body) {
  font-family: inherit;
}

:deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 15px;
}

:deep(th), :deep(td) {
  border: 1px solid #dcdfe6;
  padding: 8px 12px;
  text-align: left;
}

:deep(th) {
  background-color: #f5f7fa;
}
</style>
