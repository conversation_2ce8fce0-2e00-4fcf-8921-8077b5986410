<template>
  <div class="user-management-container">
    <div class="page-header">
      <div class="page-title">用户管理</div>
      <div class="page-actions">
        <el-button type="primary" @click="openUserDialog()">新建用户</el-button>
      </div>
    </div>

    <el-card>
      <el-table
        v-loading="loading"
        :data="userList"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="用户名" min-width="150" />
        <el-table-column label="创建时间" width="120">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <div class="table-actions">
              <el-button
                size="small"
                type="primary"
                @click="openUserDialog(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 用户对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑用户' : '新建用户'"
      width="500px"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" placeholder="请输入用户名" />
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input
            v-model="userForm.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>

        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="userForm.confirmPassword"
            type="password"
            placeholder="请再次输入密码"
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="submitting" @click="submitUserForm">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'

// 日期格式化函数
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toISOString().split('T')[0]; // 返回 YYYY-MM-DD 格式
}

// 用户列表
const userList = ref([])
const loading = ref(false)

// 对话框
const dialogVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)
const userFormRef = ref(null)

// 用户表单
const userForm = reactive({
  id: null,
  username: '',
  password: '',
  confirmPassword: ''
})

// 表单验证规则
const validateConfirmPassword = (_rule, value, callback) => {
  if (value !== userForm.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

const userRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 获取用户列表
const fetchUsers = async () => {
  loading.value = true
  try {
    const response = await axios.get('/api/users')
    userList.value = response.data
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 打开用户对话框
const openUserDialog = (user = null) => {
  if (user) {
    isEdit.value = true
    userForm.id = user.id
    userForm.username = user.username
    userForm.password = ''
    userForm.confirmPassword = ''
  } else {
    isEdit.value = false
    userForm.id = null
    userForm.username = ''
    userForm.password = ''
    userForm.confirmPassword = ''
  }

  dialogVisible.value = true
}

// 提交用户表单
const submitUserForm = async () => {
  if (!userFormRef.value) return

  try {
    await userFormRef.value.validate()

    submitting.value = true

    if (isEdit.value) {
      // 编辑用户
      await axios.put(`/api/users/${userForm.id}`, {
        username: userForm.username,
        password: userForm.password || undefined
      })

      ElMessage.success('更新用户成功')
    } else {
      // 创建用户
      await axios.post('/api/users', {
        username: userForm.username,
        password: userForm.password
      })

      ElMessage.success('创建用户成功')
    }

    dialogVisible.value = false
    fetchUsers()
  } catch (error) {
    console.error('操作用户失败:', error)
    ElMessage.error(error.response?.data?.detail || '操作用户失败')
  } finally {
    submitting.value = false
  }
}

// 删除用户
const handleDelete = (user) => {
  ElMessageBox.confirm(
    `确定要删除用户 "${user.username}" 吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
    .then(async () => {
      try {
        await axios.delete(`/api/users/${user.id}`)
        ElMessage.success('删除成功')
        fetchUsers()
      } catch (error) {
        console.error('删除用户失败:', error)
        ElMessage.error('删除用户失败')
      }
    })
    .catch(() => {
      // 用户取消删除
    })
}

onMounted(() => {
  fetchUsers()
})
</script>

<style scoped>
.user-management-container {
  padding: 20px 15px; /* 上下保持20px，左右减小到2px */
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.page-title {
  font-size: 20px;
  font-weight: bold;
}

.page-actions {
  display: flex;
  gap: 10px;
}

.el-card {
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.el-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

:deep(.el-card__body) {
  padding: 10px;
}

:deep(.el-table__header th) {
  font-weight: bold;
  text-align: center !important;
}

:deep(.table-actions) {
  display: flex;
  flex-wrap: nowrap;
  justify-content: center;
  gap: 10px;
}

:deep(.el-table__header th .cell) {
  text-align: center;
}
</style>
