<template>
  <div class="testcase-generate-container">
    <el-card class="selection-card">
      <div class="card-title">选择需求</div>
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="项目">
          <el-select v-model="searchForm.project_id" placeholder="请选择项目" style="width: 180px;" clearable @change="handleProjectChange">
            <el-option
              v-for="item in projectList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="需求标题">
          <el-input v-model="searchForm.title" placeholder="请输入需求标题" clearable />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        v-loading="loading"
        :data="requirementList"
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="title" label="需求标题" min-width="150" show-overflow-tooltip />
        <el-table-column prop="project.name" label="所属项目" min-width="120" show-overflow-tooltip />
        <el-table-column prop="category" label="类别" width="100" />
        <el-table-column prop="reviewer" label="评审人" width="120" />

        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button
              size="small"
              @click="handleViewRequirement(scope.row)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 添加分页控件 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <div class="action-buttons">
        <el-button
          type="primary"
          :disabled="selectedRequirements.length === 0 || generating"
          @click="startGeneration"
        >
          开始生成测试用例
        </el-button>
        <el-button
          type="info"
          @click="testWebSocketConnection"
        >
          测试WebSocket连接
        </el-button>
      </div>
    </el-card>

    <div class="generation-result" v-if="showResult">
      <el-card class="result-card">
        <template #header>
          <div class="card-header">
            <span>测试用例生成结果</span>
          </div>
        </template>
        <div class="content-box" v-loading="generating">
          <div v-html="generatedContent"></div>
        </div>

        <div class="action-buttons">
          <el-button
            type="success"
            :disabled="!generatedContent || generating"
            @click="saveTestCases"
          >
            保存测试用例
          </el-button>
          <el-button
            type="warning"
            :disabled="!generatedContent || generating"
            @click="regenerateTestCases"
          >
            重新生成
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 需求详情对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="需求详情"
      width="80%"
    >
      <div class="requirement-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="需求标题" :span="2">{{ currentRequirement.title }}</el-descriptions-item>
          <el-descriptions-item label="所属项目">{{ currentRequirement.project?.name }}</el-descriptions-item>
          <el-descriptions-item label="类别">{{ currentRequirement.category }}</el-descriptions-item>
          <el-descriptions-item label="评审人">{{ currentRequirement.reviewer }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(currentRequirement.created_at) }}</el-descriptions-item>
        </el-descriptions>

        <el-tabs v-model="activeTab" class="requirement-tabs">
          <el-tab-pane label="需求描述" name="description">
            <div class="content-box">{{ currentRequirement.description }}</div>
          </el-tab-pane>
          <el-tab-pane label="原始需求" name="original">
            <div class="content-box">{{ currentRequirement.original_content }}</div>
          </el-tab-pane>
          <el-tab-pane label="AI分析结果" name="refined">
            <div class="content-box" v-html="currentRequirement.refined_content"></div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import axios from 'axios'
import { ElMessage, ElLoading } from 'element-plus'

// 日期格式化函数
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toISOString().split('T')[0]; // 返回 YYYY-MM-DD 格式
}

const router = useRouter()
const route = useRoute()

// 搜索表单
const searchForm = reactive({
  project_id: '',
  title: ''
})

// 项目和需求列表
const projectList = ref([])
const requirementList = ref([])
const loading = ref(false)

// 分页参数
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 选中的需求
const selectedRequirements = ref([])

// 生成状态
const generating = ref(false)
const showResult = ref(false)
const generatedContent = ref('')
// 存储生成的测试用例数据
const generatedTestcases = ref([])

// WebSocket连接
let ws = null

// 需求详情对话框
const dialogVisible = ref(false)
const currentRequirement = ref({})
const activeTab = ref('description')

// 获取项目列表
const fetchProjects = async () => {
  try {
    const response = await axios.get('/api/projects')
    projectList.value = response.data
  } catch (error) {
    console.error('获取项目列表失败:', error)
    ElMessage.error('获取项目列表失败')
  }
}

// 获取需求列表
const fetchRequirements = async () => {
  loading.value = true
  try {
    // 添加分页参数
    const params = {
      skip: (currentPage.value - 1) * pageSize.value,
      limit: pageSize.value,
      project_id: searchForm.project_id || undefined,
      title: searchForm.title || undefined
    }

    console.log('发送请求参数:', params)

    const response = await axios.get('/api/requirements', { params })
    console.log('获取到的需求数据:', response.data)

    // 从响应头中获取总数
    const totalCount = response.headers['x-total-count']
    console.log('从响应头获取的总数:', totalCount)

    requirementList.value = response.data

    // 设置总数，如果响应头中有总数，则使用响应头中的总数，否则使用响应数据的长度
    total.value = totalCount ? parseInt(totalCount) : response.data.length

    console.log(`获取到 ${response.data.length} 条需求数据，总数: ${total.value}`)

    // 如果URL中有需求ID参数，自动选中该需求
    if (route.query.requirement_id) {
      const requirementId = parseInt(route.query.requirement_id)
      const requirement = response.data.find(item => item.id === requirementId)
      if (requirement) {
        selectedRequirements.value = [requirement]
      } else {
        // 如果当前页面没有找到指定的需求，尝试从服务器获取该需求
        try {
          const singleResponse = await axios.get(`/api/requirements/${requirementId}`)
          if (singleResponse.data) {
            // 将该需求添加到列表中并选中
            requirementList.value.unshift(singleResponse.data)
            selectedRequirements.value = [singleResponse.data]
          }
        } catch (err) {
          console.error(`获取指定需求(ID=${requirementId})失败:`, err)
        }
      }
    }

    // 如果没有数据，显示提示消息
    if (response.data.length === 0) {
      ElMessage.info('没有找到符合条件的需求')
    }
  } catch (error) {
    console.error('获取需求列表失败:', error)
    ElMessage.error('获取需求列表失败')
    requirementList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 项目变更
const handleProjectChange = () => {
  currentPage.value = 1 // 重置为第一页
  fetchRequirements()
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1 // 重置为第一页
  fetchRequirements()
}

// 重置搜索
const resetSearch = () => {
  searchForm.project_id = ''
  searchForm.title = ''
  currentPage.value = 1 // 重置为第一页
  fetchRequirements()
}

// 分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchRequirements()
}

// 当前页变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchRequirements()
}

// 选择变更
const handleSelectionChange = (selection) => {
  selectedRequirements.value = selection
}

// 查看需求详情
const handleViewRequirement = (row) => {
  currentRequirement.value = row
  dialogVisible.value = true
  activeTab.value = 'description'
}

// 开始生成测试用例
const startGeneration = () => {
  if (selectedRequirements.value.length === 0) {
    ElMessage.warning('请至少选择一个需求')
    return
  }

  generating.value = true
  showResult.value = true
  generatedContent.value = ''
  generatedTestcases.value = []

  console.log('使用WebSocket进行测试用例生成')

  // 关闭现有连接
  if (ws && (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING)) {
    ws.close()
  }

  // 连接到后端WebSocket端点
  // 使用相对路径，让代理服务器处理WebSocket连接
  const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/api/testcases/generate`
  console.log('连接到WebSocket URL:', wsUrl)

  // 备用直连URL（如果代理不工作）
  const directUrl = 'ws://localhost:8000/api/testcases/generate'
  console.log('备用直连URL:', directUrl)

  // 创建新的WebSocket连接
  try {
    console.log('尝试通过代理连接WebSocket...')
    ws = new WebSocket(wsUrl)
  } catch (error) {
    console.error('通过代理连接WebSocket失败，尝试直连:', error)
    ElMessage.warning('通过代理连接WebSocket失败，尝试直连...')
    ws = new WebSocket(directUrl)
  }

  // 连接打开时发送请求数据
  ws.onopen = () => {
    console.log('WebSocket连接已打开，准备发送生成请求')
    ElMessage.success('WebSocket连接已建立，开始生成测试用例')

    // 获取第一个选中的需求
    const requirement = selectedRequirements.value[0]

    // 发送生成请求
    ws.send(JSON.stringify({
      id: requirement.id,
      project_id: requirement.project_id,
      title: requirement.title,
      description: requirement.description,
      reviewer: requirement.reviewer,
      scenario: requirement.refined_content || requirement.description,
      task: "根据需求生成测试用例"
    }))
  }

  // WebSocket消息处理

  ws.onmessage = (event) => {
    console.log('收到WebSocket消息:', event.data)

    try {
      const data = JSON.parse(event.data)
      console.log('解析后的消息数据:', data)

      // 处理所有类型的消息
      if (data.source) {
        // 根据source设置不同的颜色
        let color = '#000000'
        if (data.source === 'system') color = '#409EFF'
        else if (data.source === 'error') color = '#F56C6C'
        else if (data.source === 'warning') color = '#E6A23C'
        else if (data.source === '数据库智能体') color = '#67C23A'
        else if (data.source === 'generated_testcases') {
          // 处理生成的测试用例数据
          try {
            // 解析测试用例数据并存储
            generatedTestcases.value = JSON.parse(data.content)
            console.log('成功解析测试用例数据:', generatedTestcases.value)

            // 显示测试用例数据摘要
            const content = `已生成 ${generatedTestcases.value.length} 条测试用例，请点击"保存测试用例"按钮将其保存到数据库。`
            generatedContent.value += `<div style="color: #67C23A"><strong>测试用例生成:</strong> ${content}</div><br>`

            // 显示测试用例列表
            generatedContent.value += `<div style="margin-top: 10px; margin-bottom: 10px;"><strong>生成的测试用例列表:</strong></div>`
            generatedTestcases.value.forEach((tc, index) => {
              generatedContent.value += `<div style="margin-left: 20px; margin-bottom: 5px;"><strong>${index + 1}. ${tc.title}</strong> - ${tc.description}</div>`
            })
            generatedContent.value += `<br>`

            return // 跳过默认的内容处理
          } catch (e) {
            console.error('解析测试用例数据失败:', e)
            generatedContent.value += `<div style="color: #F56C6C"><strong>错误:</strong> 解析测试用例数据失败</div><br>`
          }
        }

        // 将内容转换为HTML并添加到结果中
        const content = data.content.replace(/\n/g, '<br>')
        generatedContent.value += `<div style="color: ${color}"><strong>${data.source}:</strong> ${content}</div><br>`
      } else {
        // 处理没有source字段的消息
        const content = data.content ? data.content.replace(/\n/g, '<br>') : JSON.stringify(data)
        generatedContent.value += `${content}<br>`
      }

      // 如果是最终消息，结束生成状态
      if (data.is_final) {
        console.log('收到最终消息，生成完成')
        generatedContent.value += '<br><strong>测试用例生成完成</strong><br>'
        ElMessage.success('测试用例生成完成')
        generating.value = false
        ws.close()
      }

      // 自动滚动到底部
      const contentBox = document.querySelector('.content-box')
      if (contentBox) {
        contentBox.scrollTop = contentBox.scrollHeight
      }
    } catch (error) {
      console.error('解析WebSocket消息失败:', error, event.data)
      ElMessage.error('解析服务器消息失败')

      // 尝试直接显示原始消息
      generatedContent.value += `<div style="color: #F56C6C">解析消息失败: ${event.data}</div><br>`
    }
  }

  ws.onerror = (error) => {
    console.error('WebSocket错误:', error)

    // 显示更详细的错误信息
    generatedContent.value += `<div style="color: #F56C6C; margin: 10px 0; padding: 10px; border: 1px solid #F56C6C; border-radius: 4px; background-color: #FEF0F0;">
      <strong>WebSocket连接错误</strong><br>
      <p>可能的原因：</p>
      <ul>
        <li>后端服务器未运行或未在正确的端口上运行</li>
        <li>WebSocket端点路径不正确</li>
        <li>网络连接问题</li>
        <li>CORS或代理配置问题</li>
      </ul>
      <p>请检查：</p>
      <ul>
        <li>后端服务器是否在端口8000上运行</li>
        <li>后端是否正确实现了WebSocket端点</li>
        <li>网络连接是否正常</li>
      </ul>
    </div>`

    ElMessage.error('生成过程中发生错误，请检查网络连接或服务器状态')
    generating.value = false

    // 尝试通过HTTP请求检查后端服务器是否可访问
    axios.get('/api/projects')
      .then(() => {
        console.log('后端服务器可以通过HTTP访问，但WebSocket连接失败')
        generatedContent.value += `<div style="color: #E6A23C; margin-top: 10px;">
          后端服务器可以通过HTTP访问，但WebSocket连接失败。这可能是WebSocket端点配置问题。
        </div>`
      })
      .catch(error => {
        console.error('后端服务器HTTP请求错误:', error)
        generatedContent.value += `<div style="color: #F56C6C; margin-top: 10px;">
          后端服务器HTTP请求也失败，状态码: ${error.response?.status || '未知'}。这可能是后端服务器未运行或网络问题。
        </div>`
      })
  }

  ws.onclose = (event) => {
    console.log(`WebSocket连接已关闭，代码: ${event.code}, 原因: ${event.reason}`)

    // 显示关闭代码的含义
    let closeReason = '';
    switch (event.code) {
      case 1000:
        closeReason = '正常关闭';
        break;
      case 1001:
        closeReason = '终端离开';
        break;
      case 1002:
        closeReason = '协议错误';
        break;
      case 1003:
        closeReason = '接收到不可接受的数据';
        break;
      case 1005:
        closeReason = '没有提供状态码';
        break;
      case 1006:
        closeReason = '异常关闭，可能是网络问题或服务器未运行';
        break;
      case 1007:
        closeReason = '接收到的数据帧格式不符合协议';
        break;
      case 1008:
        closeReason = '策略违规';
        break;
      case 1009:
        closeReason = '消息太大';
        break;
      case 1010:
        closeReason = '客户端期望服务器协商一个或多个扩展，但服务器没有返回它们';
        break;
      case 1011:
        closeReason = '服务器遇到了阻止其完成请求的意外情况';
        break;
      case 1015:
        closeReason = 'TLS握手失败';
        break;
      default:
        closeReason = '未知原因';
    }

    if (generating.value) {
      generating.value = false

      // 添加关闭信息到生成结果
      generatedContent.value += `<div style="color: #E6A23C; margin: 10px 0; padding: 10px; border: 1px solid #E6A23C; border-radius: 4px; background-color: #FDF6EC;">
        <strong>WebSocket连接已关闭</strong><br>
        <p>关闭代码: ${event.code} (${closeReason})</p>
        <p>关闭原因: ${event.reason || '未提供'}</p>
        <p>生成过程可能未完成，请检查后端服务器状态或重试。</p>
      </div>`

      ElMessage.warning(`WebSocket连接意外关闭 (${closeReason})，生成可能未完成`)
    }
  }
}

// 保存测试用例
const saveTestCases = async () => {
  try {
    // 检查是否有生成的测试用例数据
    if (!generatedTestcases.value || generatedTestcases.value.length === 0) {
      ElMessage.warning('没有可保存的测试用例数据，请先生成测试用例')
      return
    }

    // 显示加载提示
    const loadingInstance = ElLoading.service({
      fullscreen: true,
      text: '正在保存测试用例数据到数据库...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    try {
      // 调用后端API保存测试用例
      console.log('准备保存测试用例数据:', generatedTestcases.value)

      // 确保测试步骤格式正确
      const formattedTestcases = generatedTestcases.value.map(testcase => {
        // 确保test_steps是正确的格式
        const formattedTestSteps = testcase.test_steps.map(step => {
          // 如果step已经是对象且有description和expected_result属性，直接返回
          if (typeof step === 'object' && step.description && step.expected_result) {
            return step
          }
          // 如果step是字符串或其他格式，尝试转换
          return {
            description: step.description || step,
            expected_result: step.expected_result || ''
          }
        })

        // 确保preconditions和postconditions是数组
        const preconditions = Array.isArray(testcase.preconditions) ? testcase.preconditions : []
        const postconditions = Array.isArray(testcase.postconditions) ? testcase.postconditions : []

        // 返回格式化后的测试用例
        return {
          ...testcase,
          test_steps: formattedTestSteps,
          preconditions: preconditions,
          postconditions: postconditions,
          // 确保其他必填字段存在
          status: testcase.status || '未开始',
          creator: testcase.creator || 'admin'
        }
      })

      console.log('格式化后的测试用例数据:', formattedTestcases)

      // 发送请求到后端API
      console.log('发送请求到后端API: /api/testcases/save-generated')
      const response = await axios.post('/api/testcases/save-generated', formattedTestcases)

      // 处理响应
      console.log('保存测试用例响应:', response.data)

      // 显示成功消息
      ElMessage.success(`测试用例已成功保存到数据库，共 ${response.data.length} 条`)

      // 跳转到测试用例列表页面
      router.push('/testcase/list')
    } finally {
      // 关闭加载提示
      loadingInstance.close()
    }
  } catch (error) {
    console.error('保存测试用例失败:', error)
    ElMessage.error(`保存测试用例失败: ${error.response?.data?.detail || error.message}`)
  }
}

// 重新生成测试用例
const regenerateTestCases = () => {
  if (ws && ws.readyState === WebSocket.OPEN) {
    ws.close()
  }

  generatedContent.value = ''
  generatedTestcases.value = []
  startGeneration()
}

// 测试WebSocket连接
const testWebSocketConnection = () => {
  // 显示测试结果区域
  showResult.value = true
  generatedContent.value = '<div style="color: #409EFF; font-weight: bold;">开始测试WebSocket连接...</div><br>'

  // 关闭现有连接
  if (ws && (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING)) {
    ws.close()
  }

  // 使用相对路径，让代理服务器处理WebSocket连接
  const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws-test`
  const directUrl = 'ws://localhost:8000/ws-test'

  generatedContent.value += `<div>尝试连接到WebSocket测试端点: ${wsUrl}</div><br>`
  generatedContent.value += `<div>备用直连URL: ${directUrl}</div><br>`

  // 创建WebSocket连接
  try {
    console.log('尝试通过代理连接WebSocket测试端点...')
    const testWs = new WebSocket(wsUrl)

    testWs.onopen = () => {
      console.log('WebSocket测试连接已打开')
      generatedContent.value += '<div style="color: #67C23A; font-weight: bold;">WebSocket测试连接成功！</div><br>'

      // 发送测试消息
      testWs.send('测试消息')
      generatedContent.value += '<div>已发送测试消息...</div><br>'
    }

    testWs.onmessage = (event) => {
      console.log('收到WebSocket测试消息:', event.data)
      generatedContent.value += `<div style="color: #67C23A">收到服务器响应: ${event.data}</div><br>`
      generatedContent.value += '<div style="color: #67C23A; font-weight: bold;">WebSocket连接测试成功！</div><br>'
      generatedContent.value += '<div>这表明WebSocket配置正常，可以尝试生成测试用例。</div><br>'

      // 测试成功后关闭连接
      setTimeout(() => {
        testWs.close()
      }, 2000)
    }

    testWs.onerror = (error) => {
      console.error('WebSocket测试连接错误:', error)
      generatedContent.value += '<div style="color: #F56C6C; font-weight: bold;">WebSocket测试连接失败！</div><br>'
      generatedContent.value += `<div style="color: #F56C6C">错误信息: ${error.message || '未知错误'}</div><br>`
      generatedContent.value += '<div>尝试使用直连URL...</div><br>'

      // 尝试直连
      try {
        const directWs = new WebSocket(directUrl)

        directWs.onopen = () => {
          console.log('直连WebSocket测试成功')
          generatedContent.value += '<div style="color: #67C23A; font-weight: bold;">直连WebSocket测试成功！</div><br>'
          generatedContent.value += '<div>这表明后端服务器正在运行，但代理配置可能有问题。</div><br>'

          // 发送测试消息
          directWs.send('直连测试消息')
        }

        directWs.onmessage = (event) => {
          console.log('收到直连WebSocket测试消息:', event.data)
          generatedContent.value += `<div style="color: #67C23A">收到服务器响应: ${event.data}</div><br>`

          // 测试成功后关闭连接
          setTimeout(() => {
            directWs.close()
          }, 2000)
        }

        directWs.onerror = (directError) => {
          console.error('直连WebSocket测试失败:', directError)
          generatedContent.value += '<div style="color: #F56C6C; font-weight: bold;">直连WebSocket测试也失败！</div><br>'
          generatedContent.value += '<div>请检查后端服务器是否正在运行，以及WebSocket端点是否正确实现。</div><br>'

          // 检查HTTP连接
          checkHttpConnection()
        }
      } catch (directConnectError) {
        console.error('创建直连WebSocket失败:', directConnectError)
        generatedContent.value += '<div style="color: #F56C6C; font-weight: bold;">创建直连WebSocket失败！</div><br>'
        generatedContent.value += '<div>请检查后端服务器是否正在运行。</div><br>'

        // 检查HTTP连接
        checkHttpConnection()
      }
    }

    testWs.onclose = (event) => {
      console.log(`WebSocket测试连接已关闭，代码: ${event.code}, 原因: ${event.reason || '未提供'}`)
      generatedContent.value += `<div>WebSocket测试连接已关闭，代码: ${event.code}, 原因: ${event.reason || '未提供'}</div><br>`
    }
  } catch (error) {
    console.error('创建WebSocket测试连接失败:', error)
    generatedContent.value += '<div style="color: #F56C6C; font-weight: bold;">创建WebSocket测试连接失败！</div><br>'
    generatedContent.value += `<div style="color: #F56C6C">错误信息: ${error.message || '未知错误'}</div><br>`

    // 检查HTTP连接
    checkHttpConnection()
  }
}

// 检查HTTP连接
const checkHttpConnection = () => {
  generatedContent.value += '<div>正在检查HTTP连接...</div><br>'

  axios.get('/api/projects')
    .then(response => {
      console.log('HTTP连接成功:', response.data)
      generatedContent.value += '<div style="color: #67C23A; font-weight: bold;">HTTP连接测试成功！</div><br>'
      generatedContent.value += '<div>这表明后端服务器正在运行，但WebSocket配置可能有问题。</div><br>'
      generatedContent.value += '<div>请检查后端服务器的WebSocket端点是否正确实现。</div><br>'
    })
    .catch(error => {
      console.error('HTTP连接失败:', error)
      generatedContent.value += '<div style="color: #F56C6C; font-weight: bold;">HTTP连接测试也失败！</div><br>'
      generatedContent.value += `<div style="color: #F56C6C">错误信息: ${error.message || '未知错误'}</div><br>`
      generatedContent.value += '<div>这表明后端服务器可能未运行或网络连接有问题。</div><br>'
      generatedContent.value += '<div>请确保后端服务器正在运行，并且端口配置正确。</div><br>'
    })
}

onMounted(() => {
  fetchProjects()
  fetchRequirements()
})
</script>

<style scoped>
.testcase-generate-container {
  padding: 10px 15px; /* 上下保持20px，左右减小到2px */
}

.el-card {
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.el-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}

.selection-card {
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 20px;
  background-color: #f2f8ea;
}

.action-buttons {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 20px;
}

.content-box {
  min-height: 300px;
  max-height: 500px;
  overflow-y: auto;
  padding: 10px;
  white-space: pre-wrap;
  word-break: break-word;
}

.requirement-detail {
  margin-bottom: 20px;
}

.requirement-tabs {
  margin-top: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
</style>
